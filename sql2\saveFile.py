import pandas as pd
import tkinter as tk
from tkinter import filedialog, messagebox
import duckdb
import os
# 创建示例 DataFrame

    
# 定义保存 Excel 的函数
def save_to_excel():
    # 打开文件选择对话框
    path=os.path.dirname(os.path.abspath(__file__))
    print(path)
    with open(path+"\凭证查询2.sql","r",encoding="UTF-8") as f:
        content=f.read()
    con=duckdb.connect(r"D:\infoTech\fip-rpa-robot-oldscriptref\data\InternalData\Duckdb\example.duckdb")
    df=con.sql(content).df()
    file_path = filedialog.asksaveasfilename(
        defaultextension='.xlsx',  # 默认扩展名
        filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],  # 文件类型
        title="保存文件为"
    )

    
    # 如果用户选择了文件路径，则保存 DataFrame
    if file_path:
        try:
            df.to_excel(file_path, index=False, engine='openpyxl')
            messagebox.showinfo("成功", f"数据已保存到 {file_path}")
        except Exception as e:
            messagebox.showerror("错误", f"保存文件时发生错误: {str(e)}")
    con.close()
if __name__ == "__main__":
    save_to_excel()
