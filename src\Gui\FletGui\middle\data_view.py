import flet as ft
import src.Gui.callProcess as callF
import src.base.cache as cache
import src.base.settings as settings
import os
from datetime import datetime


class changeData(ft.Row):
    def __init__(self):
        super().__init__()
        self.visible = True
        self.expand = True
        self.commbox=ft.Dropdown(
            width=200,
            options=[
                ft.dropdown.Option("科目对照"),
                ft.dropdown.Option("主数据"),
                ft.dropdown.Option("付款台账"),
                ft.dropdown.Option("收款台账"),
                ft.dropdown.Option("分供结算台账"),
                ft.dropdown.Option("异常数据"),
            ],
            border_radius=10,
            filled=True,
            bgcolor=ft.colors.SURFACE_VARIANT,
            hint_text="请选择数据库",
        )
        #self.spacing=5
        self.controls=[
            ft.Container(
                content=self.commbox,
                padding=10,
                border_radius=10,
                bgcolor=ft.colors.SURFACE_VARIANT,
            ),
            ft.Container(
                content=ft.ElevatedButton(
                    text="更新数据库结构",
                    icon=ft.icons.BUILD,
                    style=ft.ButtonStyle(
                        shape=ft.RoundedRectangleBorder(radius=10),
                        padding=15,
                    ),
                    on_click=lambda e:callF.thisProcess.run({"功能":"创建数据库"})
                ),
                padding=10,
                alignment=ft.alignment.center,
            ),
            ft.Container(
                content=ft.ElevatedButton(
                    text="查询数据库",
                    icon=ft.icons.SEARCH,
                    style=ft.ButtonStyle(
                        shape=ft.RoundedRectangleBorder(radius=10),
                        padding=15,
                    ),
                    on_click=lambda e:callF.thisProcess.run({"功能":"查询数据库","参数":[self.commbox.value]})
                ),
                padding=10,
                alignment=ft.alignment.center,
            ),
            ft.Container(
                content=ft.ElevatedButton(
                    text="更新数据表",
                    icon=ft.icons.UPDATE,
                    style=ft.ButtonStyle(
                        shape=ft.RoundedRectangleBorder(radius=10),
                        padding=15,
                    ),
                    on_click=lambda e:callF.thisProcess.run({"功能":"更新数据库","参数":[self.commbox.value]})
                ),
                padding=10,
                alignment=ft.alignment.center,
            ),
        ]

class JiuQiReport(ft.Column):
    def __init__(self):
        super().__init__()
        self.visible = True
        self.expand = True
        
        # 创建上年度报表路径选择器
        self.input_path = ft.TextField(
            label="选择输入文件",
            read_only=True,
            width=400,
            value='',
            border_radius=10,
            border_color=ft.colors.OUTLINE_VARIANT,
            focused_border_color=ft.colors.PRIMARY,
            text_style=ft.TextStyle(size=14),
        )
        
        self.input_picker = ft.FilePicker(
            on_result=self.pick_input_file
        )
        
        self.input_button = ft.ElevatedButton(
            "选择文件",
            icon=ft.icons.FOLDER_OPEN,
            on_click=lambda _: self.input_picker.pick_files(),
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
                text_style=ft.TextStyle(size=14),
            ),
        )
        
        # 创建本年度报表路径选择器
        self.config_path = ft.TextField(
            label="选择参数表",
            read_only=True,
            width=400,
            value='',
            border_radius=10,
            border_color=ft.colors.OUTLINE_VARIANT,
            focused_border_color=ft.colors.PRIMARY,
            text_style=ft.TextStyle(size=14),
        )
        
        self.config_picker = ft.FilePicker(
            on_result=self.pick_config_file
        )
        
        self.config_button = ft.ElevatedButton(
            "选择文件",
            icon=ft.icons.FOLDER_OPEN,
            on_click=lambda _: self.config_picker.pick_files(),
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
                text_style=ft.TextStyle(size=14),
            ),
        )
        
        self.commbox=ft.Dropdown(
            width=200,
            options=[
                ft.dropdown.Option("01-资产类"),
                ft.dropdown.Option("02-负债类"),
                ft.dropdown.Option("03-损益类"),
                ft.dropdown.Option("04-税务类"),
                ft.dropdown.Option("05-主表&权益类"),
                ft.dropdown.Option("06-关联方&分部报告"),
            ],
            border_radius=10,
            filled=True,
            bgcolor=ft.colors.SURFACE_VARIANT,
            hint_text="请选择分组",
        )
        # 创建功能按钮
        
        self.audit_button = ft.ElevatedButton(
            "执行审核",
            icon=ft.icons.VERIFIED_USER,
            on_click=lambda e: callF.thisProcess.run({"功能": "批量审核","参数":[self.input_path.value,self.config_path.value,self.commbox.value]}),
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
                text_style=ft.TextStyle(size=14),
            ),
        )
        
        self.controls = [
            ft.Container(
                content=ft.Column(
                    controls=[
                        ft.Text("久其审核自动化小工具", size=20, weight=ft.FontWeight.BOLD, color=ft.colors.PRIMARY),
                        ft.Row(
                            controls=[
                                self.input_path,
                                self.input_button,
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                        ),
                        ft.Row(
                            controls=[
                                self.config_path,
                                self.config_button,
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                        ),
                        ft.Row(
                            controls=[
                                self.commbox,
                                self.audit_button
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                        )
                    ],
                    spacing=10,
                ),
                padding=10,
                border_radius=7,
                bgcolor=ft.colors.SURFACE_VARIANT,
                margin=ft.margin.only(bottom=10),
            ),
            self.input_picker,
            self.config_picker,
        ]
    
    def pick_input_file(self, e: ft.FilePickerResultEvent):
        if e.files:
            self.input_path.value = e.files[0].path
            self.input_path.update()
    
    def pick_config_file(self, e: ft.FilePickerResultEvent):
        if e.files:
            self.config_path.value = e.files[0].path
            self.config_path.update()

class FinancialReportView(ft.Column):
    def __init__(self):
        super().__init__()
        self.visible = True
        self.expand = True
        
        # 创建上年度报表路径选择器
        self.last_year_path = ft.TextField(
            label="上年度报表路径",
            read_only=True,
            width=400,
            value=cache.LastYearFinancialReport,
            border_radius=10,
            border_color=ft.colors.OUTLINE_VARIANT,
            focused_border_color=ft.colors.PRIMARY,
            text_style=ft.TextStyle(size=14),
        )
        
        self.last_year_picker = ft.FilePicker(
            on_result=self.pick_last_year_file
        )
        
        self.last_year_button = ft.ElevatedButton(
            "选择文件",
            icon=ft.icons.FOLDER_OPEN,
            on_click=lambda _: self.last_year_picker.pick_files(),
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
                text_style=ft.TextStyle(size=14),
            ),
        )
        
        # 创建本年度报表路径选择器
        self.current_year_path = ft.TextField(
            label="本年度报表路径",
            read_only=True,
            width=400,
            value=cache.CurrentYearFinancialReport,
            border_radius=10,
            border_color=ft.colors.OUTLINE_VARIANT,
            focused_border_color=ft.colors.PRIMARY,
            text_style=ft.TextStyle(size=14),
        )
        
        self.current_year_picker = ft.FilePicker(
            on_result=self.pick_current_year_file
        )
        
        self.current_year_button = ft.ElevatedButton(
            "选择文件",
            icon=ft.icons.FOLDER_OPEN,
            on_click=lambda _: self.current_year_picker.pick_files(),
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
                text_style=ft.TextStyle(size=14),
            ),
        )
        
        # 创建功能按钮
        self.fill_button = ft.ElevatedButton(
            "执行初期数补全",
            icon=ft.icons.AUTOFPS_SELECT,
            on_click=lambda e: callF.thisProcess.run({"功能": "补全期初数"}),
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
                text_style=ft.TextStyle(size=14),
            ),
        )
        
        self.audit_button = ft.ElevatedButton(
            "执行批量审核",
            icon=ft.icons.VERIFIED_USER,
            on_click=lambda e: callF.thisProcess.run({"功能": "批量审核"}),
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
                text_style=ft.TextStyle(size=14),
            ),
        )
        
        self.controls = [
            ft.Container(
                content=ft.Column(
                    controls=[
                        ft.Text("财报助手", size=20, weight=ft.FontWeight.BOLD, color=ft.colors.PRIMARY),
                        ft.Row(
                            controls=[
                                self.last_year_path,
                                self.last_year_button,
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                        ),
                        ft.Row(
                            controls=[
                                self.current_year_path,
                                self.current_year_button,
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                        ),
                        ft.Row(
                            controls=[
                                self.fill_button,
                                #self.audit_button,
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_AROUND,
                        ),
                    ],
                    spacing=10,
                ),
                padding=10,
                border_radius=15,
                bgcolor=ft.colors.SURFACE_VARIANT,
                margin=ft.margin.only(bottom=10),
            ),
            self.last_year_picker,
            self.current_year_picker,
        ]
    
    def pick_last_year_file(self, e: ft.FilePickerResultEvent):
        if e.files:
            self.last_year_path.value = e.files[0].path
            cache.LastYearFinancialReport = e.files[0].path
            cache.wirteLastYearFinancialReport()
            self.last_year_path.update()
    
    def pick_current_year_file(self, e: ft.FilePickerResultEvent):
        if e.files:
            self.current_year_path.value = e.files[0].path
            cache.CurrentYearFinancialReport = e.files[0].path
            cache.wirteCurrentYearFinancialReport()
            self.current_year_path.update()

class dataTabView(ft.Column):
    def __init__(self):
        super().__init__()
        self.visible = True
        self.expand = True
        #self.spacing=5
        
        # 创建日期选择器
        self.startDate = ft.DatePicker(
            first_date=datetime(2020, 1, 1),
            last_date=datetime(2030, 12, 31),
            on_change=self.start_date_changed
        )
        self.endDate = ft.DatePicker(
            first_date=datetime(2020, 1, 1),
            last_date=datetime(2030, 12, 31),
            on_change=self.end_date_changed
        )
        
        # 创建日期显示按钮
        self.startDateButton = ft.ElevatedButton(
            text=cache.Lastdate,
            icon=ft.icons.CALENDAR_TODAY,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
            ),
            on_click=lambda _: self.startDate.pick_date()
        )
        
        self.endDateButton = ft.ElevatedButton(
            text=cache.Nowdate,
            icon=ft.icons.CALENDAR_TODAY,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
            ),
            on_click=lambda _: self.endDate.pick_date()
        )
        
        self.navigation_bar = ft.NavigationBar(
            destinations=[
                ft.NavigationBarDestination(icon=ft.icons.PAGES, label="数据整理"),
                ft.NavigationBarDestination(icon=ft.icons.ACCOUNT_BALANCE, label="科目余额表"),
                ft.NavigationBarDestination(icon=ft.icons.SUMMARIZE, label="财报助手"),
            ],
            on_change=lambda e:self.changeMain(e)
        )
        self.default=[ft.Container(
                    content=ft.Column(
                        controls=[
                            ft.Text("数据整理", size=20, weight=ft.FontWeight.BOLD, color=ft.colors.PRIMARY),
                            ft.Container(
                                content=ft.ElevatedButton(
                                    text="生成项目报表",
                                    icon=ft.icons.SUMMARIZE,
                                    style=ft.ButtonStyle(
                                        shape=ft.RoundedRectangleBorder(radius=10),
                                        padding=15,
                                    ),
                                    on_click=lambda e:callF.thisProcess.run({"功能":"生成各台账"})
                                ),
                                padding=10,
                                alignment=ft.alignment.center,
                            ),
                        ],
                        spacing=15,
                    ),
                    padding=20,
                    border_radius=15,
                    bgcolor=ft.colors.SURFACE_VARIANT,
                    margin=ft.margin.only(bottom=20),
                )

        ]
        self.mainRegional=ft.Column(controls=[],alignment=ft.MainAxisAlignment.CENTER)
        self.controls=(self.navigation_bar,self.mainRegional)
        self.mainRegional.controls=self.default
        
    def start_date_changed(self, e):
        if e.date:
            self.startDateButton.text = e.date.strftime("%Y-%m-%d")
            self.startDateButton.update()
    
    def end_date_changed(self, e):
        if e.date:
            self.endDateButton.text = e.date.strftime("%Y-%m-%d")
            self.endDateButton.update()

    def changeMain(self,e):
        index=e.control.selected_index
        if index==0:
            self.mainRegional.controls=self.default
        elif index==1:
            self.mainRegional.controls=[
                ft.Container(
                    content=ft.Column(
                        controls=[
                            ft.Text("科目余额表", size=20, weight=ft.FontWeight.BOLD, color=ft.colors.PRIMARY),
                            ft.Row(
                                controls=[
                                    ft.Container(
                                        content=ft.Row(
                                            controls=[
                                                ft.Text("开始日期", size=14, weight=ft.FontWeight.BOLD),
                                                self.startDateButton
                                            ],
                                            spacing=10,
                                            alignment=ft.MainAxisAlignment.START,
                                        ),
                                        padding=10,
                                        border_radius=10,
                                        bgcolor=ft.colors.SURFACE_VARIANT,
                                    ),
                                    ft.Container(
                                        content=ft.Row(
                                            controls=[
                                                ft.Text("结束日期", size=14, weight=ft.FontWeight.BOLD),
                                                self.endDateButton
                                            ],
                                            spacing=10,
                                            alignment=ft.MainAxisAlignment.START,
                                        ),
                                        padding=10,
                                        border_radius=10,
                                        bgcolor=ft.colors.SURFACE_VARIANT,
                                    ),
                                ],
                                alignment=ft.MainAxisAlignment.SPACE_AROUND,
                            ),
                            ft.Container(
                                content=ft.ElevatedButton(
                                    text="导出科目余额表",
                                    icon=ft.icons.DOWNLOAD,
                                    style=ft.ButtonStyle(
                                        shape=ft.RoundedRectangleBorder(radius=10),
                                        padding=15,
                                    ),
                                    on_click=lambda e: callF.thisProcess.run({"功能":"导出科目余额表","参数":[self.startDateButton.text, self.endDateButton.text]})
                                ),
                                padding=10,
                                alignment=ft.alignment.center,
                            ),
                        ],
                        spacing=15,
                    ),
                    padding=20,
                    border_radius=15,
                    bgcolor=ft.colors.SURFACE_VARIANT,
                    margin=ft.margin.only(bottom=20),
                ),
                self.startDate,
                self.endDate
            ]
        elif index==2:
            self.mainRegional.controls=[FinancialReportView(),JiuQiReport()]
        self.mainRegional.update()