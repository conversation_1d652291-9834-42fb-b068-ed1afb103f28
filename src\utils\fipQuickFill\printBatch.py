import tkinter as tk
from tkinter import ttk
import win32com.client
import win32api
import src.base.settings as settings
import fitz


# !python3
# 自动打印.py - 自动打印，使用PDF
# -*- coding: utf-8 -*-
import os,time
import win32api
import win32print
from PIL import Image

def pdf_wirte(s,text):
    with fitz.open(s) as doc:
        for page in doc:
                try:
                    font_size = 16
                    x = 20
                    y = 20
                    page.insert_text(fitz.Point(x, y), text, fontname='helv', fontsize=font_size)
                except:
                    page
        try:
            new_path=s
            doc.saveIncr() 
        except:
            dirname, filename = os.path.split(s)
            # 分离文件名和扩展名
            name, ext = os.path.splitext(filename)
            # 在文件名后面加上“新”
            new_filename = f"{name}新{ext}"
            # 重新组合路径
            new_path = os.path.join(dirname, new_filename)
            doc.save(new_path)
        return new_path

def image_to_pdf(image_path):
    # 打开图片文件
    img = Image.open(image_path)
    
    # 创建PDF文件路径
    pdf_path = os.path.splitext(image_path)[0] + '.pdf'
    
    # 将图片保存为PDF
    img.save(pdf_path, "PDF", resolution=100.0)
    
    return pdf_path
def changePrint(printName):
    # 重要提示：因为我的PDF默认使用Adobe Acrobat DC打开，但这里有一个问题是必须关闭Acrobat.exe才能知道默认打印机的更改，故每次打印前为保险起见需要关闭所有Acrobat.exe
    try:
        os.system("taskkill /F /IM Acrobat.exe")
    except:
        pass# 如没有找到Acrobat.exe进程，则跳过
    
    
    # 获取所有打印机信息
    printers = win32print.EnumPrinters(3)
    
    # 获取默认打印机
    default_printers = win32print.GetDefaultPrinter()
    # 指定另一个打印机名作为默认打印机
    win32print.SetDefaultPrinter(printName)# 这里可以换成其他打印机名称
    default_printers = win32print.GetDefaultPrinter()
    ##查看devmode各类属性
    #for n in dir(devmode):
    #   print(n,getattr(devmode,n))

def printFile(filename,Copies,FormName='A4',PaperSize=9,Orientation=1,Duplex=2,Color=1,selected_printer=None):   
    default_printers = win32print.GetDefaultPrinter()
    selected_printer = default_printers if selected_printer is None else selected_printer
    # 设置权限作为获得句柄语句的参数，有时也可不用
    printaccess = {"DesiredAccess":win32print.PRINTER_ACCESS_USE}# 较低的权限
    print_DEFAULTS = {"DesiredAccess":win32print.PRINTER_ALL_ACCESS}# 较高的权限
    # 获取指定打印机句柄
    pHandle = win32print.OpenPrinter(default_printers,print_DEFAULTS)# 这里使用默认打印机，第2个权限参数是可选选项，但如果不设置足够高的权限可能无法成功更改打印参数设置
    # 根据指定打印机句柄获取指定打印机信息
    properties = win32print.GetPrinter(pHandle,2)#传入1返回1个元祖，传入2返回1个字典
    # 获取打印机打印参数设置——pDevMode类
    devmode = properties['pDevMode']
    # 设置各类参数
    devmode.Copies = Copies # 打印份数设置
    devmode.FormName = FormName # 纸张尺寸设置：默认A4
    devmode.PaperSize = PaperSize # 只设置FormName不能更改纸张尺寸，设定PaperSize可以。另外指定PaperSize大小（A3为8，A4为9，A5为11，B4为12，B5为13等等）后，则PaperLength和PaperWidth不生效，
    devmode.Orientation = Orientation # 方向设置：1为纵向，2为横向。这里默认为1. 
    devmode.Duplex = Duplex # 双面打印设置：1代表单面；2代表是，翻转；3代表是，向上翻。默认为2。
    devmode.Color = Color # 灰度打印设置：1代表仅限黑白，2代表关（即彩色）
    # 可以对一些一般不更改的打印参数进行默认设置
    devmode.MediaType = 291 # 纸张类型设置：292是未指定，291是普通纸，290是HP EcoFFICIENT，以此往下类推。这里默认291——普通纸
    devmode.DefaultSource = 264 # 纸张来源设置：这里默认为1——打印机自动选择

    #保存更改后的设置
    properties['pDevMode'] = devmode
    win32print.SetPrinter(pHandle,2,properties,0)
    #使用ShellExecute打印不成功
    #printPath=thePath+"\第三方组件\SumatraPDF.exe"
    #printCmd=f"{printPath} -print-to \"{selected_printer}\" -print-settings \"{settings}\" \"{filename}\""
    #os.system(printCmd)
    time.sleep(0.1)
    process_info = win32api.ShellExecute(
            0,
            "print",#"print"
            filename,
            '/d:"%s"' % win32print.GetDefaultPrinter(),#设置为None即可，也可用下面一行的语句
            #'/d:"%s"' % win32print.GetDefaultPrinter(),
            ".",
            0
        )
    time.sleep(3)



def get_printer_list():
    # 创建 WMI 服务对象
    objWMI = win32com.client.GetObject("winmgmts:")
    # 获取所有打印机
    printers = objWMI.InstancesOf("Win32_Printer")
    # 提取打印机名称
    printer_list = [printer.Name for printer in printers]
    return printer_list


def perform_printing(printer_name):
    # 这里可以添加实际的打印逻辑
    print(f"使用 {printer_name}")

def batchPrint():
    def on_printer_select(event):
        selected_printer = printer_var.get()
        print(f"选择打印机: {selected_printer}")
    def on_confirm():
        selected_printer = printer_var.get()
        changePrint(selected_printer)
        print(f"确认打印机: {selected_printer}")
        root.destroy()  # 关闭 GUI
        import tkinter.filedialog
        filePath=tkinter.filedialog.askopenfilenames(initialdir=settings.PATH_FIP_DOWNLOAD)
        for path in filePath:
            if "流程单" in path:
                printFile(path,1,FormName='A4',PaperSize=9,Orientation=2,Duplex=3,Color=1)
            else:
                #获取文件后缀
                suffix=path.split(".")[-1]
                if suffix=="pdf":
                    filename_without_extension = os.path.splitext(os.path.basename(path))[0]
                    newPath=pdf_wirte(path,filename_without_extension)
                    if "代付" in newPath :
                        Color2=2 
                    else:
                        Color2=1
                    printFile(newPath,1,FormName='A4',PaperSize=9,Orientation=1,Duplex=1,Color=Color2) 
                elif suffix=="docx" or suffix=="doc":
                    printFile(path,1,FormName='A4',PaperSize=9,Orientation=1,Duplex=1,Color=1)
                elif suffix in ["jpg" ,"png","jpeg"]:
                    path2=image_to_pdf(path)
                    filename_without_extension = os.path.splitext(os.path.basename(path2))[0]
                    path3=pdf_wirte(path2,filename_without_extension)
                    printFile(path3,1,FormName='A4',PaperSize=9,Orientation=1,Duplex=1,Color=2)
            #print("结束"+path)
        print("打印完啦")
    printers = get_printer_list()
    # 创建主窗口
    root = tk.Tk()
    # 设置窗口置顶
    root.attributes('-topmost', True)
    root.iconbitmap(settings.CACHE_PATH+"/rpa.ico")  # 设置窗口图标
    width=400
    height=200
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()

    # 计算窗口左上角的位置
    x = (screen_width / 2) - (width / 2)
    y = (screen_height / 2) - (height / 2)

    # 设置窗口的大小和位置
    root.geometry(f'{width}x{height}+{int(x)}+{int(y)}')
    root.title("打印")
    # 创建一个标签
    label = ttk.Label(root, text="选择打印机:")
    label.pack(pady=10)
    # 创建一个变量来存储选择的打印机
    printer_var = tk.StringVar()
    # 创建一个下拉列表
    printer_combo = ttk.Combobox(root, textvariable=printer_var)
    printer_combo['values'] = printers
    printer_combo.pack(pady=10)
    # 绑定选择事件
    printer_combo.bind('<<ComboboxSelected>>', on_printer_select)
    # 创建一个确定按钮
    confirm_button = ttk.Button(root, text="确定", command=on_confirm)
    confirm_button.pack(pady=10)
    root.mainloop()


    