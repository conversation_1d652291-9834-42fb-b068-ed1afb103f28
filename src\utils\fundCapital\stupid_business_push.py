
from playwright.sync_api import Page
import time
import src.utils.cscec as cscec



def autoABC(i,table,page:Page,d: dict,project_name): 
    page.locator("//div[contains(text(),'分包结算')]").click()
    page.get_by_text("过程应付款申请",exact=True).nth(0).click()
    page.locator("//input[@placeholder='请输入项目名称']").fill(project_name)
    page.locator("//span[contains(text(),'查 询')]").click()
    page.locator("//a[text()='"+project_name+"']/parent::div//a").click()
    page.get_by_text("分供合同名称").click()
    page.locator("//span[contains(text(),'新增')]").click()
    page.locator("//li[text()='过程应付款申请']").click()
    s="//div[text()='分包单位名称']/parent::span/parent::label/parent::div/parent::div//input[@placeholder='请输入']"
    page.locator(s).fill(d["分供方名称"])
    s="//div[text()='分包合同名称']/parent::span/parent::label/parent::div/parent::div//input[@placeholder='请输入']"
    page.locator(s).fill(d["分供方合同名称"][:49])#B合同名称不能超过50字符，不一致没法用
    page.locator("//span[contains(text(),'搜 索')]").click()
    page.locator(s).click()#此处能点击说明搜索完成
    try:#尝试
        page.locator("//div[text()='选择分包合同']/parent::div/parent::div//div[text()='"+d["分供方合同名称"]+"']/parent::td/preceding::td[3]").first.click(timeout=100) #改为合同重新查找
        page.locator("//span[contains(text(),'确认')]").click()
    except:
        page.locator("//span[contains(text(),'取消')]").click()
        table.item(i,"消息列").Value="合同映射有问题"
        return
    s="//div[text()='所属月份']/parent::span/parent::label/parent::div/parent::div//input[@placeholder='请选择']"
    page.locator(s).click()
    page.locator("//td//div[text()='"+d["月份"]+"']").click()
    s="//div[contains(text(),'是否最终结算款申请')]/parent::span/parent::label/parent::div/parent::div//input"
    page.locator(s).click()
    page.locator("//div[@class='g3-scm-select-item-option-content'][contains(text(),'否')]").click()
    
    page.locator("//textarea[@placeholder='请输入']").fill(d["合同业务内容"])

    s="//span[contains(text(),'预付款（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[4]//input"
    s1="//span[contains(text(),'预付款（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[4]"
    page.locator(s1).click()
    page.locator(s).fill("0")
    s1="//span[contains(text(),'预付款（不含税）')]/parent::div/parent::div/parent::td"
    page.locator(s1).click() #预付款报错
    
    s="//span[text()='工程总造价（不含税）']/parent::div/parent::div/parent::td/following-sibling::td[4]//span"
    jiesuan=float(page.locator(s).text_content())
    s="//span[contains(text(),'应付工程款（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[4]//input"
    s1="//span[contains(text(),'应付工程款（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[4]"
    page.locator(s1).click()
    page.locator(s).fill(str(round(jiesuan*d["约定付款比例"],2)))
    s1="//span[contains(text(),'应付工程款（不含税）')]/parent::div/parent::div/parent::td"
    page.locator(s1).click()

    if d["约定付款比例"]<0.95:
        ration=0.05
    else:
        ration=1-d["约定付款比例"]
    s="//span[contains(text(),'质保金（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[4]//input"
    s1="//span[contains(text(),'质保金（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[4]"
    page.locator(s1).click()
    page.locator(s).fill(str(round(jiesuan*ration,2)))
    s1="//span[contains(text(),'质保金（不含税）')]/parent::div/parent::div/parent::td"
    page.locator(s1).click() #有的要填质保金


    page.mouse.wheel(0,1000)# 滚动鼠标 , 参数给一个较大值，以保证直接移动至最后
    
    s="//span[text()='价税合计']/parent::div/parent::div/parent::td/following-sibling::td[4]//span"
    jiesuan=float(page.locator(s).text_content())
    s="//span[text()='按合同应付金额（含税）']/parent::div/parent::div/parent::td/following-sibling::td[4]//input"
    s1="//span[text()='按合同应付金额（含税）']/parent::div/parent::div/parent::td/following-sibling::td[4]"
    page.locator(s1).click()
    page.locator(s).fill(str(round(d["按合同付款金额"],2)))
    s1="//span[text()='按合同应付金额（含税）']/parent::div/parent::div/parent::td"
    page.locator(s1).click()

    
    page.locator("//span[contains(text(),'保存')]").click()
    page.locator("//span[contains(text(),'确认')]").click()
    page.locator("//button[@class='g3-scm-btn g3-scm-btn-primary g3-button']").click()
    page.locator("//span[contains(text(),'确认')]").click()
    

    cscec.getVisible(page,"//td[text()='应付款推送']/preceding::td[2]").click()
    cscec.getVisible(page,"//button[@class='ant-btn ant-btn-primary']").click()



def autoOne(i,table,page:Page,d: dict,project_name): 
    page.locator("//div[contains(text(),'分包结算')]").click()
    page.get_by_text("过程应付款申请",exact=True).nth(0).click()
    page.locator("//input[@placeholder='请输入项目名称']").fill(project_name)
    page.locator("//span[contains(text(),'查 询')]").click()
    page.locator("//a[text()='"+project_name+"']/parent::div/parent::td//a").click()
    page.get_by_text("分供合同名称").click()
    page.locator("//span[contains(text(),'新增')]").click()
    page.locator("//li[contains(text(),'应付款申请(简易)')]").click()
    s="//div[text()='分包单位名称']/parent::span/parent::label/parent::div/parent::div//input[@placeholder='请输入']"
    page.locator(s).fill(d["分供方名称"])
    s="//div[text()='分包合同名称']/parent::span/parent::label/parent::div/parent::div//input[@placeholder='请输入']"
    page.locator(s).fill(d["分供方合同名称"][:49])#jb合同名称不能超过50字符，不一致没法用
    page.locator("//span[contains(text(),'搜 索')]").click()
    page.locator(s).click()#此处能点击说明搜索完成
    try:#尝试
        page.locator("//div[text()='选择分包合同']/parent::div/parent::div//div[text()='"+d["分供方合同名称"]+"']/parent::td/preceding::td[3]").first.click(timeout=100) #改为合同重新查找
        page.locator("//span[contains(text(),'确认')]").click()
    except:
        page.locator("//span[contains(text(),'取消')]").click()
        table.item(i,"消息列").Value="合同映射有问题"
        return
    #page.locator("//div[text()='选择分包合同']/parent::div/parent::div//div[text()='"+d["分供方合同名称"]+"']/parent::td/preceding::td[3]").first.click() #改为合同重新查找
    #page.locator("//span[contains(text(),'确认')]").click()

    s="//div[text()='所属月份']/parent::span/parent::label/parent::div/parent::div//input[@placeholder='请选择']"
    page.locator(s).click()
    page.locator("//td//div[text()='"+d["月份"]+"']").click()
    s="//div[contains(text(),'是否最终结算款申请')]/parent::span/parent::label/parent::div/parent::div//input"
    page.locator(s).click()
    page.locator("//div[@class='g3-scm-select-item-option-content'][contains(text(),'否')]").click()

    page.locator("//textarea[@placeholder='请输入']").fill(d["合同业务内容"])



    s="//span[contains(text(),'合约内工程造价（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[4]//input"
    s1="//span[contains(text(),'合约内工程造价（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[4]"
    page.locator(s1).click()
    page.locator(s).fill(str(round(d["不含税结算额"],2)))

    
    s="//span[contains(text(),'其中：安全文明施工费（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[4]//input"
    s1="//span[contains(text(),'其中：安全文明施工费（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[4]"
    page.locator(s1).click()
    page.locator(s).fill("0")

    s="//span[contains(text(),'应付工程款（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[4]//input"
    s1="//span[contains(text(),'应付工程款（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[4]"
    page.locator(s1).click()
    if d["不含税结算额"]>1:
        page.locator(s).fill(str(round(d["不含税结算额"]*d["约定付款比例"],2)))
        s="//span[contains(text(),'质保金（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[4]//input"
        s1="//span[contains(text(),'质保金（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[4]"
        page.locator(s1).click()
        page.locator(s).fill("0")
        s1="//span[contains(text(),'质保金（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[1]"
        page.locator(s1).click()
    else:
        s2="//span[contains(text(),'应付工程款（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[3]/div/div"
        if float(page.locator(s2).text_content())>1:
            fillAmout="-"+str(round(float(page.locator(s2).text_content())-1,2)) #调整一元钱
            page.locator(s).fill(fillAmout)
        else:
            page.locator(s).fill("0")
        page.locator(s2).click()
        s="//span[contains(text(),'质保金（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[4]//input"
        s1="//span[contains(text(),'质保金（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[4]"
        s2="//span[contains(text(),'质保金（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[3]/div/div"
        page.locator(s1).click()
        if s2!="0":
            fillAmout="-"+page.locator(s2).text_content()
            page.locator(s).fill(fillAmout)
        s1="//span[contains(text(),'质保金（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[1]"
        page.locator(s1).click()
        #负数非常离谱，要小心
    
    x=page.locator(s1).bounding_box()["height"]
    page.mouse.wheel(0,9*x)
    s="//span[contains(text(),'质保金税金')]/parent::div/parent::div/parent::td/following-sibling::td[4]//input"
    s1="//span[contains(text(),'质保金税金')]/parent::div/parent::div/parent::td/following-sibling::td[4]"
    page.locator(s1).click()
    page.locator(s).fill(str(round(d["质保金税金"],2)))

    s="//span[contains(text(),'质保金税金')]/parent::div/parent::div/parent::td/following-sibling::td[2]//input"
    s1="//span[contains(text(),'质保金税金')]/parent::div/parent::div/parent::td/following-sibling::td[2]" #税率
    page.locator(s1).click()
    page.locator(s).fill(str(int(d["税率整数"])))
    page.locator("//span[contains(text(),'质保金税金')]/parent::div/parent::div/parent::td/following-sibling::td[1]").click()

    page.mouse.wheel(0,-6*x)# 滚动鼠标 , 参数给一个较大值，以保证直接移动至最上
    s="//span[contains(text(),'奖励小计')]/parent::div/parent::div/parent::td/following-sibling::td[4]//input"
    s1="//span[contains(text(),'奖励小计')]/parent::div/parent::div/parent::td/following-sibling::td[4]"
    page.locator(s1).click()
    page.locator(s).fill("0")
    
    page.mouse.wheel(0,-6*x)# 滚动鼠标 , 参数给一个较大值，以保证直接移动至最上
    s="//span[contains(text(),'预付款（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[4]//input"
    s1="//span[contains(text(),'预付款（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[4]"
    page.locator(s1).click()
    page.locator(s).fill("0")

    s="//span[contains(text(),'其中：人工费（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[4]//input"
    s1="//span[contains(text(),'其中：人工费（不含税）')]/parent::div/parent::div/parent::td/following-sibling::td[4]"
    page.locator(s1).click()
    page.locator(s).fill("0")

    s="//span[contains(text(),'其中：安全文明施工费（不含税')]/parent::div/parent::div/parent::td/following-sibling::td[4]//input"
    s1="//span[contains(text(),'其中：安全文明施工费（不含税')]/parent::div/parent::div/parent::td/following-sibling::td[4]"
    page.locator(s1).click()
    page.locator(s).fill("0")

    s="//span[contains(text(),'变更签证工程造价（不含税')]/parent::div/parent::div/parent::td/following-sibling::td[4]//input"
    s1="//span[contains(text(),'变更签证工程造价（不含税')]/parent::div/parent::div/parent::td/following-sibling::td[4]"
    page.locator(s1).click()
    page.locator(s).fill("0")
    page.locator("//span[contains(text(),'变更签证工程造价（不含税')]/parent::div/parent::div/parent::td/following-sibling::td[1]").click()


    

    page.mouse.wheel(0,7*x)# 滚动鼠标 , 参数给一个较大值，以保证直接移动至最后

    s="//span[contains(text(),'税金小计')]/parent::div/parent::div/parent::td/following-sibling::td[4]//input"
    s1="//span[contains(text(),'税金小计')]/parent::div/parent::div/parent::td/following-sibling::td[4]"
    page.locator(s1).click()
    page.locator(s).fill(str(round(d["结算额税金"],2)))

    s="//span[text()='按合同应付金额（含税）']/parent::div/parent::div/parent::td/following-sibling::td[2]//input"
    s1="//span[text()='按合同应付金额（含税）']/parent::div/parent::div/parent::td/following-sibling::td[2]"
    page.locator(s1).click()
    page.locator(s).fill(str(round(d["约定付款比例"]*100,2)))

    s="//span[text()='按合同应付金额（含税）']/parent::div/parent::div/parent::td/following-sibling::td[4]//input"
    s1="//span[text()='按合同应付金额（含税）']/parent::div/parent::div/parent::td/following-sibling::td[4]"
    page.locator(s1).click()
    page.locator(s).fill(str(round(d["按合同付款金额"],2)))
    s1="//span[text()='按合同应付金额（含税）']/parent::div/parent::div/parent::td"
    page.locator(s1).click()
    page.locator(s1).click()
    page.locator(s1).click()

    
    page.get_by_role("tab", name="按合同其他应付款项明细").click()
    s="//div[contains(text(),'预扣税')]/parent::div/parent::td/following-sibling::td[2]//input"
    s1="//div[contains(text(),'预扣税')]/parent::div/parent::td/following-sibling::td[2]"
    page.locator(s1).click()
    page.locator(s).fill("0")
    s="//div[contains(text(),'农民工工资保证金')]/parent::div/parent::td/following-sibling::td[2]//input"
    s1="//div[contains(text(),'农民工工资保证金')]/parent::div/parent::td/following-sibling::td[2]"
    page.locator(s1).click()
    page.locator(s).fill("0")
    s="//div[contains(text(),'履约保证金')]/parent::div/parent::td/following-sibling::td[2]//input"
    s1="//div[contains(text(),'履约保证金')]/parent::div/parent::td/following-sibling::td[2]"
    page.locator(s1).click()
    page.locator(s).fill("0")
    s="//div[contains(text(),'安全保证金')]/parent::div/parent::td/following-sibling::td[2]//input"
    s1="//div[contains(text(),'安全保证金')]/parent::div/parent::td/following-sibling::td[2]"
    page.locator(s1).click()
    page.locator(s).fill("0")
    s="//div[contains(text(),'其他保证金')]/parent::div/parent::td/following-sibling::td[2]//input"
    s1="//div[contains(text(),'其他保证金')]/parent::div/parent::td/following-sibling::td[2]"
    page.locator(s1).click()
    page.locator(s).fill("0")


    if d["合同类型"].find("分包")!=-1:
        page.get_by_role("tab", name="税前扣款").click()
        s="//div[contains(text(),'总包管理配合费')]/parent::div/parent::td/following-sibling::td[2]//input"
        s1="//div[contains(text(),'总包管理配合费')]/parent::div/parent::td/following-sibling::td[2]"
        page.locator(s1).click()
        page.locator(s).fill("0")
        s="//div[contains(text(),'现场管理费')]/parent::div/parent::td/following-sibling::td[2]//input"
        s1="//div[contains(text(),'现场管理费')]/parent::div/parent::td/following-sibling::td[2]"
        page.locator(s1).click()
        page.locator(s).fill("0")
        s="//div[contains(text(),'维修费用')]/parent::div/parent::td/following-sibling::td[2]//input"
        s1="//div[contains(text(),'维修费用')]/parent::div/parent::td/following-sibling::td[2]"
        page.locator(s1).click()
        page.locator(s).fill("0")
        s="//div[contains(text(),'临建费用')]/parent::div/parent::td/following-sibling::td[2]//input"
        s1="//div[contains(text(),'临建费用')]/parent::div/parent::td/following-sibling::td[2]"
        page.locator(s1).click()
        page.locator(s).fill("0")
        s="//div[contains(text(),'规费')]/parent::div/parent::td/following-sibling::td[2]//input"
        s1="//div[contains(text(),'规费')]/parent::div/parent::td/following-sibling::td[2]"
        page.locator(s1).click()
        page.locator(s).fill("0")
        s="//div[contains(text(),'罚款')]/parent::div/parent::td/following-sibling::td[2]//input"
        s1="//div[contains(text(),'罚款')]/parent::div/parent::td/following-sibling::td[2]"
        page.locator(s1).click()
        page.locator(s).fill("0")
        s="//div[contains(text(),'甲供材及超供扣款')]/parent::div/parent::td/following-sibling::td[2]//input"
        s1="//div[contains(text(),'甲供材及超供扣款')]/parent::div/parent::td/following-sibling::td[2]"
        page.locator(s1).click()
        page.locator(s).fill("0")
        s="//div[contains(text(),'水费')]/parent::div/parent::td/following-sibling::td[2]//input"
        s1="//div[contains(text(),'水费')]/parent::div/parent::td/following-sibling::td[2]"
        page.locator(s1).click()
        page.locator(s).fill("0")
        s="//div[contains(text(),'电费')]/parent::div/parent::td/following-sibling::td[2]//input"
        s1="//div[contains(text(),'电费')]/parent::div/parent::td/following-sibling::td[2]"
        page.locator(s1).click()
        page.locator(s).fill("0")
        s="//div[contains(text(),'电费')]/parent::div/parent::td/parent::tr/following-sibling::tr[1]/td[4]//input"
        s1="//div[contains(text(),'电费')]/parent::div/parent::td/parent::tr/following-sibling::tr[1]/td[4]"
        page.locator(s1).click()
        page.locator(s).fill("0")

    page.locator("//span[contains(text(),'保存')]").click()
    page.locator("//span[contains(text(),'确认')]").click()
    page.locator("//button[@class='g3-scm-btn g3-scm-btn-primary g3-button']").click()
    page.locator("//span[contains(text(),'确认')]").click()
    cscec.getVisible(page,"//td[text()='应付款推送']/preceding::td[2]").click()
    cscec.getVisible(page,"//button[@class='ant-btn ant-btn-primary']").click()


def autoOperate(page:Page):
    print(page.title)
    import src.utils.Excel.excel as excel
    wb=excel.myBook()
    ws=wb.sheet("数据总表对照及推送财商")
    table=ws.table("总表")
    start_number=int(ws.Cells(1,2).Value)
    max_number=table.MaxRow
    for i in range(start_number+1,max_number+1):  #跳过标题行+1
        d=table.toDict(i)
        if d["是否简易推送"]=="是" :
            autoOne(i,table,page,d,d["商务系统项目名称"])
        if d["是否分包合同加ABC项目"]=="是"  and d["数据判断是否推送"]=="是" and d["对照方式"]=="完成对照"and d["结算差额"]>1000 and d["按合同付款金额"]>1:
            autoABC(i,table,page,d,d["商务系统项目名称"])
        ws.Cells(1,2).Value=ws.Cells(1,2).Value+1
        
def nextVBA():
    import Browser.Browser as browser
    page=browser.myBrowser("shangwu").page
    autoOperate(page)



