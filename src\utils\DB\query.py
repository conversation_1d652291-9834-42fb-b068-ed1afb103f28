import pandas as pd
import duckdb
import src.base.settings as settings
import src.utils.DB.outputSQL as outputSQL
import os
import openpyxl
from openpyxl.utils.dataframe import dataframe_to_rows
from src.utils.DB.configDB import configDB

ws=openpyxl.load_workbook(settings.PATH_CONFIG+r"\配置文件-资金.xlsx",read_only=True)["单据分类"]
docCodeDict={}
for row in ws.iter_rows(min_row=2,values_only=True):
    docCodeDict[row[0]]=row[1]

def if_list_elements(lst2: list,s:str): #资金分类函数
    d=s[:3]
    lst=str(lst2)
    if d in docCodeDict: 
        e=docCodeDict[d]
        if d == "CFK" and "劳务派遣" in lst:
            return "对外成本费用付款申请-薪酬"
        elif d == "SRL" and "待资金系统" in lst:
            return "收款确认单-退票"
        elif d == "JFK":
            if "劳务" in lst:
                return "支付劳务款"
            elif "购货" in lst:
                return "支付购货款"
            elif "分包" in lst:
                return "支付分包工程款"
            else :
                return "支付供应商其他"
        else:
            return e
    else:
        return ""
def accountBalanceTable(startDate,endDate):
    con = duckdb.connect(database=settings.PATH_DUCKDB+'/example.duckdb', read_only=False)
    content = outputSQL.科目余额表
    content=content.replace('期初日期留',startDate)
    content=content.replace('期末日期留',endDate)
    cancellationResult=con.execute(content).df()
    cancellationResult.to_excel(settings.PATH_QUERY+'\科目余额表.xlsx', index=False)
    return cancellationResult


def sanitize_filename(filename):
    # 定义Windows不允许的特殊字符
    special_chars = ['<', '>', ':', '"', '/', '\\', '|', '?', '*']
    # 替换特殊字符为下划线
    for char in special_chars:
        filename = filename.replace(char, '_')
    return filename          
def getLedgerTest():
    con = duckdb.connect(database=settings.PATH_DUCKDB+'/example.duckdb', read_only=False)   
    con.create_function("if_classification", if_list_elements, [duckdb.list_type(type=str),str], str)
    print("此步执行耗时较久，请耐心等待")
    

    con.execute(outputSQL.成本查询)
    con.execute(outputSQL.内行查询)
    con.execute(outputSQL.内行查询资金整理)
    con.execute(outputSQL.分供结算台账)
    con.execute(outputSQL.付款台账)
    con.execute(outputSQL.收款台账)
    con.execute(outputSQL.专项储备)
    con.execute(outputSQL.外部确权台账)


    concatArray=configDB().internalCustomers
    concatArrayTuple=tuple(concatArray[i][0] for i in range(1,len(concatArray)))
    replaceCondition1=str(concatArrayTuple)
    replaceCondition2=configDB().expenseTransfer[0][0]
    replace2=outputSQL.按利润中心计算余额明细帐.replace("机关划转费用科目",replaceCondition2)
    replace2=replace2.replace("('总部客商名称')",replaceCondition1)
    df1=con.execute("select * from 一体化合同台账").df()
    df0=con.execute(replace2).df()
    df2=con.execute(outputSQL.应付汇总按供应商).df()
    df3=con.execute(outputSQL.应付汇总按合同).df()
    df4=pd.merge(df1,df3,on=['合同编号','项目名称'],how='left')
    con.execute(f"select 凭证编号,过帐日期,成本类别 as 类型,利润中心,利润中心描述 as 项目名称,成本科目 as 科目,文本 as 事由,中台单据号,科目分类,入账成本 as 金额 from 成本表 where 过帐日期>='{settings.YEAR}-1-1'").df().to_excel(settings.PATH_QUERY+'\本年成本分析.xlsx',sheet_name="成本导入",index=False)
    df0.to_excel(settings.PATH_QUERY+'\总台账.xlsx', index=False)
    df2.to_excel(settings.PATH_QUERY+'\应付汇总供应商.xlsx', index=False)
    df4.to_excel(settings.PATH_QUERY+'\合同台账.xlsx', index=False)

    con.execute(f"DROP TABLE IF EXISTS 总台账")
    con.execute(f"CREATE TABLE 总台账 AS SELECT * FROM df0")

    con.execute(f"DROP TABLE IF EXISTS 应付汇总按供应商")
    con.execute(f"CREATE TABLE 应付汇总按供应商 AS SELECT * FROM df2")

    con.execute(f"DROP TABLE IF EXISTS 应付汇总按合同")
    con.execute(f"CREATE TABLE 应付汇总按合同 AS SELECT * FROM df3")
    
    csv=True
    if csv:
        path=settings.PATH_QUERY+'\成本查询.csv'
        con.execute(f"COPY (SELECT * FROM 成本表) TO '{path}' (HEADER, DELIMITER ',');")

        path=settings.PATH_QUERY+'\内行查询.csv'
        con.execute(f"COPY (SELECT * FROM 内行查询) TO '{path}' (HEADER, DELIMITER ',');")

        path=settings.PATH_QUERY+'\资金整理.csv'
        con.execute(f"COPY (SELECT * FROM 资金整理) TO '{path}' (HEADER, DELIMITER ',');")

        path=settings.PATH_QUERY+'\分供结算台账.csv'
        con.execute(f"COPY (SELECT * FROM 分供结算台账) TO '{path}' (HEADER, DELIMITER ',');")

        path=settings.PATH_QUERY+'\付款台账.csv'
        con.execute(f"COPY (SELECT * FROM 付款台账) TO '{path}' (HEADER, DELIMITER ',');")

        path=settings.PATH_QUERY+'\收款台账.csv'
        con.execute(f"COPY (SELECT * FROM 收款台账) TO '{path}' (HEADER, DELIMITER ',');")

        path=settings.PATH_QUERY+'\专项储备.csv'
        con.execute(f"COPY (SELECT * FROM 专项储备) TO '{path}' (HEADER, DELIMITER ',');")

        path=settings.PATH_QUERY+'\内部对账.csv'
        con.execute(f"COPY (SELECT * FROM 内部对账) TO '{path}' (HEADER, DELIMITER ',');")

        path=settings.PATH_QUERY+'\外部确权台账.csv'
        con.execute(f"COPY (SELECT * FROM 外部确权台账) TO '{path}' (HEADER, DELIMITER ',');")
   
    directory_path = settings.PATH_QUERY+'\子表'
    # 判断目录是否存在
    if not os.path.exists(directory_path):
        os.makedirs(directory_path)
    for index,row in df0.iterrows():
        profitCenterCode=f"'{row["利润中心"]}'"
        name=row["利润中心描述"]
        filtered_df9 = con.execute(f"select * from 内部对账 where 利润中心={profitCenterCode}").df()
        filtered_df8 = con.execute(f"select * from 专项储备 where 利润中心={profitCenterCode}").df()
        filtered_df7 = con.execute(f"select * from 收款台账 where 利润中心={profitCenterCode}").df()
        filtered_df6 = con.execute(f"select * from 资金整理 where 利润中心={profitCenterCode}").df()
        filtered_df5 =con.execute(f"select * from 成本表 where 利润中心={profitCenterCode}").df()
        filtered_df4 = con.execute(f"select * from 分供结算台账 where 利润中心={profitCenterCode}").df()
        filtered_df3 = con.execute(f"select * from 付款台账 where 利润中心={profitCenterCode}").df()
        filtered_df10 = con.execute(f"select * from 外部确权台账 where 利润中心={profitCenterCode}").df()

        filtered_df2 = df4[df4['利润中心']== row["利润中心"]]
        filtered_df1 = df2[df2['利润中心']== row["利润中心"]]
        name=sanitize_filename(name)
        #使用openpyxl打开一个模板文件
        wb=openpyxl.load_workbook(settings.PATH_EXCEL+r"\模板\项目报表模板.xlsx",read_only=False)
        #将df写入指定的sheet
        ws1=wb["总数据"]
        ws1.cell(row=1, column=3, value=row['利润中心描述'])
        ws1.cell(row=2, column=3, value=row['利润中心'])
        ws1.cell(row=4, column=3, value=row['收入'])
        ws1.cell(row=5, column=3, value=row['成本'])
        ws1.cell(row=7, column=3, value=row['收入']-row['合同余额'])
        ws1.cell(row=6, column=3, value=row['附加税'])
        ws1.cell(row=8, column=3, value=row['商务间接费用'])
        ws1.cell(row=9, column=3, value=row['商务机械费用'])
        ws1.cell(row=10, column=3, value=row['非分包安全费'])

        ws1.cell(row=13, column=3, value=row['专项储备余额'])
        ws1.cell(row=14, column=3, value=row['合同履约成本余额'])
        ws1.cell(row=15, column=3, value=row['原材料'])
        ws1.cell(row=16, column=3, value=row['暂估应付余额'])
        ws1.cell(row=17, column=3, value=row['总包结算额'])
        ws1.cell(row=18, column=3, value=row['总包付款额'])
        ws1.cell(row=19, column=3, value=row['总包暂估额'])
        ws1.cell(row=21, column=3, value=row['销项税余额'])

        ws1.cell(row=24, column=3, value=row['累计确权'])
        ws1.cell(row=25, column=3, value=row['累计收款'])
        ws1.cell(row=27, column=3, value=row['累计分供结算'])
        ws1.cell(row=28, column=3, value=row['累计分供付款'])

        ws1.cell(row=33, column=3, value=row['原始存量'])
        ws1.cell(row=34, column=3, value=row['内部借款'])
        ws1.cell(row=35, column=3, value=row['保理借款'])
        ws1.cell(row=36, column=3, value=row['内部往来挂总部'])
        ws1.cell(row=37, column=3, value=row['内部往来需调整'])


        def write_df_to_sheet(df,sheetname,wb):
            #将DataFrame写入指定的sheet
            sheet = wb[sheetname]
            for r in dataframe_to_rows(df, index=False, header=True):
                sheet.append(r)

        #将df写入指定的sheet
        write_df_to_sheet(filtered_df1,"应付供应商汇总台账",wb)
        write_df_to_sheet(filtered_df2,"应付合同汇总台账",wb)
        write_df_to_sheet(filtered_df3,"付款台账",wb)
        write_df_to_sheet(filtered_df4,"分供结算台账",wb)
        write_df_to_sheet(filtered_df5,"成本台账",wb)
        write_df_to_sheet(filtered_df6,"资金整理",wb)
        write_df_to_sheet(filtered_df7,"收款台账",wb)
        write_df_to_sheet(filtered_df8,"安全费台账",wb)
        write_df_to_sheet(filtered_df9,"内部对账",wb)
        write_df_to_sheet(filtered_df10,"外部确权台账",wb)

        #保存文件
        wb.save(directory_path + "\\" + name + ".xlsx")
        print(f"{name}子表生成完成")