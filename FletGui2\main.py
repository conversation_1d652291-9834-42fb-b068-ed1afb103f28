import sys
sys.path.append(".")
import asyncio
import importlib
from pathlib import Path

import flet as ft

# Add project root to sys.path to allow imports from src
# Assuming this main.py is at src/Gui/FletGui2/main.py
# Project root is 3 levels up from this file's directory
PROJECT_ROOT = Path(__file__).resolve().parents[3]
sys.path.insert(0, str(PROJECT_ROOT))

from FletGui2.views import ui_constants as C
from FletGui2.views import home_view

# --- Global State for Message Manager & UI Refs ---
message_manager_visible = True
messages = []
message_log_ref = ft.Ref[ft.ListView]()
view_container_ref = ft.Ref[ft.Container]()      # Main content area for switching views
header_ref = ft.Ref[ft.Container]()
message_manager_content_ref = ft.Ref[ft.Container]()

# Widget creation functions
def create_system_status_widget():
    """Create a system status widget for the home view."""
    return ft.Container(
        content=ft.Column(
            [
                ft.Row(
                    [
                        ft.Icon(ft.icons.COMPUTER_ROUNDED, color=C.ACCENT_COLOR, size=20),
                        ft.Text("系统状态", size=16, weight=ft.FontWeight.BOLD, color=C.ACCENT_COLOR, font_family=C.FONT_ORBITRON),
                    ],
                    spacing=8,
                ),
                ft.Divider(height=1, color=ft.colors.with_opacity(0.3, C.ACCENT_COLOR)),
                ft.Row(
                    [
                        ft.Container(
                            content=ft.Icon(ft.icons.CIRCLE, color=C.SUCCESS_COLOR, size=12),
                            margin=ft.margin.only(right=8),
                        ),
                        ft.Text("RPA服务运行中", size=12, color=C.TEXT_COLOR, font_family=C.FONT_CONSOLAS),
                    ],
                ),
                ft.Row(
                    [
                        ft.Container(
                            content=ft.Icon(ft.icons.CIRCLE, color=C.SUCCESS_COLOR, size=12),
                            margin=ft.margin.only(right=8),
                        ),
                        ft.Text("数据库连接正常", size=12, color=C.TEXT_COLOR, font_family=C.FONT_CONSOLAS),
                    ],
                ),
                ft.Row(
                    [
                        ft.Container(
                            content=ft.Icon(ft.icons.CIRCLE, color=C.SUCCESS_COLOR, size=12),
                            margin=ft.margin.only(right=8),
                        ),
                        ft.Text("网络连接稳定", size=12, color=C.TEXT_COLOR, font_family=C.FONT_CONSOLAS),
                    ],
                ),
            ],
            spacing=8,
        ),
        bgcolor=ft.colors.with_opacity(0.8, C.SECONDARY_COLOR),
        border_radius=12,
        padding=15,
        border=ft.border.all(1, ft.colors.with_opacity(0.3, C.ACCENT_COLOR)),
        shadow=ft.BoxShadow(
            spread_radius=1,
            blur_radius=8,
            color=ft.colors.with_opacity(0.2, C.ACCENT_COLOR),
            offset=ft.Offset(0, 2),
        ),
    )

def create_quick_stats_widget():
    """Create a quick statistics widget for the home view."""
    return ft.Container(
        content=ft.Column(
            [
                ft.Row(
                    [
                        ft.Icon(ft.icons.ANALYTICS_ROUNDED, color=C.ACCENT_COLOR, size=20),
                        ft.Text("今日统计", size=16, weight=ft.FontWeight.BOLD, color=C.ACCENT_COLOR, font_family=C.FONT_ORBITRON),
                    ],
                    spacing=8,
                ),
                ft.Divider(height=1, color=ft.colors.with_opacity(0.3, C.ACCENT_COLOR)),
                ft.Row(
                    [
                        ft.Text("处理单据:", size=12, color=C.TEXT_COLOR, font_family=C.FONT_CONSOLAS),
                        ft.Text("156", size=12, weight=ft.FontWeight.BOLD, color=C.SUCCESS_COLOR, font_family=C.FONT_CONSOLAS),
                    ],
                    alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                ),
                ft.Row(
                    [
                        ft.Text("自动制证:", size=12, color=C.TEXT_COLOR, font_family=C.FONT_CONSOLAS),
                        ft.Text("89", size=12, weight=ft.FontWeight.BOLD, color=C.SUCCESS_COLOR, font_family=C.FONT_CONSOLAS),
                    ],
                    alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                ),
                ft.Row(
                    [
                        ft.Text("节省时间:", size=12, color=C.TEXT_COLOR, font_family=C.FONT_CONSOLAS),
                        ft.Text("4.2h", size=12, weight=ft.FontWeight.BOLD, color=C.ACCENT_COLOR, font_family=C.FONT_CONSOLAS),
                    ],
                    alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                ),
            ],
            spacing=8,
        ),
        bgcolor=ft.colors.with_opacity(0.8, C.SECONDARY_COLOR),
        border_radius=12,
        padding=15,
        border=ft.border.all(1, ft.colors.with_opacity(0.3, C.ACCENT_COLOR)),
        shadow=ft.BoxShadow(
            spread_radius=1,
            blur_radius=8,
            color=ft.colors.with_opacity(0.2, C.ACCENT_COLOR),
            offset=ft.Offset(0, 2),
        ),
    )

def create_recent_activity_widget():
    """Create a recent activity widget for the home view."""
    return ft.Container(
        content=ft.Column(
            [
                ft.Row(
                    [
                        ft.Icon(ft.icons.HISTORY_ROUNDED, color=C.ACCENT_COLOR, size=20),
                        ft.Text("最近活动", size=16, weight=ft.FontWeight.BOLD, color=C.ACCENT_COLOR, font_family=C.FONT_ORBITRON),
                    ],
                    spacing=8,
                ),
                ft.Divider(height=1, color=ft.colors.with_opacity(0.3, C.ACCENT_COLOR)),
                ft.Column(
                    [
                        ft.Row(
                            [
                                ft.Container(
                                    content=ft.Icon(ft.icons.CHECK_CIRCLE, color=C.SUCCESS_COLOR, size=14),
                                    margin=ft.margin.only(right=8),
                                ),
                                ft.Column(
                                    [
                                        ft.Text("统计大师执行完成", size=11, color=C.TEXT_COLOR, font_family=C.FONT_CONSOLAS),
                                        ft.Text("2分钟前", size=9, color=ft.colors.with_opacity(0.7, C.TEXT_COLOR), font_family=C.FONT_CONSOLAS),
                                    ],
                                    spacing=2,
                                    expand=True,
                                ),
                            ],
                        ),
                        ft.Row(
                            [
                                ft.Container(
                                    content=ft.Icon(ft.icons.CHECK_CIRCLE, color=C.SUCCESS_COLOR, size=14),
                                    margin=ft.margin.only(right=8),
                                ),
                                ft.Column(
                                    [
                                        ft.Text("自动制证批量处理", size=11, color=C.TEXT_COLOR, font_family=C.FONT_CONSOLAS),
                                        ft.Text("15分钟前", size=9, color=ft.colors.with_opacity(0.7, C.TEXT_COLOR), font_family=C.FONT_CONSOLAS),
                                    ],
                                    spacing=2,
                                    expand=True,
                                ),
                            ],
                        ),
                        ft.Row(
                            [
                                ft.Container(
                                    content=ft.Icon(ft.icons.INFO, color=C.ACCENT_COLOR, size=14),
                                    margin=ft.margin.only(right=8),
                                ),
                                ft.Column(
                                    [
                                        ft.Text("系统启动完成", size=11, color=C.TEXT_COLOR, font_family=C.FONT_CONSOLAS),
                                        ft.Text("1小时前", size=9, color=ft.colors.with_opacity(0.7, C.TEXT_COLOR), font_family=C.FONT_CONSOLAS),
                                    ],
                                    spacing=2,
                                    expand=True,
                                ),
                            ],
                        ),
                    ],
                    spacing=12,
                ),
            ],
            spacing=8,
        ),
        bgcolor=ft.colors.with_opacity(0.8, C.SECONDARY_COLOR),
        border_radius=12,
        padding=15,
        border=ft.border.all(1, ft.colors.with_opacity(0.3, C.ACCENT_COLOR)),
        shadow=ft.BoxShadow(
            spread_radius=1,
            blur_radius=8,
            color=ft.colors.with_opacity(0.2, C.ACCENT_COLOR),
            offset=ft.Offset(0, 2),
        ),
        expand=True,
    )

async def main(page: ft.Page):
    page.title = "信小财 V3.0"
    # Configure window for custom title bar
    #page.window_title_bar_hidden = True
    #ppage.window_title_bar_buttons_hidden = True
    #page.window_frameless = True
    page.window_bgcolor = ft.Colors.TRANSPARENT
    
    page.locale_configuration = ft.LocaleConfiguration(
        supported_locales=[
            ft.Locale("zh", "CN", "Hans"),
        ],
        current_locale=ft.Locale("zh", "CN", "Hans"),
    )
    page.window.center()
    page.vertical_alignment = ft.MainAxisAlignment.START
    page.horizontal_alignment = ft.CrossAxisAlignment.CENTER
    page.bgcolor = C.PRIMARY_COLOR
    page.padding = 0
    page.fonts = {
        C.FONT_ORBITRON: f"https://fonts.googleapis.com/css2?family={C.FONT_ORBITRON.replace(' ', '+')}:wght@400;700&display=swap",
        C.FONT_CONSOLAS: "Consolas, monaco, monospace"
    }
    
    # Create window control buttons
    def create_window_button(icon, on_click, bg_hover_color):
        return ft.Container(
            content=ft.Icon(icon, size=16, color=C.TEXT_COLOR),
            width=40,
            height=30,
            on_click=on_click,
            ink=True,
            border_radius=0,
            bgcolor=ft.colors.TRANSPARENT,
            on_hover=lambda e: setattr(e.control, 'bgcolor', bg_hover_color if e.data == "true" else ft.colors.TRANSPARENT) or e.control.update(),
        )
    
    # Create title bar
    title_bar = ft.Container(
        content=ft.Row(
            [
                ft.WindowDragArea(
                    ft.Container(
                        ft.Row(
                            [
                                ft.Icon(ft.icons.ACCOUNT_BALANCE_WALLET, color=C.ACCENT_COLOR, size=20),
                                ft.Text("信小财 V3.0", color=C.TEXT_COLOR, font_family=C.FONT_ORBITRON, size=14, weight=ft.FontWeight.BOLD),
                            ],
                            spacing=10,
                            vertical_alignment=ft.CrossAxisAlignment.CENTER,
                            expand=True,
                        ),
                        padding=ft.padding.only(left=15),
                    ),
                    expand=True,
                ),
                create_window_button(
                    ft.icons.MINIMIZE_ROUNDED,
                    lambda _: page.window_minimized or page.window_minimized is True or setattr(page, 'window_minimized', True) or page.update(),
                    ft.colors.with_opacity(0.1, C.ACCENT_COLOR)
                ),
                create_window_button(
                    ft.icons.CLOSE,
                    lambda _: page.window_close(),
                    "#ff0000"
                ),
            ],
            spacing=0,
        ),
        height=30,
        bgcolor=C.SECONDARY_COLOR,
    )

    async def add_message(text: str, color: str = C.TEXT_COLOR):
        messages.append(ft.Text(text, color=color, size=12, font_family=C.FONT_CONSOLAS))
        if len(messages) > 50: # Keep only the last 50 messages
            messages.pop(0)
        await update_message_log()

    async def update_message_log():
        if message_log_ref.current:
            message_log_ref.current.controls = list(reversed(messages))
            await message_log_ref.current.update_async()

    async def update_ui():
        if message_manager_content_ref.current:
            message_manager_content_ref.current.visible = message_manager_visible
        if header_ref.current and header_ref.current.content and len(header_ref.current.content.controls) > 1:
            header_ref.current.content.controls[1].icon = ft.icons.MENU_OPEN_ROUNDED if message_manager_visible else ft.icons.MENU_ROUNDED
        # page.update() might be blocking, use update_async if available and appropriate
        await page.update_async() 
        # Avoid logging message for UI update itself unless specifically needed
        # await add_message(f"Message manager {"shown" if message_manager_visible else "hidden"}.")

    async def toggle_message_manager(e):
        global message_manager_visible
        message_manager_visible = not message_manager_visible
        await update_ui()
        await add_message(f"Message manager {"shown" if message_manager_visible else "hidden"}.")

    async def show_home_view(e=None): # Added e=None for consistent callback signature
        if view_container_ref.current and function_grid_container:
            # Create enhanced home screen layout with widgets
            home_screen_layout = ft.Container(
                content=ft.Row(
                    [
                        # Left side with widgets
                        ft.Container(
                            content=ft.Column(
                                [
                                    # System status widget
                                    create_system_status_widget(),
                                    # Quick stats widget
                                    create_quick_stats_widget(),
                                    # Recent activity widget
                                    create_recent_activity_widget(),
                                ],
                                spacing=15,
                                expand=True,
                            ),
                            width=280,
                            padding=ft.padding.all(15),
                        ),
                        # Center area with function grid
                        ft.Container(
                            content=function_grid_container,
                            expand=True,
                            alignment=ft.alignment.center,
                            padding=ft.padding.symmetric(horizontal=20, vertical=15),
                        ),
                    ],
                    expand=True,
                    vertical_alignment=ft.CrossAxisAlignment.START,
                ),
                expand=True,
                padding=ft.padding.all(10)
            )
            view_container_ref.current.content = home_screen_layout
            await view_container_ref.current.update_async()
            await add_message("Navigated to Home Screen", C.SUCCESS_COLOR)
        else:
            print("DEBUG: Critical refs not ready in show_home_view (view_container_ref, function_display_area_ref, or function_grid_container)")
            await add_message("Error: UI components not ready for home screen.", C.ERROR_COLOR)

    async def load_view(module_name: str, view_display_name: str, e=None):
        if view_container_ref.current: # Target the main view container
            try:
                full_module_path = f"FletGui2.views.{module_name}"
                view_module = importlib.import_module(full_module_path)
                
                if hasattr(view_module, "get_view"):
                    view_content = view_module.get_view(page, view_display_name, show_home_view)
                    view_container_ref.current.content = view_content # Load view into the main container
                    await add_message(f"Loaded {view_display_name}", C.SUCCESS_COLOR)
                else:
                    error_text = f"Error: '{module_name}' view has no get_view() function."
                    view_container_ref.current.content = ft.Text(error_text, color=C.ERROR_COLOR, font_family=C.FONT_CONSOLAS)
                    await add_message(f"Error loading view '{module_name}'.", C.ERROR_COLOR)
            except ModuleNotFoundError:
                error_text = f"Error: View module '{module_name}' not found."
                view_container_ref.current.content = ft.Text(error_text, color=C.ERROR_COLOR, font_family=C.FONT_CONSOLAS)
                await add_message(f"Could not find view module '{module_name}'.", C.ERROR_COLOR)
            except Exception as ex:
                error_text = f"An error occurred while loading {view_display_name}."
                view_container_ref.current.content = ft.Text(error_text, color=C.ERROR_COLOR, font_family=C.FONT_CONSOLAS)
                await add_message(f"Error initializing {view_display_name}: {ex}", C.ERROR_COLOR)
            
            await view_container_ref.current.update_async() # Update the main container
        else:
            await add_message(f"Critical Error: Main view container (view_container_ref) is missing. Cannot load {view_display_name}.", C.ERROR_COLOR)

    # --- UI Components ---
    header = ft.Container(
        ref=header_ref,
        content=ft.Row(
            [
                ft.Text("信小财 V3.0 智能化助手", size=28, weight=ft.FontWeight.BOLD, color=C.ACCENT_COLOR, font_family=C.FONT_ORBITRON),
                ft.IconButton(
                    ft.icons.MENU_OPEN_ROUNDED if message_manager_visible else ft.icons.MENU_ROUNDED,
                    icon_color=C.ACCENT_COLOR, icon_size=30, tooltip="Toggle Message Manager", on_click=toggle_message_manager,
                ),
            ],
            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
        ),
        bgcolor=C.SECONDARY_COLOR, padding=ft.padding.symmetric(horizontal=20, vertical=15),
        border=ft.border.only(bottom=ft.border.BorderSide(2, C.ACCENT_COLOR)),
        shadow=ft.BoxShadow(spread_radius=1, blur_radius=10, color=ft.colors.with_opacity(0.5, C.ACCENT_COLOR), offset=ft.Offset(0, 2))
    )

    function_grid_container = ft.Container(
        content=home_view.get_function_grid(on_click_callback=load_view),
        # width=700, # Removed width, will be controlled by parent container in show_home_view
        # padding=ft.padding.only(top=10, left=10, right=10, bottom=10), # Padding moved to wrapper in show_home_view
        alignment=ft.alignment.center # Ensure grid itself tries to center its content if not expanding fully
    )


    # This container will hold either the home screen (grid + display area) or a loaded view
    view_container = ft.Container(
        ref=view_container_ref,
        expand=True,
        # Initial content will be set by show_home_view when app starts
    )

    message_log_display = ft.ListView(ref=message_log_ref, controls=[], expand=True, spacing=5, auto_scroll=True)

    message_manager_content = ft.Container(
        ref=message_manager_content_ref,
        content=ft.Column(
            [
                ft.Row([ft.Icon(ft.icons.MESSAGE_ROUNDED, color=C.ACCENT_COLOR), ft.Text("消息日志", size=16, weight=ft.FontWeight.BOLD, color=C.ACCENT_COLOR, font_family=C.FONT_ORBITRON)], spacing=10),
                ft.Divider(height=1, color=C.ACCENT_COLOR),
                message_log_display,
            ],
            expand=True,
        ),
        width=350, bgcolor=C.SECONDARY_COLOR, padding=15,
        border=ft.border.only(left=ft.border.BorderSide(2, C.ACCENT_COLOR)),
        shadow=ft.BoxShadow(spread_radius=1, blur_radius=10, color=ft.colors.with_opacity(0.5, C.ACCENT_COLOR), offset=ft.Offset(-2, 0)),
        visible=message_manager_visible,
        animate_size=ft.animation.Animation(300, "easeOutCubic"),
        animate_opacity=ft.animation.Animation(300, "easeOutCubic")
    )

    layout = ft.Column(
        [
            header,
            ft.Row(
                [view_container, message_manager_content], # view_container is now the main content holder
                expand=True,
                vertical_alignment=ft.CrossAxisAlignment.STRETCH
            )
        ],
        expand=True, spacing=0
    )

    # Initial setup
    page.add(layout)
    await show_home_view() # Display initial welcome screen content
    await add_message("System Initialized. Welcome to FIP RPA Control Terminal.", C.SUCCESS_COLOR)
    await page.update_async()

if __name__ == "__main__":
    ft.app(target=main, assets_dir=str(PROJECT_ROOT / "src" / "Gui" / "FletGui2" / "assets"), view=ft.AppView.FLET_APP)
