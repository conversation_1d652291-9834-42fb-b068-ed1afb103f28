
select 
SUM(case when (总账科目长文本 LIKE '应收账款\工程款%' or 总账科目长文本 LIKE '合同资产\质保金%') then 本期借方金额 ELSE NULL END) AS 累计应收,
SUM(case when (总账科目长文本 LIKE '应收账款\工程款%') then 本期贷方金额 ELSE NULL END) AS 累计已收,
SUM(case when (总账科目长文本 LIKE '%可用存款' or 总账科目长文本 LIKE '%银行存款%' or  总账科目长文本 LIKE '%内部借贷%' or
(总账科目长文本 LIKE '%内部往来%其他%' and (客户名称 = '中建三局安装工程有限公司' or 客户名称 = '中建三局集团有限公司安装事业部')))
then 期末余额 ELSE NULL END) as 资金余额,
any_value(利润中心)
from df
GROUP by 利润中心

