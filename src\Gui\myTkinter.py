import tkinter
import tkinter.messagebox
import customtkinter
import tkinter.ttk
import time
import sys
import os
import pythoncom
import ctypes
import threading
import multiprocessing
from multiprocessing import  Queue
shared_queue = Queue()
import src.Gui.processClass 
import src.Gui.allFuntion as thisFuntion
import src.Gui.register
import src.base.settings as settings
import src.base.cache as cache
thePath=settings.PATH_DATA


thisProcess=src.Gui.processClass.myProcess(thisFuntion.allfunction,shared_queue)
ifThreadmsg=True
regInstance=src.Gui.register.RegisterDialog()

def closeProcess(ifThreadmsg):
    thisProcess.terminate()
    time.sleep(1)
    ifThreadmsg=False


customtkinter.set_appearance_mode("System")  # Modes: "System" (standard), "Dark", "Light"
customtkinter.set_default_color_theme("blue")  # Themes: "blue" (standard), "green", "dark-blue"


class App(customtkinter.CTk):
    def __init__(self):
        super().__init__()
        pythoncom.CoInitialize()
    
        # configure window
        self.title("Power Python Tool")
        width=1100
        height=580
        screenwidth = self.winfo_screenwidth()
        screenheight = self.winfo_screenheight()
    
        size = '%dx%d+%d+%d' % (width, height, (screenwidth - width)/2, (screenheight - height)/2)
        self.geometry(size)
        #self.geometry(f"{1100}x{580}")

        # configure grid layout (4x4)
        self.grid_columnconfigure(1, weight=1)
        self.grid_columnconfigure((2, 3), weight=0)
        self.grid_rowconfigure((0, 1, 2), weight=1)

        # create sidebar frame with widgets

        self.sidebar_frame = customtkinter.CTkFrame(self, width=140, corner_radius=0)
        self.sidebar_frame.grid(row=0, column=0, rowspan=4, sticky="nsew")
        self.sidebar_frame.grid_rowconfigure(7, weight=1)
        self.logo_label = customtkinter.CTkLabel(self.sidebar_frame, text="功能切换", font=customtkinter.CTkFont(size=20, weight="bold"))
        self.logo_label.grid(row=0, column=0, padx=20, pady=(20, 10))

        self.sidebar_button_1 = customtkinter.CTkButton(self.sidebar_frame,text='一体化快捷',fg_color='yellow',text_color='green',command=lambda:self.sidebar_button_event(self.sidebar_button_1))
        self.sidebar_button_1.grid(row=1, column=0, padx=20, pady=10)
        self.sidebar_button_2 = customtkinter.CTkButton(self.sidebar_frame,text='税务板块',command=lambda:self.sidebar_button_event(self.sidebar_button_2))
        self.sidebar_button_2.grid(row=2, column=0, padx=20, pady=10)
        self.sidebar_button_3 = customtkinter.CTkButton(self.sidebar_frame,text='资金板块',command=lambda:self.sidebar_button_event(self.sidebar_button_3))
        self.sidebar_button_3.grid(row=3, column=0, padx=20, pady=10)
        self.sidebar_button_4 = customtkinter.CTkButton(self.sidebar_frame,text='数据板块',command=lambda:self.sidebar_button_event(self.sidebar_button_4))
        self.sidebar_button_4.grid(row=4, column=0, padx=20, pady=10)
        self.sidebar_button_5 = customtkinter.CTkButton(self.sidebar_frame,text='结账板块',command=lambda:self.sidebar_button_event(self.sidebar_button_5))
        self.sidebar_button_5.grid(row=5, column=0, padx=20, pady=10)
        self.sidebar_button_6 = customtkinter.CTkButton(self.sidebar_frame,text='设置板块',command=lambda:self.sidebar_button_event(self.sidebar_button_6))
        self.sidebar_button_6.grid(row=7, column=0, padx=20, pady=10)
        self.labelRegister = customtkinter.CTkLabel(self.sidebar_frame, text="本机到期时间"+regInstance.expire_time, font=customtkinter.CTkFont(size=10, weight="bold"))
        self.labelRegister.grid(row=8, column=0, padx=20, pady=(20, 10))


        # 创建第一个frame
        self.frame_1 = None
        self.frame_2 = None
        self.frame_3 = None
        self.frame_4 = None
        self.frame_5 = None
        self.frame_6 = None

        self.sidebar_frame2 = customtkinter.CTkFrame(self, width=150, corner_radius=0)
        self.sidebar_frame2.grid(row=0, column=2, rowspan=4, sticky="nsew")
        self.sidebar_frame2.grid_rowconfigure(7, weight=1)
        self.logo_label = customtkinter.CTkLabel(self.sidebar_frame2, text="消息管理器", font=customtkinter.CTkFont(size=20, weight="bold"))
        self.logo_label.grid(row=0, column=0, padx=20, pady=(20, 10))
        self.textBox=customtkinter.CTkTextbox(self.sidebar_frame2,width=154,height=300)
        self.textBox.grid(row=1, column=0, padx=20, pady=(20, 10))

        self.stopButton = customtkinter.CTkButton(self.sidebar_frame2,text='快速停止',command=lambda:closeProcess(ifThreadmsg))
        self.stopButton.grid(row=2, column=0, padx=20, pady=(20, 10))
        
        self.openButton = customtkinter.CTkButton(self.sidebar_frame2,text='打开文件夹',command=lambda:os.startfile(thePath))
        self.openButton.grid(row=3, column=0, padx=20, pady=(20, 10))

        self.creatFrame6()
        self.creatFrame5()
        self.creatFrame4()
        self.creatFrame2()
        self.creatFrame1()
        self.creatFrame3()
    def creatFrame1(self):
        self.frame_1 = customtkinter.CTkFrame(self,corner_radius=10)
        self.frame_1.grid(row=0, column=1, rowspan=4, sticky="nsew")
        self.frame_1.grid_rowconfigure(4, weight=1)

        tabview = customtkinter.CTkTabview(master=self.frame_1,width=800,height=600)
        tabview.pack(padx=20, pady=20)
        tabview.add("资金计划")  # add tab at the end
        tabview.add("资金常用")  # add tab at the end
        tabview.add("财商系统")  # add tab at the end
        tabview.set("资金常用")  # set currently visible tab

        downloadPlanButton = customtkinter.CTkButton(master=tabview.tab("资金计划"),text="下载一体化计划",command=lambda:self.runProcess(downloadPlanButton))
        downloadPlanButton.pack(padx=20, pady=20)

        uploadPlanButton = customtkinter.CTkButton(master=tabview.tab("资金计划"),text="上传一体化计划",command=lambda:self.runProcess(uploadPlanButton))
        uploadPlanButton.pack(padx=10, pady=10)

        uploadPlanButton2=customtkinter.CTkButton(master=tabview.tab("资金计划"),text="上传财商资金计划",command=lambda:self.runProcess(uploadPlanButton2))
        uploadPlanButton2.pack(padx=10, pady=10)
        
        button = customtkinter.CTkButton(master=tabview.tab("资金常用"),text="更新在途单据wps",command=lambda:self.runProcess(button))
        button.pack(padx=10, pady=10)
        button2 = customtkinter.CTkButton(master=tabview.tab("资金常用"),text="更新分供台账wps",command=lambda:self.runProcess(button2))
        button2.pack(padx=10, pady=10)
        customtkinter.CTkButton(master=tabview.tab("资金常用"),text="打开财商资金文件",command=lambda:os.startfile(thePath+"\\Excel文件\\财商资金模块.xlsm")).pack(padx=5, pady=5)
        getreDataButton=customtkinter.CTkButton(master=tabview.tab("资金常用"),text="更新收支数据",command=lambda:self.runProcess(getreDataButton))
        getreDataButton.pack(padx=5, pady=5)
        revenueAndExpenditureLedgerButton = customtkinter.CTkButton(master=tabview.tab("资金常用"),text="财商资金收支台账",command=lambda:self.runProcess(revenueAndExpenditureLedgerButton))
        revenueAndExpenditureLedgerButton.pack(padx=5, pady=5)

        fipContractLedgerButton = customtkinter.CTkButton(master=tabview.tab("资金常用"),text="更新分包分供台账",command=lambda:self.runProcess(fipContractLedgerButton))
        fipContractLedgerButton.pack(padx=5, pady=5)

        doPButton1 = customtkinter.CTkButton(master=tabview.tab("财商系统"),text="财商收款登记",command=lambda:self.runProcess(doPButton1))
        doPButton1.pack(padx=20, pady=20)
        doPButton2 = customtkinter.CTkButton(master=tabview.tab("财商系统"),text="财商应收台账",command=lambda:self.runProcess(doPButton2))
        doPButton2.pack(padx=20, pady=20)

    
    def creatFrame2(self):
        self.frame_2 = customtkinter.CTkFrame(self)
        self.frame_2.grid(row=0, column=1, rowspan=4, sticky="nsew")
        
        tabview = customtkinter.CTkTabview(master=self.frame_2,width=800,height=600)
        tabview.pack(padx=20, pady=20)
        tabview.add("税务模块")  # add tab at the end
        tabview.set("税务模块")  # set currently visible tab

    
    def creatFrame3(self):
        self.frame_3 = customtkinter.CTkFrame(self)
        self.frame_3.grid(row=0, column=1, rowspan=4, sticky="nsew")
        
        tabview = customtkinter.CTkTabview(master=self.frame_3,width=800,height=600)
        tabview.pack(padx=20, pady=20)
        tabview.add("常用模块") 
        tabview.add("制证板块")  # add tab at the end
        tabview.add("薪酬模块")  # add tab at the end
        
        tabview.set("常用模块")  # set currently visible tab
        combobox1 = customtkinter.CTkComboBox(master=tabview.tab("制证板块"),values=["自动打开浏览器","使用已打开浏览器"])
        combobox1.pack(padx=10, pady=10)
        button = customtkinter.CTkButton(master=tabview.tab("制证板块"),text='自动制证',command=lambda:self.runProcess(button,[combobox1.get()]))
        button.pack(padx=10, pady=10)

        frame1 = customtkinter.CTkFrame(tabview.tab("常用模块"),border_color="green",border_width=2)
        frame1.pack(padx=20, pady=20)
        buttonDownload = customtkinter.CTkButton(master=frame1 ,text='中台单据导出',command=lambda:self.runProcess(buttonDownload))
        buttonDownload.pack(padx=10, pady=10)
        buttonPrint = customtkinter.CTkButton(master=frame1 ,text='批量打印',command=lambda:self.runProcess(buttonPrint))
        buttonPrint.pack(padx=10, pady=10)
        openChromeButton = customtkinter.CTkButton(master=frame1 ,text='打开chrome浏览器',command=lambda:self.runProcess(openChromeButton))
        openChromeButton.pack(padx=10, pady=10)
        openEdgeButton = customtkinter.CTkButton(master=frame1 ,text='打开edge浏览器',command=lambda:self.runProcess(openEdgeButton))
        openEdgeButton.pack(padx=10, pady=10)

        specialDButton = customtkinter.CTkButton(master=frame1 ,text='特殊下载单据',command=lambda:self.runProcess(specialDButton))
        specialDButton.pack(padx=10, pady=20)

        clearButton = customtkinter.CTkButton(master=tabview.tab("常用模块"),text='清除已登录信息',command=lambda:self.runProcess(clearButton))
        clearButton.pack(padx=10, pady=10)

        claimWagesB = customtkinter.CTkButton(master=tabview.tab("薪酬模块"),text='认领外包工资',command=lambda:self.runProcess(claimWagesB))
        claimWagesB.pack(padx=20, pady=20)

    def creatFrame4(self):
        self.frame_4 = customtkinter.CTkFrame(self)
        self.frame_4.grid(row=0, column=1, rowspan=4, sticky="nsew")
        
        tabview = customtkinter.CTkTabview(master=self.frame_4,width=800,height=600)
        tabview.pack(padx=20, pady=20)
        tabview.add("数据导出")  # add tab at the end
        tabview.add("数据整理")  # add tab at the end
        tabview.add("sap科目余额表")  # add tab at the end
        tabview.add("数据库维护")  # add tab at the end
        tabview.set("数据导出")  # set currently visible tab
        customtkinter.CTkLabel(master=tabview.tab("数据导出"),text="开始日期").pack()
        dateInput1=customtkinter.CTkEntry(master=tabview.tab("数据导出"))
        dateInput1.insert(0, cache.Lastdate)
        dateInput1.pack()

        customtkinter.CTkLabel(master=tabview.tab("数据导出"),text="结束日期").pack()
        dateInput2=customtkinter.CTkEntry(master=tabview.tab("数据导出"))
        dateInput2.insert(0, cache.Nowdate)
        dateInput2.pack()

        customtkinter.CTkLabel(master=tabview.tab("数据导出"),text="导出模式选择").pack()
        combobox = customtkinter.CTkComboBox(master=tabview.tab("数据导出"),values=["直接快速导出","调用活动EXCEL清单"],width=120)
        combobox.pack()
        buttonDownload = customtkinter.CTkButton(master=tabview.tab("数据导出"),text='sap明细数据导出',command=lambda:self.runProcess(buttonDownload,[combobox.get(),dateInput1.get(),dateInput2.get()]))
        buttonDownload.pack(padx=20, pady=20)

        frame2=customtkinter.CTkFrame(tabview.tab("数据导出"),border_color="blue",border_width=2)
        frame2.pack(padx=20, pady=20)
        updateMDButton=customtkinter.CTkButton(master=frame2,text='更新主数据表sap',command=lambda:self.runProcess(updateMDButton))
        updateMDButton.pack(padx=10, pady=10)
        updateInerButton=customtkinter.CTkButton(master=frame2,text='更新内部对账',command=lambda:self.runProcess(updateInerButton))
        updateInerButton.pack(padx=10, pady=10)
        updateInerButton2=customtkinter.CTkButton(master=frame2,text='更新内部对账手选',command=lambda:self.runProcess(updateInerButton2))
        updateInerButton2.pack(padx=10, pady=10)
        updateFipContarctB=customtkinter.CTkButton(master=frame2,text='更新一体化合同台账',command=lambda:self.runProcess(updateFipContarctB))
        updateFipContarctB.pack(padx=10, pady=10)


        ledageButton=customtkinter.CTkButton(master=tabview.tab("数据整理"),text='生成各台账',command=lambda:self.runProcess(ledageButton))
        ledageButton.pack(padx=20, pady=20)

        customtkinter.CTkLabel(master=tabview.tab("sap科目余额表"),text="开始日期").pack()
        dateInput3=customtkinter.CTkEntry(master=tabview.tab("sap科目余额表"))
        dateInput3.insert(0, cache.theYearFirstDay)
        dateInput3.pack()

        customtkinter.CTkLabel(master=tabview.tab("sap科目余额表"),text="结束日期").pack()
        dateInput4=customtkinter.CTkEntry(master=tabview.tab("sap科目余额表"))
        dateInput4.insert(0, cache.Nowdate)
        dateInput4.pack()

        balanceTablebutton = customtkinter.CTkButton(master=tabview.tab("sap科目余额表"),text='科目余额表导出',command=lambda:self.runProcess(balanceTablebutton,[dateInput3.get(),dateInput4.get()]))
        balanceTablebutton.pack(padx=20, pady=20)
        
        frame1 = customtkinter.CTkFrame(tabview.tab("sap科目余额表"),border_color="green",border_width=2)
        frame1.pack(padx=20, pady=20)
        balanceCheckbutton = customtkinter.CTkButton(master=frame1,text='科目余额表检查本地',command=lambda:self.runProcess(balanceCheckbutton,[dateInput3.get(),dateInput4.get()]))
        balanceCheckbutton.pack(padx=10, pady=10)
        balanceCheckbutton2 = customtkinter.CTkButton(master=frame1,text='科目余额表检查SAP导出',command=lambda:self.runProcess(balanceCheckbutton2,[dateInput3.get(),dateInput4.get()]))
        balanceCheckbutton2.pack(padx=10, pady=10)

        creatButton=customtkinter.CTkButton(master=tabview.tab("数据库维护"),text='创建数据库',command=lambda:self.runProcess(creatButton))
        creatButton.pack(padx=20, pady=10)
        comboboxTableName = customtkinter.CTkComboBox(master=tabview.tab("数据库维护"),values=["科目对照","主数据","付款台账","收款台账","分供结算台账","确权台账"])
        comboboxTableName.pack(padx=10, pady=10)   
        queryButton = customtkinter.CTkButton(master=tabview.tab("数据库维护"),text='查询数据库',command=lambda:self.runProcess(queryButton,[comboboxTableName.get()]))
        queryButton.pack(padx=20, pady=20)
        updateButton = customtkinter.CTkButton(master=tabview.tab("数据库维护"),text='更新数据库',command=lambda:self.runProcess(updateButton,[comboboxTableName.get()]))
        updateButton.pack(padx=20, pady=20)




    def creatFrame5(self):
        self.frame_5 = customtkinter.CTkFrame(self)
        self.frame_5.grid(row=0, column=1, rowspan=4, sticky="nsew")
        
        tabview = customtkinter.CTkTabview(master=self.frame_5,width=800,height=600)
        tabview.pack(padx=20, pady=20)
        tabview.add("结账模块")  # add tab at the end
        
        tabview.set("结账模块")  # set currently visible tab
        customtkinter.CTkButton(master=tabview.tab("结账模块"),text='打开报表模板',command=lambda:os.startfile(settings.PATH_EXCEL+"\自动财务报表系统.xlsm")).pack(padx=20, pady=20)
        
        frame0=customtkinter.CTkFrame(tabview.tab("结账模块"),border_color="green",border_width=2)
        frame0.pack(padx=10,pady=10)
        customtkinter.CTkLabel(master=frame0,text="年份").pack(side='left',padx=10, pady=10)
        dateInput1=customtkinter.CTkEntry(master=frame0)
        dateInput1.insert(0, settings.YEAR)
        dateInput1.pack(side='left',padx=10, pady=10)

        customtkinter.CTkLabel(master=frame0,text="月份").pack(side='left',padx=10, pady=10)
        dateInput2=customtkinter.CTkEntry(master=frame0)
        dateInput2.insert(0, settings.MONTH)
        dateInput2.pack(side='left',padx=10, pady=10)

        bT2= customtkinter.CTkButton(master=frame0,text="快速取数",command=lambda:self.runProcess(bT2,[dateInput1.get(),dateInput2.get()]))
        bT2.pack(padx=10, pady=10) 

        bT1= customtkinter.CTkButton(master=tabview.tab("结账模块"),text="安全费计提",command=lambda:self.runProcess(bT1))
        bT1.pack(padx=10, pady=10)        
        bT3= customtkinter.CTkButton(master=tabview.tab("结账模块"),text="自动生成暂估单据",command=lambda:self.runProcess(bT3))
        bT3.pack(padx=10, pady=10)   
        bT4= customtkinter.CTkButton(master=tabview.tab("结账模块"),text="SAP收入测算",command=lambda:self.runProcess(bT4))
        bT4.pack(padx=10, pady=10)  
        bT5= customtkinter.CTkButton(master=tabview.tab("结账模块"),text="生成中台匡算",command=lambda:self.runProcess(bT5))
        bT5.pack(padx=10, pady=10)  
        bT6= customtkinter.CTkButton(master=tabview.tab("结账模块"),text="SAP匡算确认",command=lambda:self.runProcess(bT6))
        bT6.pack(padx=10, pady=10) 
        bT7= customtkinter.CTkButton(master=tabview.tab("结账模块"),text="最终制证",command=lambda:self.runProcess(bT7))
        bT7.pack(padx=10, pady=10) 
        bT8= customtkinter.CTkButton(master=tabview.tab("结账模块"),text="划转管理费",command=lambda:self.runProcess(bT8))
        bT8.pack(padx=10, pady=10) 
    def creatFrame6(self):
        self.frame_6 = customtkinter.CTkFrame(self)
        self.frame_6.grid(row=0, column=1, rowspan=4, sticky="nsew")
        
        tabview = customtkinter.CTkTabview(master=self.frame_6,width=800,height=600)
        tabview.pack(padx=20, pady=20)
        tabview.add("设置板块")  # add tab at the end
        
        tabview.set("设置板块")  # set currently visible tab
        customtkinter.CTkLabel(master=tabview.tab("设置板块"),text="本机机器码:").pack()
        output1=customtkinter.CTkTextbox(master=tabview.tab("设置板块"),height=25,width=400)
        output1.pack()
        output1.insert("end",regInstance.machine_code)
        
        customtkinter.CTkLabel(master=tabview.tab("设置板块"),text="下方输入注册码:").pack()
        input1=customtkinter.CTkTextbox(master=tabview.tab("设置板块"),height=75,width=400)
        input1.pack()
        button = customtkinter.CTkButton(master=tabview.tab("设置板块"),text="注册",command=lambda:self.register(input1.get("1.0", "end")))
        button.pack(padx=20, pady=20)

        openConfigButton=customtkinter.CTkButton(master=tabview.tab("设置板块"),text='打开基础配置表',command=lambda:os.startfile(settings.PATH_CONFIG+"\配置文件.xlsx"))
        openConfigButton.pack(padx=20, pady=20)


    def open_input_dialog_event(self):
        dialog = customtkinter.CTkInputDialog(text="Type in a number:", title="CTkInputDialog")
        print("CTkInputDialog:", dialog.get_input())

    def sidebar_button_event(self,event,params=None):
        text=event.cget("text")
        if text=="资金板块":
            self.frame_1.tkraise()
        elif text=="税务板块":
            self.frame_2.tkraise()
        elif text=="一体化快捷":
            self.frame_3.tkraise()
        elif text=="数据板块":
            self.frame_4.tkraise()
        elif text=="结账板块":
            self.frame_5.tkraise()
        elif text=="设置板块":
            self.frame_6.tkraise()

    def register(self,inputCode):
        #print("注册码:"+inputCode)
        inputCode=inputCode.strip()
        regInstance.regester(inputCode)
        tkinter.messagebox.showinfo("提示","注册成功")
        self.labelRegister._text="本机到期时间:"+regInstance.expire_time

    def runProcess(self,event,params=None):
        text=event.cget("text")
        if params==None:
            paramsDict={"功能":text}
        else:
            paramsDict={"功能":text,"参数":params}
        
        try:
            thisProcess.p.terminate()
        except:
            pass

        thisProcess.run(paramsDict)
        def change():
            while ifThreadmsg:
                sharedDcit=shared_queue.get()
                self.textBox.delete('1.0',tkinter.END)
                self.textBox.insert("end",sharedDcit["消息"])
                time.sleep(0.01)
        t1=threading.Thread(target=change)
        t1.daemon=True
        t1.start()
  

def main():
    app = App()
    app.mainloop()
