
from playwright.sync_api import Playwright, sync_playwright
import time
import pythoncom
import src.utils.cscec as cscec
import src.utils.Excel.excel as excel
import src.utils.Browser.Browser as browser
from src.utils.DB.midIntrSQLiteDB import excelDB
import pandas as pd
from datetime import datetime,timedelta # 获取当前日期
def label_question(s):
    s="//label[contains(text(),'"+s+"')]/parent::div/following-sibling::div[1]/div/div/div"
    return s
def autofill():
    db=excelDB()
    df=db.getDataframe("批量暂估")
    page = browser.myBrowser("cscec").page
    for i,row in df.iterrows():
            if row["是否"]=="是":
                db.updateData("批量暂估","是否","开始执行",i+1)
                d=row.to_dict()
                print("开始第"+str(i+1)+"行")
                cscec.changeProjectCscec(page,row["组织机构"],row["项目名称"])
                #先切换项目
                page.locator("//*[@id='root']/section/section/aside/div/div/div[2]/div/div[1]/ul[3]/li/span").click()
                page.locator("//span[contains(text(),'应付及付款')]").click()
                if row["合同类型"].find("专业分包")<0:
                    page.locator("//span[contains(@class,'txt')][contains(text(),'结算单-直接人工费')]").click() 
                else:
                    page.locator("//span[contains(@class,'txt')][contains(text(),'结算单-(总)分包')]").click() 
                page.get_by_placeholder("事由不允许超过").fill(row["事由"]) 

                s="//label[contains(text(),'含原始纸质附件：')]/parent::div/following-sibling::div[1]/div/div/div"
                page.locator(s).click()
                cscec.getVisible(page,"//div[text()='否']/following-sibling::div[text()='是']/preceding-sibling::div[1]").click()
                
                page.locator(label_question("合同名称")).click()
                page.get_by_placeholder("请输入查询合同编号关键字").fill(row["合同编号"])
                page.locator("//*[text()='合同帮助']/parent::div/parent::div/parent::div//span[contains(text(),'查询')]/parent::div/parent::div").click()
                page.get_by_text(row["合同编号"]).first.dblclick()
                
                page.locator(label_question("结算类型")).click()
                page.get_by_text("暂估结算").click()

                page.locator(label_question("发票种类")).click()
                page.get_by_text("其它").click()

                page.locator(label_question("当前付款条件")).click()
                page.locator("//div[text()='账期信息帮助']/parent::div/parent::div/parent::div//div[text()='"+str(int(row["付款比例"]))+".00%'"+"]/parent::td").first.dblclick()

                page.locator(label_question("付款类别")).click()
                page.get_by_placeholder("请输入查询关键字").click()
                page.get_by_placeholder("请输入查询关键字").fill("暂估")
                page.locator("//*[text()='帮助字典']/parent::div/parent::div/parent::div//span[contains(text(),'查询')]/parent::div/parent::div").click()
                page.get_by_text("应付暂估-").dblclick()

                if row['暂估成本科目']!="" and row['暂估成本科目'] is not None and (not pd.isna(row['暂估成本科目'])):
                    page.locator(label_question("成本费用事项")).click()
                    page.get_by_placeholder("请输入查询关键字").fill(row["暂估成本科目"])
                    page.locator("//*[text()='帮助字典']/parent::div/parent::div/parent::div//span[contains(text(),'查询')]/parent::div/parent::div").click()
                    page.get_by_text(row["暂估成本科目"]).dblclick()


                page.locator(label_question("率")).click()
                page.get_by_placeholder("请输入查询关键字").fill("零税率")
                page.locator("//*[text()='帮助字典']/parent::div/parent::div/parent::div//span[contains(text(),'查询')]/parent::div/parent::div").click()
                page.get_by_text("零税率").dblclick()


                ctable=cscec.cscecTable(page,"本期结算")
                ctable.fillInput(1,"本期结算",str(row["金额"]))
                
                if row["金额"]>0 :
                    ctable.fillInput(1,"上期累计","-"+str(row["金额"]))#上期累计
                
                ctable=cscec.cscecTable(page,"付款计算方式")
                ctable.reIndex()
                ctable_count=ctable.count
                money=0

                current_date = datetime.now()-timedelta(days=20)#注意每次修改下
                formatted_date = current_date.strftime("%Y-%m-%d") # 格式化日期


                if ctable_count>2:
                    for j in range(ctable_count-1):
                        ctable.getValue(j+1,"*本期确认金额")
                        money=money+float(page.locator(s).text_content().replace(',',''))
                    ctable.fillInput(ctable_count,"*本期确认金额",str(row["金额"]-money)) #调整金额0.01
                for i in range(ctable_count):
                    ctable.fillInput(i+1,"*结算日期",formatted_date)
                    ctable.fillInput(i+1,"*付款期(天)","1000")
                page.locator("//span[text()='保存']/parent::div/parent::div/parent::div/parent::div").click()
                page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='确定']").click()
                page.locator("//span[text()='保存']/parent::div/parent::div/parent::div/parent::div/parent::div//span[text()='提交']/parent::div/parent::div/parent::div/parent::div").click() #通过保存来确定提交按钮
                #page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='提交']").click()
                page.locator("//*[text()='处理意见']/parent::div/parent::div/parent::div//div[text()='同意' or text()='确定']").click()
                page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='确定']").click()
                page.locator("//button[@aria-label='remove']//span[@aria-label='close']//*[name()='svg']//*[name()='path' and contains(@d,'M563.8 512')]").click() #关闭结算单
                db.updateData("批量暂估","是否","执行完成",i+1)
    print("完成任务")