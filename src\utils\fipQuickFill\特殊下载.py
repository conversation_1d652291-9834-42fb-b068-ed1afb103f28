
from playwright.sync_api import Play<PERSON>, sync_playwright,Page
import src.utils.cscec as cscec
import src.utils.Excel.excel as excel
import time
import fitz
from tkinter import filedialog
def pdf_wirte(s,text):
    with fitz.open(s) as doc:
        for THEpage in doc:
                try:
                    font_size = 16
                    x = 20
                    y = 20
                    THEpage.insert_text(fitz.Point(x, y), text, fontname='helv', fontsize=font_size)
                except:
                    THEpage
        doc.saveIncr()


def download(page:Page,savePath,voucherNumber,order):
    processCode=page.locator(f"//td/div[text()='{voucherNumber}']/parent::td/parent::tr/td[5]").text_content()
    page.locator(f"//td/div[text()='{voucherNumber}']/parent::td/parent::tr/td[5]").click()
    print("正在下载")
    try:
        page.locator("//span[text()='退出']/parent::div/parent::div/parent::div/parent::div/parent::div//span[text()='打印']").click(timeout=1000)
    except:
        try:
            page.locator("//*[text()='系统提示']//parent::div/parent::div/parent::div//span[text()='确 定']").click(timeout=500) #这里的狗血确认有个空格
            page.locator("//span[text()='退出']/parent::div/parent::div/parent::div/parent::div/parent::div//span[text()='打印']").click(timeout=500)
        except:
            page.locator("//span[text()='退出']/parent::div/parent::div/parent::div/parent::div/parent::div//span[text()='打印']").click()
    #不具备独一性，利用附件按钮反向查找
    if processCode[:3]=="JFK" or processCode[:3]=="CFK" or processCode[:3]=="TBZ":
        page.locator("//*[text()='模板选择窗口']/parent::div/parent::div/parent::div//span[text()='确定']").click()
    frame=page.frame_locator("//*[@id='FormPdfJsPage1']")
    tryCount=60
    while tryCount>0:
        try:
            with page.expect_download(timeout=1000) as download_info:
                frame.locator("//button[@id='download']").click()
                download = download_info.value
                path = download.path()
                download.save_as(path=savePath+f"/{order}a流程单.pdf")
                tryCount=-1
        except:
            tryCount=tryCount-1
            time.sleep(0.3)  
    page.locator("//div[contains(@class,'ant-tabs-tab ant-tabs-tab-with-remove ant-tabs-tab-active')]//span[contains(@aria-label,'close')]//*[name()='svg']").click()
    page.locator("//div[@class='ant-tabs-tab ant-tabs-tab-with-remove ant-tabs-tab-active']//span[@aria-label='close']//*[name()='svg']").click()  
    pdf_wirte(savePath+f"/{order}a流程单.pdf",str(order))

def spDownload():
    ws=excel.myBook().sheet("Sheet")
    savePath=filedialog.askdirectory()
    with sync_playwright() as playwright:
        browser = playwright.chromium.connect_over_cdp("http://localhost:9222")
        default_context = browser.contexts[0]
        page = cscec.switch_to_page(default_context,"中国建筑司库")
        for i in range(2,ws.MaxRow+1):
            if ws.Cells(i,1) != None:
                if type(ws.Cells(i,1).Value)==float:
                    voucherNumber=str(int(ws.Cells(i,1).Value))
                else:
                    voucherNumber=ws.Cells(i,1).Value
                download(page,savePath,voucherNumber,ws.Cells(i,2).Value)