import os
import sys

def initRuntimeEnvironment(startup_script):
    """初始化运行环境。startup_script: 启动脚本路径"""

    import site
    # 定义一个最简单的消息弹窗
    # 初始化工作目录和Python搜索路径
    script = os.path.abspath(startup_script)  # 启动脚本.py的路径
    home = os.path.dirname(script)  # 工作目录
    os.chdir(home)  # 重新设定工作目录（不在最顶层，而在UmiOCR-data文件夹下）
    for n in ['.', '.site-packages']:  # 将模块目录添加到 Python 搜索路径中
        path = os.path.abspath(os.path.join(home, n))
        if os.path.exists(path):
            site.addsitedir(path)


initRuntimeEnvironment(__file__)  # 初始化运行环境

from playwright.sync_api import Playwright, sync_playwright
import win32com.client
import time
import pythoncom
def label_question(s):
    s="//label[contains(text(),'"+s+"')]/parent::div/following-sibling::div[1]/div/div/div"
    return s
def autofill():
    pythoncom.CoInitialize()
    try:
        et = win32com.client.Dispatch("Excel.Application")
    except:
        et = win32com.client.Dispatch("Ket.Application")
    wb=et.Workbooks("自动导入.xlsm")
    ws=wb.Worksheets("分包录入")
    ws.Cells(1,1).Value=ws.UsedRange.Rows.Count
    b=int(ws.Cells(1,5).Value)+4
    #ws.UsedRange.Rows.Count+1
    for i in range(b,ws.UsedRange.Rows.Count+1):
        if stopSignal > 0 :
             with sync_playwright() as playwright:
                print("开始序号"+str(i-4)+"行")
                if ws.Cells(i,12).Value=="是":
                    browser = playwright.chromium.connect_over_cdp("http://localhost:9522")
                    default_context = browser.contexts[0]
                    page = default_context.pages[0]
                    #先切换项目
                    if ws.Cells(i,3).Value!=ws.Cells(i-1,3).Value:
                        page.click("//img[@src='https://fip.cscec.com/OSPPortal/images/osp/app/menu/csc/qhzz.png']")

                        s="//label[contains(text(),'组织机构：')]/parent::div/following-sibling::div[1]//input"
                        judge=page.locator(s).get_attribute("value")

                        page.click("//*[@id='DataSetFieldComboBox1-input']/following-sibling::div[1]") #选择公司同级
                        page.get_by_placeholder("请输入查询关键字").fill(ws.Cells(i,2).Value)
                        page.locator("//div[contains(@class,'EI6JOB-C-g')]//span[contains(text(),'查询')]/parent::div/parent::div/parent::div").nth(1).click()
                        page.locator("//*[text()='组织机构']/parent::div/parent::div/parent::div//div[text()='"+ws.Cells(i,2).Value+"']").click()
                        page.locator("//*[text()='组织机构']/parent::div/parent::div/parent::div//div[contains(text(),'确定')]").click()

                        page.click("//*[@id='DataSetFieldComboBox3-input']/following-sibling::div[1]") #选择项目同级
                        page.get_by_placeholder("请输入查询关键字").fill(ws.Cells(i,4).Value)
                        page.locator("//div[contains(@class,'EI6JOB-C-g')]//span[contains(text(),'查询')]/parent::div/parent::div/parent::div").nth(1).click()
                        page.get_by_text(ws.Cells(i,4).Value, exact=True).click()
                        page.locator("//*[text()='项目名称']/parent::div/parent::div/parent::div//div[contains(text(),'确定')]").click()

                        page.click("//*[@id='DataSetFieldComboBox2-input']/following-sibling::div[1]") #选择项目同级
                        page.locator("//*[text()='部门名称']/parent::div/parent::div/parent::div//div[contains(text(),'XN')]").click() #XN0+公司编码
                        page.locator("//*[text()='部门名称']/parent::div/parent::div/parent::div//div[contains(text(),'确定')]").click()

                        page.locator("//*[text()='切换组织机构']/parent::div/parent::div/parent::div//div[contains(text(),'确定')]").click()
                        try:
                            page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[contains(text(),'确定')]").click()
                        except:
                            print("无提示")
                        #切换项目完成

        
                    page.locator("//*[@id='root']/section/section/aside/div/div/div[2]/div/div[1]/ul[3]/li/span").click()
                    page.locator("//span[contains(text(),'应付及付款')]").click()
                    if ws.Cells(i,9).Value.find("专业")<0:
                        page.locator("//span[contains(@class,'txt')][contains(text(),'结算单-直接人工费')]").click() 
                    else:
                        page.locator("//span[contains(@class,'txt')][contains(text(),'结算单-(总)分包')]").click() 
                    page.get_by_placeholder("事由不允许超过").fill(ws.Cells(i,5).Value) 
                    page.locator(label_question("合同名称")).click()
                    page.get_by_placeholder("请输入查询合同编号关键字").fill(ws.Cells(i,8).Value)
                    page.locator("//*[text()='合同帮助']/parent::div/parent::div/parent::div//span[contains(text(),'查询')]/parent::div/parent::div").click()
                    page.get_by_text(ws.Cells(i,8).Value).first.dblclick()
                    
                    page.locator(label_question("结算类型")).click()
                    page.locator("//div[contains(text(),'暂估结算')]/preceding-sibling::div[2]").click()


                    page.locator(label_question("付款类别")).click()
                    page.get_by_placeholder("请输入查询关键字").click()
                    page.get_by_placeholder("请输入查询关键字").fill("应付账款-")
                    page.locator("//*[text()='帮助字典']/parent::div/parent::div/parent::div//span[contains(text(),'查询')]/parent::div/parent::div").click()
                    page.get_by_text("应付账款-").dblclick()

                    page.locator(label_question("发票种类")).click()
                    if ws.Cells(i,15).Value=="一般":
                        page.get_by_text("增值税专用发票", exact=True).click()
                    else:
                        page.get_by_text("增值税普通发票", exact=True).click()

                    page.locator(label_question("当前付款条件")).click()
                    page.get_by_text(str(int(ws.Cells(i,10).Value))+".0000%").first.dblclick()


                    s="//div[contains(text(),'本期结算')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tr[1]/td[4]"
                    page.locator(s).click()
                    page.locator("//div[@class='EI6JOB-t-f']//div//input[@id='undefined-input']").fill(str(ws.Cells(i,6).Value)) #输入结算额
                    
                    s="//div[contains(text(),'本期结算')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tr[2]/td[4]"
                    page.locator(s).click() #换区域点击保存数值

                    s="//div[contains(text(),'安全生产经费用途')]//parent::span/parent::div/parent::td/preceding-sibling::td[2]" #通过科目编号找到增加按钮
                    page.locator(s).click()
                    page.get_by_text("增加").click() #增加安全生产经费
                    s="//div[contains(text(),'安全生产经费用途')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tbody[2]/tr[1]/td[3]"
                    page.locator(s).dblclick()
                    s="//div[contains(text(),'安全生产经费用途')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//div[@class='EI6JOB-v-f']//input/following-sibling::div[1]"
                    page.locator(s).click() 
                    page.get_by_placeholder("请输入查询关键字").fill("0220")
                    page.locator("//*[text()='帮助字典']/parent::div/parent::div/parent::div//span[contains(text(),'查询')]/parent::div/parent::div").click()
                    page.get_by_text("0220",exact=True).first.dblclick() #选择分包
                    s="//div[contains(text(),'安全生产经费用途')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tbody[2]/tr[1]/td[4]"
                    page.locator(s).click()
                    page.locator(s).dblclick()
                    page.locator("//div[@class='EI6JOB-t-f']//div//input[@id='undefined-input']").fill("2.00")
                    s="//div[contains(text(),'安全生产经费用途')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tbody[2]/tr[1]/td[5]"
                    page.locator(s).dblclick()
                    page.locator("//div[@class='EI6JOB-t-f']//div//input[@id='undefined-input']").fill(str(ws.Cells(i,7).Value))
                    s="//div[contains(text(),'安全生产经费用途')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tbody[2]/tr[1]/td[6]"
                    page.locator(s).dblclick()
                    page.locator("//div[@class='EI6JOB-t-f']//div//input[@id='undefined-input']").fill(str(ws.Cells(i,7).Value*0.015))

                    #导入附件
                    page.locator("//span[text()='附件']/parent::div/parent::div/parent::div/parent::div").click()
                    with page.expect_file_chooser() as fc_info:
                        page.locator("//*[text()='附件窗口']/parent::div/parent::div/parent::div//span[contains(text(),'上传')]").click() #点击input才有效果
                        file_chooser = fc_info.value
                        file_chooser.set_files(ws.Cells(i,13).Value.replace('\u202a', ''))
                        exit_number=7
                        while exit_number>0:
                            try:
                                page.locator("//*[text()='附件上传成功']/parent::div/parent::div//div[text()='确定']").nth(1).click() 
                                page.locator("//*[text()='附件上传成功']/parent::div/parent::div//div[text()='确定']").nth(0).click() 
                                exit_number=-1
                            except:
                                exit_number=exit_number-1

                        page.locator("//*[text()='附件窗口']/preceding-sibling::div[1]//td[3]").click()


                    s="//div[contains(text(),'付款计算方式')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tr"
                    c_number=page.locator(s).count()
                    money=0

                    from datetime import datetime # 获取当前日期
                    current_date = datetime.now()
                    formatted_date = current_date.strftime("%Y-%m-%d") # 格式化日期


                    if c_number>2:
                        for j in range(1,c_number-1):
                            s="//div[contains(text(),'付款计算方式')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tr["+str(j)+"]/td[9]/div"
                            money=money+float(page.locator(s).text_content().replace(',',''))
                        s="//div[contains(text(),'付款计算方式')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tr["+str(c_number-1)+"]/td[9]"
                        page.locator(s).click()
                        page.locator("//div[@class='EI6JOB-t-f']//div//input[@id='undefined-input']").fill(str(ws.Cells(i,6).Value-money)) #调整金额0.01
                    for k in range(1,c_number):
                        s="//div[contains(text(),'付款计算方式')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tr["+str(k)+"]/td[10]"
                        page.locator(s).click()
                        page.locator("//div[@class='EI6JOB-J-f']//div//input[@id='undefined-input']").fill(formatted_date) #批量写入日期
    
                    page.locator("//span[text()='保存']/parent::div/parent::div/parent::div/parent::div").click()
                    page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='确定']").click()
                    page.locator("//span[text()='保存']/parent::div/parent::div/parent::div/parent::div/parent::div//span[text()='提交']/parent::div/parent::div/parent::div/parent::div").click() #通过保存来确定提交按钮
                    page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='提交']").click()
                    page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='确定']").click()
                    page.locator("//button[@aria-label='remove']//span[@aria-label='close']//*[name()='svg']//*[name()='path' and contains(@d,'M563.8 512')]").click() #关闭结算单
                
                ws.Cells(1,5).Value=ws.Cells(1,5).Value+1
        else:
            break
    print("完成任务")
import PySimpleGUI as sg
import threading

stopSignal = 1
t1 = threading.Thread(target=autofill)
t1.start()


# 定义界面布局
layout = [[sg.Button('重新开始')],
          [sg.Button('停止')],
          ]

# 创建窗口
window = sg.Window('自动填充', layout)

while True:
    event, values = window.read()
    
    if event == sg.WINDOW_CLOSED:
        stopSignal = -1
        break
    if event == '重新开始':
        stopSignal = 1
        t1 = threading.Thread(target=autofill)
        t1.start()
        #download(stopSignal)
    if event == '停止':
        stopSignal = -2
window.close()