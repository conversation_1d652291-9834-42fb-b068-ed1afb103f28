import flet as ft
import src.Gui.callProcess as callF
import src.base.settings as settings
from datetime import datetime


class downloadPlanView(ft.Container):
    def __init__(self):
        super().__init__()
        self.padding = 20
        self.expand = True
        self.border_radius = 15
        self.bgcolor = ft.colors.SURFACE_VARIANT
        self.margin = ft.margin.only(bottom=20)
        
        self.combobox1 = ft.Dropdown(
            label="选择下载批次", 
            options=[ft.dropdown.Option(cell) for cell in ["正常计划","追加计划第一次","追加计划第二次","所有批次"]],
            border_radius=10,
            filled=True,
            bgcolor=ft.colors.SURFACE_VARIANT,
        )
        self.combobox2 = ft.Dropdown(
            label="选择状态", 
            options=[ft.dropdown.Option(cell) for cell in ["结束","流程中","所有状态"]],
            border_radius=10,
            filled=True,
            bgcolor=ft.colors.SURFACE_VARIANT,
        )
        self.button1 = ft.ElevatedButton(
            text="下载一体化计划(重新开始)",
            icon=ft.icons.DOWNLOAD,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
            ),
            on_click=lambda e:callF.thisProcess.run({"功能":"下载一体化计划重新开始","参数":[self.combobox1.value,self.combobox2.value]})
        )
        self.button2 = ft.ElevatedButton(
            text="下载一体化计划(继续未完成)",
            icon=ft.icons.DOWNLOAD_DONE,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
            ),
            on_click=lambda e:callF.thisProcess.run({"功能":"下载一体化计划追加","参数":[self.combobox1.value,self.combobox2.value]})
        )
        
        self.content = ft.Column(
            controls=[
                ft.Text("下载一体化计划", size=16, weight=ft.FontWeight.BOLD, color=ft.colors.PRIMARY),
                ft.Row(
                    controls=[self.combobox1, self.combobox2],
                    alignment=ft.MainAxisAlignment.SPACE_AROUND,
                ),
                ft.Row(
                    controls=[
                        ft.Container(
                            content=self.button1,
                            padding=10,
                            alignment=ft.alignment.center,
                        ),
                        ft.Container(
                            content=self.button2,
                            padding=10,
                            alignment=ft.alignment.center,
                        ),
                    ],
                    alignment=ft.MainAxisAlignment.SPACE_AROUND,
                ),
            ],
            spacing=15,
        )

class uploadPlanView(ft.Container):
    def __init__(self):
        super().__init__()
        self.padding = 20
        self.border_radius = 15
        self.bgcolor = ft.colors.SURFACE_VARIANT
        self.margin = ft.margin.only(bottom=20)
        
        self.button1 = ft.ElevatedButton(
            text="获取上传模板",
            icon=ft.icons.FILE_DOWNLOAD,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
            ),
            on_click=lambda e:callF.thisProcess.run({"功能":"获取上传模板"})
        )
        self.combobox1 = ft.Dropdown(
            label="选择上报批次", 
            options=[ft.dropdown.Option(cell) for cell in ["正常计划","追加计划第一次","追加计划第二次"]],
            border_radius=10,
            filled=True,
            bgcolor=ft.colors.SURFACE_VARIANT,
            width=200,
        )
        self.button2 = ft.ElevatedButton(
            text="上报一体化计划",
            icon=ft.icons.UPLOAD_FILE,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
            ),
            on_click=lambda e:callF.thisProcess.run({"功能":"下载一体化计划追加","参数":[self.combobox1.value]})
        )
        
        self.content = ft.Column(
            controls=[
                ft.Text("上传一体化计划", size=16, weight=ft.FontWeight.BOLD, color=ft.colors.PRIMARY),
                ft.Container(
                    content=ft.Row(
                        controls=[
                            ft.Container(
                                content=self.combobox1,
                                padding=10,
                                alignment=ft.alignment.center,
                            ),
                            ft.Container(
                                content=self.button1,
                                padding=10,
                                alignment=ft.alignment.center,
                            ),
                            ft.Container(
                                content=self.button2,
                                padding=10,
                                alignment=ft.alignment.center,
                            ),
                        ],
                        alignment=ft.MainAxisAlignment.SPACE_AROUND,
                    ),
                    padding=10,
                    alignment=ft.alignment.center,
                ),
            ],
            spacing=15,
        )



class fundTabView(ft.Column):
    def __init__(self):
        super().__init__()
        self.visible = True
        self.expand = True
        #self.spacing=5
        self.navigation_bar = ft.NavigationBar(
            destinations=[
                ft.NavigationBarDestination(icon=ft.icons.NEXT_PLAN, label="资金计划"),
                ft.NavigationBarDestination(icon=ft.icons.PAGES, label="资金常用"),
            ],
            on_change=lambda e:self.changeMain(e)
        )

        self.mainRegional=ft.Column(controls=[],alignment=ft.MainAxisAlignment.CENTER)
        self.controls=(self.navigation_bar,self.mainRegional)
        self.default=[downloadPlanView(), uploadPlanView()]
        self.mainRegional.controls=self.default


    def changeMain(self,e):
        index=e.control.selected_index
        if index==1:
            self.mainRegional.controls=[
                ft.Container(
                    content=ft.Column(
                        controls=[
                            ft.Container(
                                content=ft.Column(
                                    controls=[
                                        ft.Text("单据管理", size=16, weight=ft.FontWeight.BOLD, color=ft.colors.PRIMARY),
                                        ft.Row(
                                            controls=[
                                                ft.Container(
                                                    content=ft.Row(
                                                        controls=[
                                                            ft.Container(
                                                                content=ft.ElevatedButton(
                                                                    text="查询在途单据数据库",
                                                                    icon=ft.icons.DOWNLOAD,
                                                                    style=ft.ButtonStyle(
                                                                        shape=ft.RoundedRectangleBorder(radius=10),
                                                                        padding=15,
                                                                    ),
                                                                    on_click=lambda e:callF.thisProcess.run({"功能":"查询在途单据数据库"})
                                                                ),
                                                                padding=10,
                                                                alignment=ft.alignment.center,
                                                            ),
                                                            ft.Container(
                                                                content=ft.ElevatedButton(
                                                                    text="更新在途单据数据库",
                                                                    icon=ft.icons.UPDATE,
                                                                    style=ft.ButtonStyle(
                                                                        shape=ft.RoundedRectangleBorder(radius=10),
                                                                        padding=15,
                                                                    ),
                                                                    on_click=lambda e:callF.thisProcess.run({"功能":"更新在途单据数据库"})
                                                                ),
                                                                padding=10,
                                                                alignment=ft.alignment.center,
                                                            ),
                                                            ft.Container(
                                                                content=ft.ElevatedButton(
                                                                    text="同步一体化在途单据",
                                                                    icon=ft.icons.EXPLORE,
                                                                    style=ft.ButtonStyle(
                                                                        shape=ft.RoundedRectangleBorder(radius=10),
                                                                        padding=15,
                                                                    ),
                                                                    on_click=lambda e:callF.thisProcess.run({"功能":"同步一体化在途单据"})
                                                                ),
                                                                padding=10,
                                                                alignment=ft.alignment.center,
                                                            ),
                                                        ],
                                                        spacing=10,
                                                    ),
                                                    padding=10,
                                                    alignment=ft.alignment.center,
                                                )
                                            ],
                                            alignment=ft.MainAxisAlignment.START,
                                            spacing=20,
                                        ),
                                    ],
                                    spacing=15,
                                ),
                                padding=20,
                                border_radius=15,
                                bgcolor=ft.colors.SURFACE_VARIANT,
                                margin=ft.margin.only(bottom=20),
                            ),
                        ],
                        spacing=15,
                    ),
                    padding=20,
                ),
            ]
        elif index==0:
            self.mainRegional.controls=self.default
       
        self.mainRegional.update()