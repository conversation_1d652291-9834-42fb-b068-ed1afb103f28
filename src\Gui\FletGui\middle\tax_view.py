import flet as ft
import src.Gui.callProcess as callF

class taxTabView(ft.Column):
    def __init__(self):
        super().__init__()
        self.visible = True
        self.expand = True
        
        self.navigation_bar = ft.NavigationBar(
            destinations=[
                ft.NavigationBarDestination(icon=ft.icons.RECEIPT_LONG, label="税务管理"),
            ],
            on_change=lambda e:self.changeMain(e)
        )
        
        self.mainRegional = ft.Column(
            controls=[],
            alignment=ft.MainAxisAlignment.CENTER,
            scroll=ft.ScrollMode.AUTO,
            expand=True,
        )
        
        self.controls = [
            self.navigation_bar,
            self.mainRegional,
        ]

        self.default= ft.Container(
                content=ft.Column(
                    controls=[
                        ft.Text("小额税金调整", size=20, weight=ft.FontWeight.BOLD, color=ft.colors.PRIMARY),
                        ft.Row(
                            controls=[
                                ft.Container(
                                    content=ft.ElevatedButton(
                                        text="查询小额税金调整模板",
                                        icon=ft.icons.DOWNLOAD,
                                        style=ft.ButtonStyle(
                                            shape=ft.RoundedRectangleBorder(radius=10),
                                            padding=15,
                                        ),
                                        on_click=lambda e:callF.thisProcess.run({"功能":"查询小额税金调整模板"})
                                    ),
                                    padding=10,
                                    alignment=ft.alignment.center,
                                ),
                                ft.Container(
                                    content=ft.ElevatedButton(
                                        text="更新小额税金调整模板",
                                        icon=ft.icons.UPDATE,
                                        style=ft.ButtonStyle(
                                            shape=ft.RoundedRectangleBorder(radius=10),
                                            padding=15,
                                        ),
                                        on_click=lambda e:callF.thisProcess.run({"功能":"更新小额税金调整模板"})
                                    ),
                                    padding=10,
                                    alignment=ft.alignment.center,
                                ),
                                ft.Container(
                                    content=ft.ElevatedButton(
                                        text="执行小额税金调整",
                                        icon=ft.icons.EXPLORE,
                                        style=ft.ButtonStyle(
                                            shape=ft.RoundedRectangleBorder(radius=10),
                                            padding=15,
                                        ),
                                        on_click=lambda e:callF.thisProcess.run({"功能":"执行小额税金调整"})
                                    ),
                                    padding=10,
                                    alignment=ft.alignment.center,
                                ),
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_AROUND,
                        ),
                    ],
                    spacing=15,
                ),
                padding=20,
                border_radius=15,
                bgcolor=ft.colors.SURFACE_VARIANT,
                margin=ft.margin.only(bottom=20),
            )
        
        # 初始化主区域内容
        self.mainRegional.controls = [self.default]
    
    def changeMain(self, e):
        index = e.control.selected_index
        if index == 1:
            # 税务管理
            self.mainRegional.controls = [
            ]
        else:
            self.mainRegional.controls = self.default
        self.mainRegional.update() 