import os
import sys

sys.path.append(".")
import src.utils.Excel.excel as excel
mysheet=excel.myBook().sheet()
title=mysheet.Range(1,1,2,mysheet.MaxCol).Value
print(mysheet.getSheetName())
print(mysheet.getSelectAddress())


import os
from pathlib import Path
from python_calamine import CalamineWorkbook


def get_excel_data(folder_path, sheet_name, cell_reference):
    # 获取文件夹下所有Excel文件
    excel_files = [f for f in os.listdir(folder_path) if f.endswith('.xlsx') or f.endswith('.xls')]
    
    result = []
    
    for file in excel_files:
        file_path = os.path.join(folder_path, file)
        workbook = CalamineWorkbook.from_path(file_path)
        sheet = workbook.get_sheet_by_name(sheet_name)
        
        if sheet:
            # 将单元格引用转换为行和列的索引
            row, col = cell_reference_to_indices(cell_reference)
            data = sheet.to_python()
            cell_value = data[row]
            file_name = Path(file).stem  # 获取不含后缀的文件名
            cell_value.insert(0,file_name)
            result.append(cell_value)
    
    return result

def cell_reference_to_indices(cell_reference):
    # 将类似"A1"的单元格引用转换为行和列的索引
    col_part = ''.join(filter(str.isalpha, cell_reference))
    row_part = ''.join(filter(str.isdigit, cell_reference))
    
    col = 0
    for i, char in enumerate(col_part[::-1]):
        col += (ord(char.upper()) - ord('A') + 1) * (26 ** i)
    
    row = int(row_part) - 1
    col = col - 1
    
    return row, col

# 示例调用
folder_path = "D:\数字公司总部\报表部分\财务中报--年报安装2023年报"  # 指定文件夹路径
sheet_name = mysheet.getSheetName()  # 指定表格名
cell_reference = mysheet.getSelectAddress()  # 指定引用单元格



getdata = get_excel_data(folder_path, sheet_name, cell_reference)
title[0].insert(0,"文件名")
title[1].insert(0,"无")
theOutputSheet=excel.myBook("输出表").sheet("查找结果")
theOutputSheet.Range("a1").resize(len(title),len(title[0])).Value=title
theOutputSheet.Range("a3").resize(len(getdata),len(getdata[0])).Value=getdata