
from playwright.sync_api import Playwright, sync_playwright
import win32com.client
import time
import pythoncom
import src.utils.cscec as cscec
import src.utils.Excel.excel as excel
import src.utils.Excel.openpyxlExcel as openpyxlExcel
import src.utils.Browser.Browser as browser


def autofill():
    wb=excel.myBook()
    ws=wb.sheet("应收台账生成")
    excelTable=ws.table("表确权收款4")
    b=int(ws.Cells(1,2).Value)+1
    dopId=openpyxlExcel.getDict("财商配置")
    theBrowser=browser.myBrowser("3b",dopId["财商账号"],dopId["财商密码"])
    page=theBrowser.page
    theBrowser.goCscec3bReceivableLedger()
    time.sleep(3)          
    for i in range(b,excelTable.MaxRow+1):
            print("开始序号"+str(i-1)+"行")
            if excelTable.getValue(i,"是否生成台账")=="是":
                page.click("//button[@class='ant-btn ant-btn-primary list-action-add']") #点击新增
                frame=page.frame_locator("//*[@id='portal-form-list-container']/div[4]/iframe")
                frame.locator("//*[@id='project']/div[2]/div/div/div/div/span/input").click()
                frame.locator("//div[@class='ant-col ant-col-12']//div[@id='projectName']//div[@class='field__control']//div//div[@class='text']//div//input[@type='text']").fill(excelTable.getValue(i,"项目名称"))
                frame.locator("//div[@class='ant-modal-body']//button[2]").click()
                frame.locator("//div[@class='ant-modal-body']//button[2]").click()
                projectNumber=excelTable.getValue(i,"项目名称")
                cscec.getVisible(frame,f"//*[@id='list-selector']//div[text()='{projectNumber}']").click()
                frame.locator("//div[@class='ant-modal-footer']//button[2]").click()  #选择项目完成

                frame.locator("//input[@class='ant-calendar-picker-input ant-input']").click()
                if excelTable.getValue(i,"是否调整到前一年")=="是":
                    frame.locator("//div[@class='ant-calendar-month-panel']//a[@title='上一年 (Control键加左方向键)']").click()
                frame.get_by_text(excelTable.getValue(i,"台账月份"),exact=True).click() #注意月份
                time.sleep(1)
                textName=frame.locator("//*[@title='回款责任人']/parent::div/following-sibling::div").text_content()
                print(textName)
                if textName=="请选择":
                    frame.locator("//*[@title='回款责任人']/parent::div/following-sibling::div").first.click()
                    frame.locator("//input[@placeholder='搜索组织、姓名']").fill(excelTable.getValue(i,"回款责任人"))
                    frame.locator("//i[@aria-label='图标: search']").click()
                    frame.locator("//span[contains(text(),'搜索结果:')]/parent::div/parent::div").get_by_text(excelTable.getValue(i,"回款责任人")).first.click()
                    frame.locator("//div[contains(text(),'已选择：')]/preceding-sibling::div//span[contains(text(),'确 定')]").click()

                frame.locator("//button/span[text()='保 存']/parent::button").click()#点击保存
                time.sleep(1)
                
            ws.Cells(1,2).Value=ws.Cells(1,2).Value+1
    time.sleep(5) #最后一条保存不了

