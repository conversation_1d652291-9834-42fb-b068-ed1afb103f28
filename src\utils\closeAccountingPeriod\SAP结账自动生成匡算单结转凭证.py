
from playwright.sync_api import Playwright, sync_playwright
import win32com.client
import src.utils.sapPublic.GetSAPSession as GetSAPSession
from src.utils.DB.midIntrSQLiteDB import excelDB
def autofill():
    db=excelDB()
    df=db.getDataframe("独立结账模板收入成本")
    session = GetSAPSession.creatSAP()
    session.StartTransaction("ZARAP0003")
    for i,row in df.iterrows():
        d=row.to_dict()
        if d["SAP最终过账是否"]=="是":
            print("开始序号"+str(i+1)+"行")
            db.updateData("独立结账模板收入成本","SAP最终过账是否","开始执行",i+1)
            session.findById("wnd[0]/usr/ctxtS_PRCTR-LOW").text = d["利润中心编码"]#填写利润中心
            projectCode=d["项目编码"] if isinstance(d["项目编码"],str) else str(int(d["项目编码"]))
            session.findById("wnd[0]/usr/ctxtS_PSPNR-LOW").text = projectCode #填写项目编码
            session.findById("wnd[0]/usr/txtS_GJAHR-LOW").text = d["年份"]#年份
            session.findById("wnd[0]/usr/txtS_MONAT-LOW").text = d["月份"]#月份
            session.findById("wnd[0]/usr/radP_CSGZ").setFocus()
            session.findById("wnd[0]/usr/radP_CSGZ").select()
            session.findById("wnd[0]/usr/radP_SRCBGZ").select() #选中收入成本过账
            session.findById("wnd[0]/tbar[1]/btn[8]").press()
            table_RowCount=session.findById("wnd[0]/usr/cntlGRID1/shellcont/shell").RowCount
            session.findById("wnd[0]/usr/cntlGRID1/shellcont/shell").setCurrentCell(table_RowCount-1, "GJAHR") #选中最大行
            session.findById("wnd[0]/usr/cntlGRID1/shellcont/shell").doubleClickCurrentCell()
            buttonText=session.findById("wnd[0]/tbar[1]/btn[13]").text
            if buttonText=="重新编辑" :
                buttonText=session.findById("wnd[0]/tbar[1]/btn[13]").press()  #看是否需要编辑

            session.findById("wnd[0]/usr/tabsGO_TABCTRL/tabpTAB_9252").select()
            session.findById("wnd[0]/usr/tabsGO_TABCTRL/tabpTAB_9252/ssubTABSUB03:ZARAPRP0002:9252/cntlCONTAINER9252/shellcont/shell").modifyCell(4, "MWSKZ", d["税码"]) #税金
            session.findById("wnd[0]/usr/tabsGO_TABCTRL/tabpTAB_9252/ssubTABSUB03:ZARAPRP0002:9252/cntlCONTAINER9252/shellcont/shell").modifyCell(8, "BEWAR", "I02")
            #session.findById("wnd[0]/usr/tabsGO_TABCTRL/tabpTAB_9252/ssubTABSUB03:ZARAPRP0002:9252/cntlCONTAINER9252/shellcont/shell").setCurrentCell(5, "MWSKZ")
            #session.findById("wnd[0]/usr/tabsGO_TABCTRL/tabpTAB_9252/ssubTABSUB03:ZARAPRP0002:9252/cntlCONTAINER9252/shellcont/shell").triggerModified()
            session.findById("wnd[0]/usr/tabsGO_TABCTRL/tabpTAB_9252/ssubTABSUB03:ZARAPRP0002:9252/cntlCONTAINER9252/shellcont/shell").modifyCell(5, "MWSKZ", d["税码"])
            #session.findById("wnd[0]/usr/tabsGO_TABCTRL/tabpTAB_9252/ssubTABSUB03:ZARAPRP0002:9252/cntlCONTAINER9252/shellcont/shell").setCurrentCell(4, "ZZ010")
            #session.findById("wnd[0]/usr/tabsGO_TABCTRL/tabpTAB_9252/ssubTABSUB03:ZARAPRP0002:9252/cntlCONTAINER9252/shellcont/shell").triggerModified()
            session.findById("wnd[0]/usr/tabsGO_TABCTRL/tabpTAB_9252/ssubTABSUB03:ZARAPRP0002:9252/cntlCONTAINER9252/shellcont/shell").modifyCell(5, "ZZ010", d["收入合同编号"])
            #session.findById("wnd[0]/usr/tabsGO_TABCTRL/tabpTAB_9252/ssubTABSUB03:ZARAPRP0002:9252/cntlCONTAINER9252/shellcont/shell").currentCellRow = 5
            #session.findById("wnd[0]/usr/tabsGO_TABCTRL/tabpTAB_9252/ssubTABSUB03:ZARAPRP0002:9252/cntlCONTAINER9252/shellcont/shell").triggerModified()
            session.findById("wnd[0]/usr/tabsGO_TABCTRL/tabpTAB_9252/ssubTABSUB03:ZARAPRP0002:9252/cntlCONTAINER9252/shellcont/shell").modifyCell(4, "ZZ010", d["收入合同编号"])
            #session.findById("wnd[0]/usr/tabsGO_TABCTRL/tabpTAB_9252/ssubTABSUB03:ZARAPRP0002:9252/cntlCONTAINER9252/shellcont/shell").currentCellRow = 4
            #session.findById("wnd[0]/usr/tabsGO_TABCTRL/tabpTAB_9252/ssubTABSUB03:ZARAPRP0002:9252/cntlCONTAINER9252/shellcont/shell").triggerModified()
            session.findById("wnd[0]/usr/subSUB01:ZARAPRP0002:9210/ctxtGS_CSD-EXDATA-KSSPDH").text = d["匡算单据号"] #匡算单号
            session.findById("wnd[0]/usr/subSUB01:ZARAPRP0002:9210/ctxtGS_CSD-EXDATA-KSSPDH").setFocus()
            session.findById("wnd[0]/usr/subSUB01:ZARAPRP0002:9210/ctxtGS_CSD-EXDATA-KSSPDH").caretPosition = 19
            session.findById("wnd[0]/tbar[1]/btn[13]").press() #保存
            session.findById("wnd[0]/tbar[1]/btn[14]").press() #最终过账
            sapInfo=session.findById("wnd[0]/sbar").text  
            db.updateData("独立结账模板收入成本","消息列",sapInfo,i+1)
            session.findById("wnd[0]/tbar[0]/btn[3]").press()#退出1
            session.findById("wnd[0]/tbar[0]/btn[3]").press()#推出2
            db.updateData("独立结账模板收入成本","SAP最终过账是否","执行完毕",i+1)            
    session.findById("wnd[0]").Close()
    print("完成任务")
