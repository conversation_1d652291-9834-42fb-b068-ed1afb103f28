import fitz
import pandas as pd

def extract_tables_from_pdf(pdf_path, page_numbers=None):
    """
    从PDF文件中提取表格
    
    参数:
    pdf_path (str): PDF文件路径
    page_numbers (list): 需要提取的页码列表,如果为None则提取所有页面
    
    返回:
    list: 包含所有提取到的表格的列表,每个表格为pandas DataFrame格式
    """
    # 打开PDF文件
    doc = fitz.open(pdf_path)
    tables = []
    
    # 确定要处理的页面范围
    if page_numbers is None:
        pages = range(len(doc))
    else:
        pages = [p-1 for p in page_numbers]  # 转换为0基页码
        
    # 遍历每一页
    for page_num in pages:
        try:
            page = doc[page_num]
            
            # 提取表格
            tab = page.find_tables()
            
            # 处理页面中的每个表格
            for table in tab:
                # 获取表格数据
                table_data = table.extract()
                
                # 转换为DataFrame
                df = pd.DataFrame(table_data)
                
                # 如果第一行包含列名,可以将其设置为列标题
                if len(df) > 0:
                    df.columns = df.iloc[0]
                    df = df.iloc[1:]
                    
                tables.append(df)
                
        except Exception as e:
            print(f"处理第 {page_num+1} 页时出错: {str(e)}")
            continue
    
    # 关闭PDF文件
    doc.close()
    
    return tables

# 使用示例
if __name__ == "__main__":
    # 提取指定PDF文件中的所有表格
    tables = extract_tables_from_pdf(r"C:\Users\<USER>\Desktop\保理贴现利息、手续费电子回单--常德城发.pdf")
    
    # 打印每个表格的信息
    for i, table2 in enumerate(tables):
        #合并talle数据
        table=pd.concat([table2],axis=1)
    table.to_excel(r"C:\Users\<USER>\Desktop\保理贴现利息、手续费电子回单--常德城发.xlsx")
        
    # 也可以指定特定页码
    # tables = extract_tables_from_pdf("example.pdf", page_numbers=[1,3,5])