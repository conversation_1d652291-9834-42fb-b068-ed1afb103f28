import flet as ft
import src.Gui.callProcess as callF
from src.Gui.FletGui.middle.data_view import dataTabView
from src.Gui.FletGui.middle.fund_view import fundTabView
from src.Gui.FletGui.middle.setting_view import settingTabView
from src.Gui.FletGui.middle.common_view import commonTabView, VoucherWidget
from src.Gui.FletGui.middle.close_view import closeTabView
from src.Gui.FletGui.middle.FS_view import FSView
from src.Gui.FletGui.middle.collaboration_view import collaborationTabView
from src.Gui.FletGui.middle.sync_view import syncTabView
from src.Gui.FletGui.middle.salaryFee_view import salaryTabView
from src.Gui.FletGui.middle.audit_view import auditTabView
from src.Gui.FletGui.middle.tax_view import taxTabView



class middleView(ft.Column):
    def __init__(self):
        super().__init__()
        self.visible = True
        self.expand = True
        self.spacing=5
        self.control_name_text = ft.Text(theme_style=ft.TextThemeStyle.HEADLINE_MEDIUM)
        self.control_description = ft.Text(theme_style=ft.TextThemeStyle.BODY_MEDIUM)
        self.examples = ft.Column(expand=True, spacing=10, scroll=ft.ScrollMode.AUTO)
        self.controls = [
            self.control_name_text,
            self.control_description,
            self.examples,
        ]
    def changeView(self,name):
        self.controls=[]
        
        if name=="资金协管":
            self.controls=[fundTabView()]
        elif name=="设置板块":
            self.controls=[settingTabView()]
        elif name=="统计大师":
            self.controls=[syncTabView()]
        elif name=="速填精灵":
            self.controls=[salaryTabView()]
        elif name=="一键结账":
            self.controls=[closeTabView()]
        elif name=="自动制证":
            view = commonTabView()
            view.navigation_bar.destinations = [
                ft.NavigationBarDestination(icon=ft.icons.PADDING, label="制证模块"),
            ]
            view.mainRegional.controls = [
                ft.Container(
                    content=VoucherWidget(),
                    padding=15,
                    border_radius=10,
                    bgcolor=ft.colors.SURFACE_VARIANT,
                    margin=ft.margin.only(bottom=10),
                )
            ]
            self.controls = [view]
        elif name=="系统协同":
            self.controls=[collaborationTabView()]
        elif name=="报表助手":
            self.controls=[dataTabView()]
        elif name=="智慧税务":
            self.controls=[taxTabView()]
        elif name=="智能稽核":
            self.controls=[auditTabView()]
        elif name=="电子归档":
            view = commonTabView()
            view.navigation_bar.destinations = [
                ft.NavigationBarDestination(icon=ft.icons.COMMENT, label="常用模块"),
            ]
            view.mainRegional.controls = view.default
            self.controls = [view]
        self.page.update() 

    
        


        
