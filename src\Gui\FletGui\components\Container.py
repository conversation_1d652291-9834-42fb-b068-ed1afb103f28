import flet as ft
from typing import Optional, List, Union

class LabeledContainer(ft.Container):
    """
    A container with a label in the top-left corner, background color, and border.
    
    Args:
        label_text (str): The text to display in the top-left corner
        content: The content to display inside the container
        bgcolor: Background color of the container (default: colors.WHITE)
        border_color: Color of the border (default: colors.GREY_400)
        border_width: Width of the border in pixels (default: 1)
        padding: Padding inside the container (default: 10)
        **kwargs: Additional arguments to pass to the parent Container
    """
    
    def __init__(
        self,
        label_text: str,
        content: Optional[ft.Control] = None,
        bgcolor: str = ft.colors.WHITE,
        border_color: str = ft.colors.GREY_400,
        border_width: int = 1,
        padding: int = 10,
        
        **kwargs
    ):
        # Create the label
        self.label = ft.Container(
            content=ft.Text(
                label_text,
                size=12,
                weight=ft.FontWeight.BOLD,
                color=ft.colors.WHITE,
            ),
            bgcolor=border_color,
            padding=ft.padding.only(left=8, right=8, top=2, bottom=2),
            border_radius=ft.border_radius.only(top_left=5, top_right=5),
            top=0,
            left=10,
        )
        
        # Create the main content container
        content_container = ft.Container(
            content=content,
            padding=padding,
            margin=ft.margin.only(top=15),
        )
        
        # Create the stack to hold both label and content
        stack = ft.Stack(
            controls=[
                content_container,
                self.label,
            ],
            expand=True,
        )
        
        # Initialize the parent Container
        super().__init__(
            content=stack,
            border=ft.border.all(border_width, border_color),
            border_radius=ft.border_radius.all(5),
            padding=ft.padding.only(top=10, left=0, right=0, bottom=0),
            bgcolor=bgcolor,
            **kwargs
        )
        
        # Add margin to prevent the label from being cut off
        self.margin = ft.margin.only(top=10)
        
    def update_label(self, new_text: str):
        """Update the label text."""
        if hasattr(self, 'label') and self.label.content is not None:
            self.label.content.value = new_text
            self.update()


def test_labeled_container(page: ft.Page):
    """Test function to demonstrate the LabeledContainer."""
    page.title = "LabeledContainer Demo"
    page.locale_configuration = ft.LocaleConfiguration(
        supported_locales=[
            ft.Locale("zh", "CN", "Hans"),
        ],
        current_locale=ft.Locale("zh", "CN", "Hans"),
    )
    page.padding = 20
    page.theme_mode = ft.ThemeMode.LIGHT
    
    # Create a column to hold our test containers
    main_column = ft.Column(spacing=20, expand=True)
    
    # Example 1: Basic usage
    basic_container = LabeledContainer(
        label_text="基本信息",
        content=ft.Text("这是一个基本的LabeledContainer示例，带有标题和边框。"),
        bgcolor=ft.colors.WHITE,
        border_color=ft.colors.BLUE_400,
        width=400,
    )
    
    # Example 2: With controls inside
    controls = ft.Column(
        controls=[
            ft.Text("用户名:"),
            ft.TextField(label="输入用户名"),
            ft.Text("密码:"),
            ft.TextField(label="输入密码", password=True),
            ft.ElevatedButton("登录")
        ],
        spacing=10
    )
    
    form_container = LabeledContainer(
        label_text="登录表单",
        content=controls,
        bgcolor=ft.colors.GREY_50,
        border_color=ft.colors.GREEN_400,
        width=400,
    )
    
    # Example 3: With dynamic content
    dynamic_text = ft.Text("点击按钮更改此文本")
    
    def update_text(e):
        dynamic_text.value = f"更新于: {ft.datetime.datetime.now().strftime('%H:%M:%S')}"
        dynamic_container.update()
    
    dynamic_controls = ft.Column(
        controls=[
            dynamic_text,
            ft.ElevatedButton("更新文本", on_click=update_text)
        ],
        spacing=10
    )
    
    dynamic_container = LabeledContainer(
        label_text="动态内容",
        content=dynamic_controls,
        bgcolor=ft.colors.WHITE,
        border_color=ft.colors.ORANGE_400,
        width=400,
    )
    
    # Add all containers to the main column
    main_column.controls.extend([
        basic_container,
        form_container,
        dynamic_container,
    ])
    
    # Add a button to update the label
    def update_label(e):
        new_text = f"更新标题 {ft.datetime.datetime.now().strftime('%H:%M:%S')}"
        dynamic_container.update_label(new_text)
    
    update_button = ft.ElevatedButton("更新标题", on_click=update_label)
    main_column.controls.append(update_button)
    
    # Add the main column to the page
    page.add(main_column)


# To run the test:
if __name__ == "__main__":
    ft.app(target=test_labeled_container)
