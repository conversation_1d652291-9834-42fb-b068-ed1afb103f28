
from playwright.sync_api import Page,BrowserContext
from playwright.sync_api import ChromiumBrowserContext
import re
import src.utils.cscec as cscec
import src.utils.Browser.Browser as browser
import duckdb
import pandas as pd
import src.base.settings as settings
import src.utils.fileui as fileui
from src.utils.DB.midIntrSQLiteDB import excelDB
from src.utils.DB.configDB import configDB

class stupidPlan():
    def __init__(self) -> None:
        pass
    def getTemplate(self):
        conn = duckdb.connect(database=settings.PATH_DUCKDB+'/example.duckdb', read_only=True)
        df1=conn.execute("select *,0 as 本次付款金额,'' as 备注 from 一体化合同台账").df()
        df2=conn.execute("select any_value(组织机构名称) as 组织机构名称, any_value(项目名称) as 项目名称,'' as 是否历史项目,any_value(项目编号) as 项目编号,0 as 本月收款,'' as 上级机关项目,0 as 预计借款,'' as 回款责任人,'' as 是否上报 from 一体化合同台账 group by 组织机构名称,项目名称,项目编号 ").df()
        directory = fileui.select_directory()
        with pd.ExcelWriter(directory+'/财商计划上传模板.xlsx') as writer:
            df2.to_excel(writer, sheet_name='计划上传总表', index=False)
            df1.to_excel(writer, sheet_name='计划上传明细', index=False)
        print("模板保存至"+directory+'/财商计划上传模板.xlsx')

    def returnstring(self,s):
        if type(s)==float or type(s)==int :
            s=str(round(s,2))
            return s
        else: 
            return "0.00"
    
    def __fillHistoryProject(self,page:Page,df:pd.DataFrame):
        def filOneContract(page:Page,locateStr:str,row:pd.Series):  #填入历史项目
            table=page.locator(locateStr)
            table_row=table.count()
            table.nth(table_row-1).locator("//button[1]").click() #新增
            table=page.locator(locateStr)
            table_row=table.count()
            table.nth(table_row-2).locator("//td[@name='contractName']").click()#选择合同
            page.locator("//span[contains(text(),'更多')]").click()
            page.locator("//div[@id='contractCode']//div[@class='field__control']//div//div[@class='text']//div//input[@placeholder='请输入']").fill(row["合同编号"])
            page.locator("//div[@class='ant-modal-body']//button[2]").click()   #点击查询
            page.locator("//div[@class='ant-modal-body']//button[2]").click()   #点击查询
            page.locator("//div[@class='ant-modal-body']//button[2]").click()   #点击查询
            page.locator("//*[@id='list-selector']/div[2]/div[1]/div[1]/div[2]/div/div[2]/div/div[2][text()='"+row["合同编号"]+"']")
            page.get_by_text(row["合同编号"]).click()
            cscec.getVisible(page,"//div[@class='ant-modal-footer']//button[2]").click() #点击确认

            table.nth(table_row-2).locator("//td[@name='cumulativeMonthly']").click()#累计结算
            table.nth(table_row-2).locator("//td[@name='cumulativeMonthly']//input").fill(self.returnstring(row["结算金额"]))  #

            table.nth(table_row-2).locator("//td[@name='paymentsDue']").click()#
            table.nth(table_row-2).locator("//td[@name='paymentsDue']//input").fill(self.returnstring(round(row["结算金额"]*row["付款比例"],2)))  #
            
            table.nth(table_row-2).locator("//td[@name='paymentsMade']").click()#
            table.nth(table_row-2).locator("//td[@name='paymentsMade']//input").fill(self.returnstring(row["已付金额"]))  #

            table.nth(table_row-2).locator("//td[@name='contractArrears']").click()#欠付金额
            table.nth(table_row-2).locator("//td[@name='contractArrears']//input").fill(self.returnstring(round(row["拖欠款"],2)))  #

            table.nth(table_row-2).locator("//td[@name='plannedPayment']").click()#
            page.keyboard.down('Control')
            page.keyboard.press('A')
            page.keyboard.up('Control')
            page.keyboard.press('Backspace')
            table.nth(table_row-2).locator("//td[@name='plannedPayment']//input").fill(self.returnstring(row["本次付款金额"]))  #

        for index,row in df.iterrows():
            if row["本次付款金额"] >0.001:
                contractType=row["合同类型"]
                if contractType=="专业分包合同":
                    page.get_by_role("tab", name="专业分包支出").click()
                    s="//*[@id='app']/div/a-spin/div/div[4]/div[3]/div[3]/div[2]/div[3]/div[5]/div[2]/div[3]/div[2]/div[2]/table/tr" #倒数第四个div
                    filOneContract(page,s,row)           
                elif contractType=="劳务分包合同":
                    page.get_by_role("tab", name="劳务分包支出").click()
                    s="//*[@id='app']/div/a-spin/div/div[4]/div[3]/div[3]/div[2]/div[3]/div[5]/div[2]/div[3]/div[3]/div[2]/table/tr"
                    filOneContract(page,s,row)
                elif contractType=="材料采购合同":
                    page.get_by_role("tab", name="材料款项支出").click()
                    s="//*[@id='app']/div/a-spin/div/div[4]/div[3]/div[3]/div[2]/div[3]/div[5]/div[2]/div[3]/div[4]/div[2]/table/tr"
                    filOneContract(page,s,row)

    def __fillNormalProject(self,page:Page,d: dict):  #填入正常项目
        page.get_by_role("tab", name="专业分包支出").click()
        table=page.locator("//*[@id='app']/div/a-spin/div/div[4]/div[3]/div[3]/div[2]/div[3]/div[5]/div[2]/div[3]/div[2]/div[2]/table/tr")
        table_row=table.count()

        if table_row>2 :
            for k in range(3,table_row+1):
                s=table.nth(k-1).locator("//td[@name='subSupplieProjectCode']").text_content() #合同编码
                a=table.nth(k-1).locator("//td[@name='plannedPayment']")
                a.click()
                page.keyboard.down('Control')
                page.keyboard.press('A')
                page.keyboard.up('Control')
                page.keyboard.press('Backspace')
                try:
                    pattern = r"\s*([^ \n]+)\s*"
                    result = re.search(pattern, s)
                    extracted_text = result.group(1)
                    table.nth(k-1).locator("//td[@name='plannedPayment']//input").fill(self.returnstring(d[extracted_text]))
                except:
                    table.nth(k-1).locator("//td[@name='plannedPayment']//input").fill("0")
        
        page.get_by_role("tab", name="劳务分包支出").click()
        table=page.locator("//*[@id='app']/div/a-spin/div/div[4]/div[3]/div[3]/div[2]/div[3]/div[5]/div[2]/div[3]/div[3]/div[2]/table/tr")
        table_row=table.count()

        if table_row>2 :
            for k in range(3,table_row+1):

                s=table.nth(k-1).locator("//td[@name='subSupplieProjectCode']").text_content()
                a=table.nth(k-1).locator("//td[@name='plannedPayment']")
                a.click()
                page.keyboard.down('Control')
                page.keyboard.press('A')
                page.keyboard.up('Control')
                page.keyboard.press('Backspace')
                try:
                    pattern = r"\s*([^ \n]+)\s*"
                    result = re.search(pattern, s)
                    extracted_text = result.group(1)
                    table.nth(k-1).locator("//td[@name='plannedPayment']//input").fill(self.returnstring(d[extracted_text]))
                except:
                    table.nth(k-1).locator("//td[@name='plannedPayment']//input").fill("0")
        
        page.get_by_role("tab", name="材料款项支出").click()
        table=page.locator("//*[@id='app']/div/a-spin/div/div[4]/div[3]/div[3]/div[2]/div[3]/div[5]/div[2]/div[3]/div[4]/div[2]/table/tr")
        table_row=table.count()

        if table_row>2 :
            for k in range(3,table_row+1):

                s=table.nth(k-1).locator("//td[@name='subSupplieProjectCode']").text_content()
                a=table.nth(k-1).locator("//td[@name='plannedPayment']")
                a.click()
                page.keyboard.down('Control')
                page.keyboard.press('A')
                page.keyboard.up('Control')
                page.keyboard.press('Backspace')
                try:
                    pattern = r"\s*([^ \n]+)\s*"
                    result = re.search(pattern, s)
                    extracted_text = result.group(1)
                    table.nth(k-1).locator("//td[@name='plannedPayment']//input").fill(self.returnstring(d[extracted_text]))
                except:
                    table.nth(k-1).locator("//td[@name='plannedPayment']//input").fill("0")

    
    def fillStupidPlan(self):
        def checkStr(s):
            if type(s)==float or type(s)==int :
                return str(int(s))
            else:
                return s
        db=excelDB()
        df0=db.getDataframe("计划上传总表")
        df1=db.getDataframe("计划上传明细")
        configDict=configDB().fqUserSettings
        theBrowser=browser.myBrowser("3b",configDict["username"],configDict["upassword"])
        page=theBrowser.page
        context=theBrowser.Context
        theBrowser.goCscec3bPlan()

        try:
            for index,row in df0.iterrows():
                if row["是否上报"]=="是":
                    db.updateData("计划上传总表","是否上报","开始执行",index+1)
                    page.locator("//a[@title='资金收支计划']/parent::li").click()
                    if row["是否历史项目"]=="是":
                        page.locator("//li[contains(text(),'历史项目资金计划')]").click()
                    else:
                        page.locator("//li[contains(text(),'历史项目资金计划')]/preceding::li[1]").click() #因为文本等于有空格
                        
                    #系统更改了选择计划入口

                    page.locator("//div[text()='当前项目：']/following-sibling::div[1]/div[1]").click()
                    page.locator("//input[@placeholder='请输入项目编码']").fill(checkStr(row["项目编号"]))
                    page.locator("//*[@id='portal-form-list-container']/div[3]/div/div/div[2]/div/div[2]/div[2]/div/div/div/div[1]/div[5]/button").click()
                    page.locator("//td[normalize-space()='"+checkStr(row["项目编号"])+"']").click()
                    #选择项目
                    
                    with context.expect_page() as new_page_info:
                        page.locator("//button[@class='ant-btn ant-btn-primary list-action-add']").click() 
                    newPage = new_page_info.value

                    '''
                    page.locator("//*[contains(text(),'日期')]/parent::div/following-sibling::div[1]//span").first.click()
                    page.locator("//span[@title='2024年1季度']/preceding-sibling::span[1]").click()
                    page.get_by_text("2024年02月").click()
                    page.locator("//div[contains(text(),'期间')]/parent::div/parent::div//span[contains(text(),'确 定')]").click()
                    '''
                    newPage.locator("//label[contains(text(),'本级机关项目')]/parent::div/following-sibling::div[1]//span").first.click()
                    newPage.get_by_text(row["上级机关项目"]).click()
                    newPage.get_by_text("确 定").click()

                    newPage.locator("//label[contains(text(),'收款责任人')]/parent::div/following-sibling::div[1]//span").first.click()
                    newPage.locator("//input[@placeholder='搜索组织、姓名']").fill(row["回款责任人"])
                    newPage.locator("//i[@aria-label='图标: search']").click()
                    newPage.get_by_text(row["回款责任人"]).first.click()
                    newPage.locator("//div[contains(text(),'已选择：')]/preceding-sibling::div//span[contains(text(),'确 定')]").click()
                    
                    newPage.get_by_text("取 数").click()
                    newPage.get_by_text("收入汇总").click()

                    newPage.get_by_text("工程款收入计划").click()
                    table=newPage.locator("//*[@id='app']/div/a-spin/div/div[4]/div[3]/div[2]/div[2]/div[3]/div[1]/div[2]/table/tr")
                    if table.count()==1:
                        table.nth(0).locator("//button[1]").click()
                    table.nth(0).locator("//td[4]").click()
                    newPage.keyboard.type("总包工程款")  #收款
                    table.nth(0).locator("//td[11]").click()
                    table.nth(0).locator("//td[11]//input").fill(self.returnstring(row["本月收款"]))  #收款

                    newPage.get_by_text("上级单位借款（建造）").click()
                    table=newPage.locator("//*[@id='app']/div/a-spin/div/div[4]/div[3]/div[2]/div[2]/div[3]/div[3]/div[2]/table/tr")
                    table.nth(1).locator("//button[1]").click()
                    table.nth(1).locator("//td[5]").click()
                    table.nth(1).locator("//td[5]//input").fill(self.returnstring(row["预计借款"])) #借款金额
                    newPage.get_by_text("支出汇总",exact=True).click()
                    newPage.get_by_text("分包支出汇总",exact=True).click()

                    df=df1[df1["项目名称"]==row["项目名称"]]
                    if row["是否历史项目"]=="是":
                        self.__fillHistoryProject(newPage,df)
                    else:
                        d1=df.set_index("合同编号")["本次付款金额"].to_dict()
                        self.__fillNormalProject(newPage,d1)  

                    newPage.get_by_text("保 存").click()
                    newPage.get_by_text("提 交").click()
                    df0.loc[index, '是否上报'] = "否"
                    db.updateData("计划上传总表","是否上报","执行完毕",index+1)
                    print("完成一个项目")
        except Exception as e:
            print(e)
            print(f"{index}发生错误")
        db.close()



def createTemplate():
    stupidPlan().getTemplate()

def queryData():
    db=excelDB()
    db.queryData(tableName=["计划上传总表","计划上传明细"],fileName="财商资金计划")
    db.close()

def writeData():
    db=excelDB()
    db.writeExcel(tableName=["计划上传总表","计划上传明细"])
    db.close()

def main():
    stupidPlan().fillStupidPlan()