from tkinter import filedialog


def select_files():
    # 打开文件选择对话框，允许选择多个文件
    file_paths = filedialog.asksaveasfilename(filetypes=[('Excel Files', '*.xlsx'),('pdf',"*.pdf")])
    print(file_paths)
    return file_paths

import openpyxl
file_path=filedialog.askopenfilename()

def save_file():
    import pandas as pd
    df=pd.read_excel(file_path,header=1)
    saveName=select_files()
    df.to_excel(saveName+".xlsx")

def readFiletoList():
    wb=openpyxl.load_workbook(file_path)
    ws=wb.active
    for row in ws.iter_rows(min_row=1,values_only=True):
        print(row)
readFiletoList()