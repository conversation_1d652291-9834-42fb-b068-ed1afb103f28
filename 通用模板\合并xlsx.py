import tkinter as tk
from tkinter import filedialog
import pandas as pd

def select_files():
    # 打开文件选择对话框，允许选择多个文件
    file_paths = filedialog.askopenfilenames(title='选择 Excel 文件', filetypes=[('Excel Files', '*.xlsx')])
    if file_paths:
        merge_files(file_paths)

def merge_files(file_paths):
    # 初始化一个空的列表来存储 DataFrame
    dfs = []

    # 遍历所有选择的文件路径并读取每个文件
    for file in file_paths:
        try:
            df = pd.read_excel(file)
            dfs.append(df)
        except Exception as e:
            print(f"无法读取文件 {file}: {e}")

    if dfs:
        # 合并所有 DataFrame
        merged_df = pd.concat(dfs, ignore_index=True)
        
        # 将合并后的 DataFrame 保存到新的 Excel 文件
        merged_df.to_excel('merged_output.xlsx', index=False)
        print("合并完成，结果已保存为 'merged_output.xlsx'")

# 创建主窗口
root = tk.Tk()
root.title('Excel 文件合并器')

# 创建选择文件的按钮
btn = tk.Button(root, text='选择 Excel 文件', command=select_files)
btn.pack(pady=20)

# 运行主循环
root.mainloop()
