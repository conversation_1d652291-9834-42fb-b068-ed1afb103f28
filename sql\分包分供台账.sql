select 
any_value(项目名称||合同编号) as 查找主键,
any_value(组织机构名称) as 组织机构名称,
any_value(项目名称) as 项目名称,
any_value(项目编号) as 项目编号,
any_value(合同名称) as 合同名称,
any_value(合同编号) as 合同编号,
any_value(原合同编号) as 原合同编号,
any_value(合同业务内容) as 合同业务内容,
any_value(客商名称) as 客商名称,
any_value(客商编号) as 客商编号,
any_value(合同类型) as 合同类型,
sum("合同金额(含税)") as 合同金额,
any_value(税率) as 税率,
sum("累计已结算金额(含税)") as 结算金额,
sum("预收/预付余额") as 预付金额,
sum("累计已收/付金额(含税)") as 已付金额,
sum("累计发票金额(含税)") as 发票金额,
max("约定付款比例")as 付款比例,
(结算金额*付款比例) as 按比例应付余额,
(结算金额*付款比例-已付金额) as 拖欠款
from df
GROUP by  项目名称,合同编号

