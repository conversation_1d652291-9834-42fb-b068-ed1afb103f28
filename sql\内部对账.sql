with a as (
select any_value(客商编码) as 客商编码,
any_value(客商名称) as 客商名称,
any_value(过帐日期) as 过帐日期,
any_value(凭证编号) as 凭证编号,
any_value(行项目文本) as 事由,
0-sum(case when 科目描述 like '应付账款\分包工程款\%' and 借贷标识=='贷方'  and 科目描述 not like '%暂估%' then 本币金额 else 0 end) as 结算额,
sum(case when 科目描述 like '应付账款\分包工程款\%' and 借贷标识=='借方' and 科目描述 not like '%暂估%' then 本币金额 else 0 end) as 付款额,
0-sum(case when 科目描述 like '应付账款\分包工程款\%暂估%' and 借贷标识=='贷方'  then 本币金额 else 0 end) as 暂估额
from df
where 凭证编号 not like '10%' and 凭证编号 not like '20%' and 凭证编号 not like '99%'
group by 客商编码,过帐日期,凭证编号)
--
select a.*,主数据.利润中心 from a left join 主数据 on a.客商编码=主数据.项目所属的核算组织
