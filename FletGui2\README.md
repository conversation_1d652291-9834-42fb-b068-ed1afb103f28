# FIP RPA Sci-Fi Flet GUI

This application provides an advanced sci-fi themed main page for a Flet-based RPA tool.
It features a card-based function selection area and a collapsible message manager.

## Setup

1.  **Ensure Python is installed.** (Version 3.7+ recommended)
2.  **Create a virtual environment (recommended):**
    ```bash
    python -m venv .venv
    # Activate the virtual environment
    # On Windows:
    .venv\Scripts\activate
    # On macOS/Linux:
    source .venv/bin/activate
    ```
3.  **Install dependencies:**
    ```bash
    pip install -r requirements.txt
    ```
    *(You might need to install `google-fonts-orbitron` or other font packages if you decide to use custom local fonts not available by default with Flet. The current code uses "Orbitron" and "Consolas" which <PERSON><PERSON> attempts to find or use system fallbacks.)*

4.  **(Optional) Create an `assets` directory:**
    If you plan to use custom fonts or images, create an `assets` directory in the same location as `main.py` (`src/Gui/FletGui2/assets/`) and place your asset files there. The `ft.app(target=main, assets_dir="assets")` line in `main.py` tells <PERSON><PERSON> to look for assets in this folder.

## Running the Application

Once the setup is complete, run the application using:

```bash
python main.py
```

This will launch the Flet application window.

## Features

*   **Sci-Fi Themed UI:** Dark theme with neon accents.
*   **Card-Based Function Access:** Clickable cards to navigate to different application modules.
*   **Dynamic Function Display:** The right panel updates to show content related to the selected function.
*   **Collapsible Message Manager:** A right sidebar to display system messages, logs, or notifications. Can be toggled.
*   **Responsive Elements:** UI elements adapt to interactions.

## Customization

*   **Colors:** Modify the color constants at the top of `main.py` to change the theme.
*   **Functions:** Update the `function_cards_data` list in `main.py` to add, remove, or modify function cards.
*   **Function Views:** Implement the actual UI for each function within the `show_function_view` function.
*   **Fonts:** To use specific sci-fi fonts, ensure they are installed on your system or place font files in the `assets` directory and reference them in Flet components (e.g., `font_family="YourCustomFont"`).
