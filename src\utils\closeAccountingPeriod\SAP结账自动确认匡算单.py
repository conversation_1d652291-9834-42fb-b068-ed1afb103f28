import os
from playwright.sync_api import Playwright, sync_playwright
import src.utils.sapPublic.GetSAPSession as GetSAPSession
import src.utils.Excel.excel as excel
import src.base.settings as settings
from src.utils.DB.midIntrSQLiteDB import excelDB
import openpyxl
def saveExcel(session,savePath:str):
    shellgird=session.findById("wnd[0]/usr/cntlGRID1/shellcont/shell")
    titles=["GJAHR","MONAT"
    ,"DOC_NR"
    ,"DOC_VER"
    ,"ZDBRWDH"
    ,"POSID"
    ,"POST1"
    ,"PRCTR"
    ,"LTEXT"
    ,"BUKRS"
    ,"BUTXT"
    ,"KUN<PERSON>"
    ,"ZDQ"
    ,"PGSBR"
    ,"ZYJZSR"
    ,"ZYJZCB"
    ,"ZYJML"
    ,"<PERSON>YJMLL"
    ,"<PERSON>LJYGZCB"
    ,"ZWGJD"
    ,"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"
    ,"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"
    ,"<PERSON><PERSON><PERSON><PERSON>"
    ,"<PERSON>LJ<PERSON><PERSON><PERSON><PERSON>"
    ,"<PERSON><PERSON><PERSON>QQ<PERSON><PERSON>"
    ,"<PERSON><PERSON><PERSON><PERSON><PERSON>"
    ,"<PERSON><PERSON>J<PERSON><PERSON><PERSON>"
    ,"ZLJQQ<PERSON>"
    ,"<PERSON><PERSON>Q<PERSON>"
    ,"ZLJBQYJ<PERSON>"
    ,"ZLJQQYJSS"
    ,"ZBQYYSS"
    ,"ZLJGCJS"
    ,"ZYWGWJS"
    ,"ZYJSWWG"
    ,"ZLJJLR"
    ,"ZBNJLR"
    ,"ZBQJLR"
    ,"CB_QZZDSBTR"]

    first=[]
    second=[]
    for title in titles:
        first.append(shellgird.getCellValue(-1, title))
        needTrans=shellgird.getCellValue(0, title).replace(",","")
        if needTrans[-1]=="-":
            needTrans=float("-"+needTrans[:-1])
        if isinstance(needTrans,str):
            try:
                needTrans=float(needTrans)
            except:
                pass
        second.append(needTrans)


    wb=openpyxl.Workbook()
    ws=wb.active
    ws.append(first)
    ws.append(second)
    wb.save(savePath)

def autofill():
    db=excelDB()
    df=db.getDataframe("独立结账模板收入成本")
    session = GetSAPSession.creatSAP()
    for i,row in df.iterrows():
        d=row.to_dict()
        if d["SAP确认是否"]=="是" :
            print("开始序号"+str(i+1)+"行")
            db.updateData("独立结账模板收入成本","SAP确认是否","开始执行",i+1)
            session.StartTransaction("ZARAP0003")
            session.findById("wnd[0]/usr/ctxtS_PRCTR-LOW").text = d["利润中心编码"]#填写利润中心
            session.findById("wnd[0]/usr/ctxtS_PSPNR-LOW").text = d["项目编码"] #填写项目编码
            session.findById("wnd[0]/usr/txtS_GJAHR-LOW").text = d["年份"]#年份
            session.findById("wnd[0]/usr/txtS_MONAT-LOW").text = d["月份"] #月份
            session.findById("wnd[0]/usr/ctxtS_PSPNR-LOW").setFocus()
            session.findById("wnd[0]/usr/radP_DBCL").select()
            session.findById("wnd[0]/tbar[1]/btn[8]").press()
            table_RowCount=session.findById("wnd[0]/usr/cntlGRID1/shellcont/shell").RowCount
            session.findById("wnd[0]/usr/cntlGRID1/shellcont/shell").selectedRows = str(table_RowCount-1)
            session.findById("wnd[0]/tbar[1]/btn[18]").press()
            sapInfo=''
            try:
                session.findById("wnd[0]/tbar[1]/btn[17]").press()
                sapInfo=session.findById("wnd[0]/sbar").text  
                db.updateData("独立结账模板收入成本","SAP确认是否",sapInfo,i+1)
            except:
                db.updateData("独立结账模板收入成本","SAP确认是否","未见确认按钮，是未到期还是股份外部",i+1)
            session.findById("wnd[0]/tbar[0]/btn[3]").press()
            session.findById("wnd[0]/tbar[0]/btn[3]").press()

            #以下为自动导出匡算单

            try:
                session.findById("wnd[0]/usr/radP_CSGZ").setFocus()
                session.findById("wnd[0]/usr/radP_CSGZ").select()
                session.findById("wnd[0]/usr/radP_SRXMKS").setFocus()
                session.findById("wnd[0]/usr/radP_SRXMKS").select()
                session.findById("wnd[0]/usr/radP_SRCBGZ").setFocus()
                session.findById("wnd[0]/usr/radP_SRCBGZ").select()

                session.findById("wnd[0]/tbar[1]/btn[8]").press()
                table_RowCount=session.findById("wnd[0]/usr/cntlGRID1/shellcont/shell").RowCount
                session.findById("wnd[0]/usr/cntlGRID1/shellcont/shell").setCurrentCell(table_RowCount-1, "GJAHR") #选中最大行
                session.findById("wnd[0]/usr/cntlGRID1/shellcont/shell").doubleClickCurrentCell()
                session.findById("wnd[0]/tbar[1]/btn[13]").press()
                session.findById("wnd[0]/tbar[0]/btn[3]").press()
                session.findById("wnd[0]/tbar[0]/btn[3]").press()
                session.findById("wnd[0]/usr/radP_SRXMKS").setFocus()
                session.findById("wnd[0]/usr/radP_SRXMKS").select()
                session.findById("wnd[0]/tbar[1]/btn[8]").press()
                file_path=settings.PATH_DOWNLOAD+"/收入匡算附件/"+d["利润中心编码"]+".xlsx"
                if os.path.exists(file_path):
                    os.remove(file_path)
                if not os.path.exists(settings.PATH_DOWNLOAD+"/收入匡算附件"):
                    os.makedirs(settings.PATH_DOWNLOAD+"/收入匡算附件")
                saveExcel(session,file_path)
                session.findById("wnd[0]/tbar[0]/btn[3]").press()
                sapInfo=session.findById("wnd[0]/sbar").text 
                db.updateData("独立结账模板收入成本","SAP确认是否","执行完毕",i+1)
            except:
                db.updateData("独立结账模板收入成本","SAP确认是否",sapInfo+"导出失败",i+1)
            
    session.findById("wnd[0]").Close()
