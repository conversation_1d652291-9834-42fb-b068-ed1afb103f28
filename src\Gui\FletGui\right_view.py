import flet as ft
import src.base.settings as setting
import os
import src.Gui.callProcess as callF
import threading
import time

class rightView(ft.Column):
    def __init__(self):
        super().__init__()
        self.visible = True
        self.spacing = 20
        self.width = 250
        self.height = 800
        self.expand = False
        self.msgBox = self.createView()
        self.alignment = ft.MainAxisAlignment.CENTER
        self.horizontal_alignment = ft.CrossAxisAlignment.CENTER
        
        # 创建标题
        title = ft.Container(
            content=ft.Text(
                "消息管理器",
                size=16,
                weight=ft.FontWeight.BOLD,
                text_align=ft.TextAlign.CENTER,
            ),
            padding=10,
            border_radius=10,
            alignment=ft.alignment.center,
        )
        
        # 创建消息框容器
        msg_container = ft.Container(
            content=self.msgBox,
            padding=15,
            border_radius=10,
            alignment=ft.alignment.center,
        )
        
        # 创建按钮容器
        button_container = ft.Container(
            content=ft.Column(
                controls=[
                    ft.Row(
                        controls=[
                            ft.OutlinedButton(
                                "打开文件夹",
                                icon=ft.Icons.FOLDER_OPEN,
                                on_click=lambda e: os.startfile(setting.PATH_DATA),
                                style=ft.ButtonStyle(
                                    shape=ft.RoundedRectangleBorder(radius=5),
                                    padding=10,
                                ),
                            ),
                            ft.OutlinedButton(
                                "快速停止",
                                icon=ft.Icons.STOP,
                                on_click=lambda e: callF.thisProcess.terminate(),
                                style=ft.ButtonStyle(
                                    shape=ft.RoundedRectangleBorder(radius=5),
                                    padding=10,
                                    color=ft.colors.RED,
                                ),
                            ),
                        ],
                        alignment=ft.MainAxisAlignment.CENTER,
                        spacing=10,
                    ),
                    ft.Row(
                        controls=[
                            ft.OutlinedButton(
                                "打开浏览器",
                                icon=ft.Icons.CHROME_READER_MODE,
                                on_click=lambda e: callF.thisProcess.run({"功能":"打开chrome浏览器"}),
                                style=ft.ButtonStyle(
                                    shape=ft.RoundedRectangleBorder(radius=5),
                                    padding=10,
                                ),
                            ),
                            ft.OutlinedButton(
                                "清除登录",
                                icon=ft.Icons.LOGOUT,
                                on_click=lambda e: callF.thisProcess.run({"功能":"清除已登录信息"}),
                                style=ft.ButtonStyle(
                                    shape=ft.RoundedRectangleBorder(radius=5),
                                    padding=10,
                                ),
                            ),
                        ],
                        alignment=ft.MainAxisAlignment.CENTER,
                        spacing=10,
                    ),
                ],
                spacing=10,
            ),
            padding=5,
            border_radius=5,
            alignment=ft.alignment.center,
        )
        
        self.controls = [
            title,
            msg_container,
            button_container,
        ]
        
        self.threadMsg()

    def createView(self):
        msgBox = ft.TextField(
            read_only=True,
            value="未开始",
            multiline=True,
            min_lines=15,
            max_lines=15,
            border_radius=10,
            border_color=ft.colors.OUTLINE_VARIANT,
            focused_border_color=ft.colors.PRIMARY,
            text_style=ft.TextStyle(
                size=14,
                weight=ft.FontWeight.W_500,
                font_family="Microsoft YaHei",
            ),
            text_align=ft.TextAlign.LEFT,
        )
        return msgBox
    
    def threadMsg(self):
        def changeMsgBox():
            while True:
                s = callF.shared_queue.get()["消息"]
                # 检查是否包含处理统计信息
                if "处理统计:" in s:
                    # 分离主要消息和统计信息
                    main_msg, stats = s.split("处理统计:")
                    # 设置主要消息
                    self.msgBox.value = main_msg.strip()
                    # 添加统计信息，使用不同的样式
                    self.msgBox.value += "\n\n" + "处理统计:" + stats
                else:
                    self.msgBox.value = s
                time.sleep(0.1)
                self.msgBox.update()
        t1 = threading.Thread(target=changeMsgBox)
        t1.setDaemon(True)
        t1.start()