import os
import sys

sys.path.append(".")
from playwright.sync_api import <PERSON><PERSON>, sync_playwright,Page
import src.utils.cscec as cscec
import src.utils.Excel.excel as excel
import src.utils.dop as dop
import re
import time
with sync_playwright() as playwright:
    browser = playwright.chromium.connect_over_cdp("http://localhost:9222")
    default_context = browser.contexts[0]
    page = cscec.switch_to_page(default_context,"多维度科目余额表")
    frame1=page.frame_locator("//*[@name='contentAreaFrame']").frame_locator("//*[@title='多维度科目余额表']").frame_locator("//*[@id='ITSFRAME1']")
    if frame1:
        print("开始第二步")
        frame1.locator("//span[text()='总账科目']").click()
        print("正常")
        frame1.locator("//span[text()='总账科目']/parent::label/parent::span/parent::div").click()
        frame1.locator("//span[text()='总账科目']/parent::label/parent::span/parent::div/following-sibling::div[1]/table//td").click()
        frame1.locator("//span[text()='总账科目']/parent::label/parent::span/parent::div/following-sibling::div[1]/table//input").fill("450*")
    

