import os
import sys

sys.path.append(".")
from playwright.sync_api import <PERSON><PERSON>, sync_playwright,Page
import src.utils.cscec as cscec
import src.utils.Excel.excel as excel
import src.utils.dop as dop
import re
import time
companyCode=["2000856","2061266","1030516"]
ws=excel.myBook().sheet("节点")
with sync_playwright() as playwright:
    browser = playwright.chromium.connect_over_cdp("http://localhost:9222")
    default_context = browser.contexts[0]
    page = cscec.switch_to_page(default_context,"财务年报")
    for i in range(1,ws.MaxRow):
        cc=ws.Cells(i,3).Value
        cn=ws.Cells(i,4).Value
        if ws.Cells(i,5).Value=="是":
            page.locator("//*[@id='unit']//input[@placeholder='请输入关键字...']").fill(cc)
            page.locator("//*[@id='unit']//input[@placeholder='请输入关键字...']/following-sibling::*").click()
            #svg涉及命名空间
            page.locator(f"//span[@class='isearch-keywords' and text()='{cc}']").click()
            time.sleep(1)
            page.locator("//*[@id='jtableViewDiv']/div[1]/div[2]/div[1]/div/div[3]/div[1]/canvas[5]").click()
            page.get_by_role("button", name="导出").click()
            page.locator("//label[text()='所有报表']").click()
            with page.expect_download(timeout=80000) as download_info:
                page.get_by_role("button", name="确定").click()
                download = download_info.value
                path = download.path()
                download.save_as(path=r"C:\Users\<USER>\Desktop\报表下载"+f"/{cn}.xlsx")
            ws.Cells(i,6).Value="完成"