import os
import sys

sys.path.append(".")
from playwright.sync_api import <PERSON><PERSON>, sync_playwright,Page
import src.utils.cscec as cscec
import src.utils.Excel.excel as excel
import time

def autoOne(page:Page,d: dict):  
    page.locator("//div[text()='当前项目：']/following-sibling::div[1]/div[1]").click()
    page.locator("//*[@id='portal-form-list-container']/div[3]/div/div/div[2]/div/div[2]/div[2]/div/div/div/div[1]/div[1]/div[2]/input").fill(d["项目编号"])
    page.locator("//*[@id='portal-form-list-container']/div[3]/div/div/div[2]/div/div[2]/div[2]/div/div/div/div[1]/div[5]/button").click()
    page.locator("//td[normalize-space()='"+d["项目编号"]+"']").click()
    #选择项目
    
    page.click("//button[@class='ant-btn ant-btn-primary list-action-add']") #点击新增收入成本表
    frame1=page.frame_locator("//*[@id='portal-form-list-container']/div[4]/iframe")
    frame1.locator("//*[contains(text(),'财商申请期间')]/parent::div/following-sibling::div[1]//input").click()
    frame1.locator("//span[@title='"+d["季度"]+"']/preceding-sibling::span[1]").click()
    frame1.get_by_text(""+d["期间"]+"").click()
    frame1.locator("//div[contains(text(),'期间')]/parent::div/parent::div//span[contains(text(),'确 定')]").click()
    
    frame1.locator("//*[contains(text(),'分供方合同编码')]/parent::div/following-sibling::div[1]//input[@placeholder='请选择']").click()
    frame1.locator("//div[@id='contractCode']//div[@class='field__control']//div//div[@class='text']//div//input[@placeholder='请输入']").fill(d["合同编号"])
    frame1.locator("//div[@class='ant-modal-body']//button[2]").click()
    frame1.locator("//div[@class='sheet']//div[1]//div[2]//div[1]//div[1]//div[1]//div[1]//div[1][contains(text(),'"+d["合同编号"]+"')]").click()
    #frame1.get_by_text("中建三局020020230045060056").click() #注意编码
    frame1.locator("//div[@class='ant-modal-footer']//button[2]").dblclick()
    #合同选择
    # 
    frame1.locator("//*[contains(text(),'特殊支付种类')]/parent::div/following-sibling::div[1]").click() #注意默认为预付款
    frame1.get_by_text("其他",exact=True).click()  

    if d["历史项目"]=="是":
        frame1.locator("//*[contains(text(),'累计月度结算:')]/parent::div/following-sibling::div[1]//input").fill(str(d["累计结算"]))
        frame1.locator("//*[contains(text(),'累计应付款:')]/parent::div/following-sibling::div[1]//input").fill(str(d["累计应付"]))
        frame1.locator("//*[contains(text(),'累计已付款:')]/parent::div/following-sibling::div[1]//input").fill(str(d["累计已付"]))
        frame1.locator("//*[contains(text(),'至本月合同实际欠款:')]/parent::div/following-sibling::div[1]//input").fill(str(d["累计欠付"]))

    s="//*[contains(text(),'累计月度结算:')]/parent::div/following-sibling::div[1]//input"
    valueMoney=cscec.getVisible(frame1,s).input_value()
    tk=3
    try:
        valueMoney=float(valueMoney)
    except:
        valueMoney=0
    while valueMoney<=0 and tk>0 :
        s="//*[contains(text(),'累计月度结算:')]/parent::div/following-sibling::div[1]//input"
        valueMoney=cscec.getVisible(frame1,s).input_value()
        try:
            valueMoney=float(valueMoney)
        except:
            valueMoney=0
        time.sleep(1)
        tk=tk-1


  
    s="//*[contains(text(),'款项类型:')]/parent::div/following-sibling::div[1]//div[@title='进度款']"
    #cscec.getVisible(frame1,s).click()  已经默认是进度款不用选择
    #frame1.get_by_text("进度款",exact=True).click()  

    frame1.locator("//*[contains(text(),'本次支付')]/parent::div/following-sibling::div[1]//input").fill(str(d["金额"]))

    frame1.locator("//*[@title='资金主管']/parent::div/following-sibling::div[1]//span[contains(text(),'请选择')]").click()
    frame1.locator("//input[@placeholder='搜索组织、姓名']").fill(d["资金主管"])
    frame1.locator("//i[@aria-label='图标: search']").click()
    frame1.locator(f"//span[contains(text(),'南方大区/数字公司')]/preceding-sibling::span[text()='{d['资金主管']}']").click()
    cscec.getVisible(frame1,"//span[contains(text(),'确 定')]").click()

    frame1.locator("//*[@title='财务经理']/parent::div/following-sibling::div[1]//span[contains(text(),'请选择')]").click()
    frame1.locator("//input[@placeholder='搜索组织、姓名']").nth(1).fill(d["财务经理"])
    frame1.locator("//i[@aria-label='图标: search']").nth(1).click()
    frame1.locator(f"//span[contains(text(),'南方大区/数字公司')]/preceding-sibling::span[text()='{d['财务经理']}']").click()
    cscec.getVisible(frame1,"//span[contains(text(),'确 定')]").click()

    if d["上一级财务经理"]!=None:
        frame1.locator("//*[contains(@title,'上一级财务经理')]/parent::div/following-sibling::div[1]//span[contains(text(),'请选择')]").click()
        frame1.locator("//input[@placeholder='搜索组织、姓名']").nth(2).fill(d["上一级财务经理"])
        frame1.locator("//i[@aria-label='图标: search']").nth(2).click()
        frame1.locator(f"//span[contains(text(),'数字公司')]/preceding-sibling::span[text()='{d['上一级财务经理']}']").click()
        cscec.getVisible(frame1,"//span[contains(text(),'确 定')]").click()

    if d["上一级总会"]!=None:
        frame1.locator("//*[contains(@title,'上一级总会计师')]/parent::div/following-sibling::div[1]//span[contains(text(),'请选择')]").click()
        frame1.locator("//input[@placeholder='搜索组织、姓名']").nth(3).fill(d["上一级总会"])
        frame1.locator("//i[@aria-label='图标: search']").nth(3).click()
        frame1.locator(f"//span[contains(text(),'数字公司')]/preceding-sibling::span[text()='{d['上一级总会']}']").click()
        cscec.getVisible(frame1,"//span[contains(text(),'确 定')]").click()

    s="//*[contains(text(),'截至目前项目资金账户余额:')]/parent::div/following-sibling::div[1]//input"
    valueMoney=cscec.getVisible(frame1,s).input_value()
    if float(valueMoney)<0:
        s="//*[contains(text(),'预计收款:')]/parent::div/following-sibling::div[1]//input"
        valueMoney=cscec.getVisible(frame1,s).fill(str(-float(valueMoney)))

    frame1.locator("//span[text()='提 交']/parent::button").click()


def autoOperate():
    wb=excel.myBook()
    ws=wb.sheet("特殊支付导入")
    start_number=int(ws.Cells(1,2).Value)
    table=ws.table("表11")
    max_number=table.MaxRow+1

    with sync_playwright() as playwright:
        browser = playwright.chromium.connect_over_cdp("http://localhost:9222")
        default_context = browser.contexts[0]
        page = cscec.switch_to_page(default_context,"财商")

        #page.locator("//span[text()='资金管理' and @title='资金管理']").click()
        #page.locator("//td[normalize-space()='1']").click()
        page.locator("//span[@title='资金收支管理']").click()
        page.locator("//a[@title='资金支付申请']/parent::li").click()
        page.locator("//div[@class='main-center']//div[2]//ul[1]//li[2]").click()
        for i in range(start_number+1,max_number+1):  #跳过标题行+1
            d=table.toDict(i)     
            autoOne(page,d)  
            ws.Cells(1,2).Value=ws.Cells(1,2).Value+1
autoOperate()
    
