with a as (
select 
any_value(利润中心描述) as 利润中心描述,
any_value(总账科目长文本) as 总账科目长文本,
any_value(核对线索) as 核对线索,
any_value(业务范围) as 业务范围,
any_value(供应商) as 供应商,
any_value(供应商描述) as 供应商描述,
any_value(合同) as 合同,
any_value(合同文本描述) as 合同文本描述,
any_value(CAST(付款起算日期 as DATE)) as 起算日期,
any_value(cast(付款天数 as integer)) as 天1,
any_value(利润中心) as 利润中心,
any_value(WBS元素) as WBS元素,
any_value(付款条款) as 付款条件,
any_value(公司代码) as 公司,
any_value(总账科目) as 总账科目,
sum(case when 过帐日期 <= '替换日期' then 0-带符号的本位币金额 else null end) as 金额,
sum(0-带符号的本位币金额 ) as 当前参考额,
(起算日期 + 天1) AS 到期日
from 明细帐  
where  总账科目长文本 like '应付账款%' and  总账科目长文本 not like '%税%' and (清帐凭证='' or (清帐凭证!='' and TRY_CAST(清帐日期 as date)>'替换日期')) and 付款起算日期 SIMILAR TO '^\d{4}-\d{2}-\d{2}$' 
GROUP BY 总账科目长文本,利润中心,核对线索,供应商,合同,付款起算日期,付款天数)
--
select * from a where (金额<-1 or 金额>1) and 到期日<='替换日期' 