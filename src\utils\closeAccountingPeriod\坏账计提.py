import src.utils.sapPublic.GetSAPSession as GetSAPSession
def autofill():
    session = GetSAPSession.creatSAP()
    tableCount=session.findById("wnd[0]/usr/cntlGRID1/shellcont/shell").RowCount
    for i in range(0, tableCount):
        session.findById("wnd[0]/usr/cntlGRID1/shellcont/shell").selectColumn("YB_YJTHZ")
        session.findById("wnd[0]/usr/cntlGRID1/shellcont/shell").currentCellRow = i
        session.findById("wnd[0]/usr/cntlGRID1/shellcont/shell").modifyCell(i, "YB_YJTHZ", "-10000")
