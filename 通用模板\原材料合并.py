import tkinter as tk
from tkinter import filedialog
import openpyxl
import re
import os
def select_files():
    # 打开文件选择对话框，允许选择多个文件
    file_paths = filedialog.askopenfilenames(title='选择 Excel 文件', filetypes=[('Excel Files', '*.xlsx')])
    if file_paths:
        merge_files(file_paths)

def merge_files(file_paths):
    wb = openpyxl.Workbook()
    ws=openpyxl.load_workbook(file_paths[0],data_only=True)["Sheet1"]
    
    #添加标题
    for row in ws.iter_rows(min_row=4,max_row=5,values_only=True):
        row2=list(row)
        row2.insert(0,"时间")
        row2.insert(0,"项目名称")
        wb.active.append(row2) # 读取第一行并添加到列表

    for file in file_paths:
        file_name=os.path.splitext(os.path.basename(file))[0]
        timeStr=re.search(r"\d{4}-\d{1,2}",file).group()
        projectName=file_name.replace(timeStr,"")
        ws=openpyxl.load_workbook(file,data_only=True)["Sheet1"]
        for row in ws.values:
            #如果字符串是数字整数
            if type(row[0]) is str and row[0].isdigit():
                row2=list(row)
                row2.insert(0,timeStr)
                row2.insert(0,projectName)
                wb.active.append(row2)
    wb.save("C:/Users/<USER>/Desktop/merge.xlsx")
         



select_files()