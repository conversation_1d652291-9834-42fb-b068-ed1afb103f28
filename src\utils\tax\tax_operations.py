import os
import pandas as pd
from datetime import datetime
import src.base.settings as settings
import src.base.cache as cache

class TaxOperations:
    def __init__(self):
        self.template_path = os.path.join(settings.PATH_TEMPLATE, "税务模板")
        self.data_path = os.path.join(settings.PATH_DATA, "税务数据")
        
        # 确保目录存在
        os.makedirs(self.template_path, exist_ok=True)
        os.makedirs(self.data_path, exist_ok=True)
    
    def batch_invoice_entry(self):
        """
        批量录入发票收票单
        """
        try:
            # 获取模板文件路径
            template_file = os.path.join(self.template_path, "发票收票单模板.xlsx")
            
            # 检查模板文件是否存在
            if not os.path.exists(template_file):
                # 创建模板文件
                df = pd.DataFrame(columns=[
                    "发票代码", "发票号码", "开票日期", "金额", "税额", 
                    "价税合计", "销售方名称", "销售方税号", "购买方名称", "购买方税号",
                    "发票类型", "备注"
                ])
                df.to_excel(template_file, index=False)
            
            # 打开模板文件
            os.startfile(template_file)
            
            return True, "模板文件已打开，请填写数据后保存"
        except Exception as e:
            return False, f"操作失败：{str(e)}"
    
    def batch_tax_adjustment(self):
        """
        批量小额税金调整
        """
        try:
            # 获取模板文件路径
            template_file = os.path.join(self.template_path, "小额税金调整模板.xlsx")
            
            # 检查模板文件是否存在
            if not os.path.exists(template_file):
                # 创建模板文件
                df = pd.DataFrame(columns=[
                    "凭证日期", "凭证编号", "科目编码", "科目名称", 
                    "借方金额", "贷方金额", "摘要", "备注"
                ])
                df.to_excel(template_file, index=False)
            
            # 打开模板文件
            os.startfile(template_file)
            
            return True, "模板文件已打开，请填写数据后保存"
        except Exception as e:
            return False, f"操作失败：{str(e)}" 