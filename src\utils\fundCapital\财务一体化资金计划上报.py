
from playwright.sync_api import Page
from playwright.sync_api import ChromiumBrowserContext
import time
import src.utils.cscec as cscec
import src.utils.Excel.excel as excel
import src.utils.Browser.Browser as browser
from src.utils.Excel.excel import table
import duckdb
import src.base.settings as settings
import re
import pandas as pd


class planUpload:
    def __init__(self):
        self.stfillcollection='''
(js_need,Parameter)=>{var spread = GC.Spread.Sheets.findControl(js_need);
sheet=spread.getSheet(0);
var rowCount = sheet.getRowCount(); // 获取最大行数
for (var i = 0; i<rowCount; i++) { // 遍历行
if (sheet.getValue(i, 7)=="CNY") {sheet.setValue(i,29,0)};
};
sheet.setValue(4,29,Parameter[0]);
sheet.setValue(4,34,Parameter[1]);
}
'''     
        self.stchoose='''
js_need=>{var spread = GC.Spread.Sheets.findControl(js_need);
sheet=spread.getSheet(0);
var rowCount = sheet.getRowCount(); // 获取最大行数
for (var i = 0; i<rowCount; i++) { 
if (sheet.getValue(i, 2)=="借入方其他资金筹集") {var nuM=i+1}
}
sheet.setActiveCell(nuM, 4);//使得单元格出现在屏幕
sheet.showCell(nuM,3);
cellRect=sheet.getCellRect(nuM,4);//是表格相对元素
x=cellRect.x+cellRect.width*0.95;
y=cellRect.y+cellRect.height*0.5;
var arr=new Array(2);
arr[0]=x;
arr[1]=y;
return arr;
}
'''
        self.stfillbrrow='''
(js_need,borrowMoney)=>{var spread = GC.Spread.Sheets.findControl(js_need);
sheet=spread.getSheet(0);
var rowCount = sheet.getRowCount(); // 获取最大行数
for (var i = 0; i<rowCount; i++) { 
if (sheet.getValue(i, 2)=="借入方其他资金筹集") {var nuM=i+1}
}
sheet.setValue(nuM,7,borrowMoney);
}
'''
        self.stfocus='''
js_need=>{var spread = GC.Spread.Sheets.findControl(js_need);
sheet=spread.getSheet(0);
var rowCount = sheet.getRowCount(); // 获取最大行数
for (var i = 0; i<rowCount; i++) { 
if (sheet.getValue(i, 2)=="借入方其他资金筹集") {var nuM=i}
}

sheet.setActiveCell(nuM, 5)
}
'''
        self.st_deleteALLrownotcontract='''
js_need=>{var spread = GC.Spread.Sheets.findControl(js_need);
sheet=spread.getSheet(0);
var rowCount = sheet.getRowCount(); // 获取最大行数
var colCount = sheet.getColumnCount(); // 获取最大列数
for (var i = rowCount-3; i >= 7; i--) { // 遍历行
if(sheet.getValue(i, 6)!=null && sheet.getValue(i, 5)=="CNY"){sheet.deleteRows(i, 2);break;};
};
}
'''
        self.st2='''
js_need=>{var spread = GC.Spread.Sheets.findControl(js_need);
sheet=spread.getSheet(0);
return sheet.getValue(4, 5);}
''' #获取资金计划余额

        self.st='''
(js_need,Parameter)=>{var spread = GC.Spread.Sheets.findControl(js_need);
sheet=spread.getSheet(0);
var rowCount = sheet.getRowCount(); // 获取最大行数
var colCount = sheet.getColumnCount(); // 获取最大列数
var totalPayment = 0;
for (var i = 0; i<rowCount; i++) { // 遍历行
if (sheet.getValue(i, 2) in Parameter[0]) {sheet.setValue(i,32,Parameter[0][sheet.getValue(i, 2)])};
if (sheet.getValue(i, 2) in Parameter[1]) {sheet.setValue(i,50,Parameter[1][sheet.getValue(i, 2)])};
if (sheet.getValue(i, 7)=="CNY") {var predict=-(sheet.getValue(i, 10)*sheet.getValue(i, 9)-sheet.getValue(i, 15)-sheet.getValue(i, 37)-sheet.getValue(i, 31)-sheet.getValue(i, 43)-sheet.getValue(i, 23))/sheet.getValue(i, 9).toFixed(2)+1;totalPayment=totalPayment+sheet.getValue(i, 23)+sheet.getValue(i, 21)};
if (sheet.getValue(i, 7)=="CNY" && sheet.getValue(i, 10)*sheet.getValue(i, 9)-sheet.getValue(i, 15)-sheet.getValue(i, 37)-sheet.getValue(i, 31)-sheet.getValue(i, 43)-sheet.getValue(i, 23)<0) {sheet.setValue(i,11,predict)};
};
return totalPayment;
}
'''

    
    def __fillOneProject(self,page:Page,planDict: dict,project_name,project_unit,collection,batch,dRemark:dict,mainRemark):
        page.locator("//span[contains(text(),'-"+project_unit+"') and contains(text(),'000')]/parent::div").click() #同时包含000*-
        
        tr=cscec.getTr(page,"项目名称")
        tr.locator("//div[contains(text(),'"+project_name+"')]").dblclick()
        tr.locator("//div[contains(text(),'"+project_name+"')]").click()
        try:
            page.locator("//div[contains(text(),'"+project_name+"')]/parent::td/following-sibling::td[2]//div[contains(text(),'"+batch+"')]").dblclick()
        except Exception as e:
            page.locator("//span[contains(text(),'增加')]").click()
            page.locator("//div[contains(text(),'"+project_name+"')]/parent::td/following-sibling::td[2]//div[contains(text(),'"+batch+"')]").dblclick()
        
        page.locator("//span[contains(text(),'汇总表2')]/parent::li").click()
        js_need=cscec.getVisible(page,"//div[@gcuielement='gcSpread']")
        lastFundBalance=js_need.evaluate(self.st2)

        page.locator("//span[contains(text(),'项目流入流出表')]/parent::li").click()
        page.locator("//span[contains(text(),'项目流入表')]/parent::li").click()
        js_need=cscec.getVisible(page,"//div[@gcuielement='gcSpread']")
        page.locator("//span[contains(text(),'编辑')]/parent::div/parent::div/parent::div/parent::div").click()

        Parameter=[]
        Parameter.append(collection)
        Parameter.append(mainRemark)
        js_need.evaluate(self.stfillcollection,Parameter)

        page.locator("//span[contains(text(),'项目流出表')]/parent::li").click()
        js_need=cscec.getVisible(page,"//div[@gcuielement='gcSpread']")
        
        Parameter=[]
        Parameter.append(planDict)
        Parameter.append(dRemark)
        totalPayment=js_need.evaluate(self.st,Parameter)

        cscec.getVisible(page,"//span[contains(text(),'保存')]/parent::div/parent::div/parent::div/parent::div").click()
        msg=cscec.locatorDigalog(page,"提示").text_content()
        if "可用支出限额" in msg:
            pattern = r'\b[-+]?\d*\.\d+|\b[-+]?\d+\b'
            matches = re.findall(pattern, msg)
            borrowMoney=str(float(matches[1])-float(matches[2])+1000)
            cscec.clickDigalog(page,"提示")
            page.locator("//span[contains(text(),'其他附表')]/parent::li").click()
            page.locator("//span[contains(text(),'其他流入流出表')]/parent::li").click()
            js_need=cscec.getVisible(page,"//div[@gcuielement='gcSpread']")
            page.locator("//span[contains(text(),'编辑')]/parent::div/parent::div/parent::div/parent::div").click()
            time.sleep(1)
            js_need.evaluate(self.stfocus)
            js_need.click(button='right')
            page.get_by_text("插入浮动行...").click()
            js_need.evaluate(self.stfillbrrow,borrowMoney)
            point=js_need.evaluate(self.stchoose)
            page.mouse.click(point[0]+js_need.bounding_box()['x'],point[1]+js_need.bounding_box()['y'])
            page.keyboard.press('ArrowDown')
            page.keyboard.press('Enter')  #选择内部结算户
            cscec.getVisible(page,"//span[contains(text(),'保存')]/parent::div/parent::div/parent::div/parent::div").click()
            cscec.clickDigalog(page,"提示")
        else:
            cscec.clickDigalog(page,"提示")
        cscec.closeTab(page)

    def getTemplate(self):
        conn = duckdb.connect(database=settings.PATH_DUCKDB+'/example.duckdb', read_only=True)
        df1=conn.execute("select *,0 as 本次付款金额,'' as 备注 from 一体化合同台账").df()
        df2=conn.execute("select any_value(组织机构名称) as 组织机构名称, any_value(项目名称) as 项目名称,any_value(项目编号) as 项目编号,0 as 本次收款,'' as 总备注,'' as 是否上报 from 一体化合同台账 group by 组织机构名称,项目名称,项目编号 ").df()
        with pd.ExcelWriter(settings.PATH_EXCEL+'/一体化计划上传模板.xlsx') as writer:
            df2.to_excel(writer, sheet_name='计划上传总表', index=False)
            df1.to_excel(writer, sheet_name='计划上传明细', index=False)

    def upload(self,batch):
        df0=pd.read_excel(settings.PATH_EXCEL+'/一体化计划上传模板.xlsx',sheet_name='计划上传总表',header=0)
        df1=pd.read_excel(settings.PATH_EXCEL+'/一体化计划上传模板.xlsx',sheet_name='计划上传明细',header=0)
        B=browser.myBrowser("cscec")
        page=B.page
        cscec.toFunction(page,"司库系统","资金预算","明细财务计划编制")
        cscec.getVisible(page,"//img[contains(@style,'YduHnZdsQAAAABJRU5ErkJggg==')]").click() #展开计划
        for index, row in df0.iterrows():
            print("持续运行")
            if row["是否上报"]=="是":
                df2=df1[df1['项目名称']==row['项目名称']]
                pay_dict = dict(zip(df2['合同编号'], df2['本次付款金额']))
                remarks_dict = dict(zip(df2['合同编号'], df2['备注']))
                project_name=row["项目名称"]
                project_unit=row["组织机构名称"]
                collection=row["本次收款"]
                batch=batch
                project_code=row["项目编号"]
                mainRemark=row["总备注"]
                tryCount=3
                while tryCount>0:
                    try:
                        self.__fillOneProject(page,pay_dict,project_name,project_unit,collection,batch,remarks_dict,mainRemark)
                        tryCount=-1
                    except Exception as e:
                        print("报错一次")
                        print(row["项目名称"])
                        cscec.closeTab(page)
                        tryCount=tryCount-1
                if tryCount==0:
                    cscec.closeTab(page)


