
import requests
import sys
import os

def getDataFromWps(url,AirScriptToken):
    request_url = url
    headers = {'Content-Type': 'application/json',
        'AirScript-Token': AirScriptToken}
    payloads = {'Context': {'argv':{}}}
    response = requests.post(url=request_url, json=payloads,headers=headers).json()['data']['result']  #payloads不是用data而是json编码
    return response


def updateDatatoWithWps(arr,url,AirScriptToken):
    request_url = url
    headers = {'Content-Type': 'application/json',
        'AirScript-Token': AirScriptToken}
    payloads = {'Context': {'argv':{'js':arr}}}
    requests.post(url=request_url, json=payloads,headers=headers) #payloads不是用data而是json编码

def updateDatatoWithWps2(arr,url,AirScriptToken):
    request_url = url
    headers = {'Content-Type': 'application/json',
        'AirScript-Token': AirScriptToken}
    payloads = {'Context': {'argv':{'js':arr}}}
    requests.post(url=request_url, json=payloads,headers=headers) #payloads不是用data而是json编码


