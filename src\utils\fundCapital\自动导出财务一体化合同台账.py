import src.utils.Browser.Browser as myBrowser
import src.utils.cscec as cscec
import src.utils.Excel.excel as excel
import src.utils.Excel.openpyxlExcel as openpyxlExcel
import pandas as pd
import duckdb 
import src.base.settings as settings
import src.utils.DB.Sql as Sql
from src.utils.DB.configDB import configDB


def auto(ifExcel=True):

    Br=myBrowser.myBrowser("cscec")
    page=Br.page
    print("启动浏览器成功")
    unitList=configDB().financialIntegrationUnits
    contractList=[]
    for i in range(len(unitList)):
        organizationCode=unitList[i][0]
        cscec.toFunction(page,"合约系统","合同查询","合同履行情况明细查询")
        page.locator("//label[contains(text(),'组织机构：')]/parent::div/following-sibling::div[1]//input").click()#点击输入
        page.locator("//label[contains(text(),'组织机构：')]/parent::div/following-sibling::div[1]//input").fill(organizationCode)
        page.get_by_text("合同总金额(含联合体，元)小于：").click()
        page.locator("//img[contains(@src,'查询')]").click()
        cscec.getVisible(page,"//tr//div[normalize-space()='1']",timeout=150)
        page.locator("//div[@title='tips:导出']").click()
        with page.expect_download(timeout=60000) as download_info:
            cscec.clickDigalog(page,"导出Excel","导出")
            download=download_info.value
            download.save_as(path=settings.PATH_DOWNLOAD+"/导出数据/临时文件1.xlsx")
        cscec.closeTab(page)
        cscec.closeTab(page)
        temporaryList=openpyxlExcel.getUsedListByPath(settings.PATH_DOWNLOAD+"/导出数据/临时文件1.xlsx")
        if len(contractList)>0:
            contractList=contractList+temporaryList[1:]
        else:
            contractList=temporaryList+contractList
    Br.Context.close()
    df=pd.DataFrame(contractList[1:],columns=contractList[0])
    con=duckdb.connect(":memory:")
    content = Sql.一体化分供
    Result=con.execute(content).fetchall()
    if ifExcel:
        wb=excel.myBook()
        wb.sheet("财务一体化合同明细").Range("a2").resize(len(Result),len(Result[0])).Value=Result
    else:
        coulums2=[desc[0] for desc in con.description]
        dfnew = pd.DataFrame(Result, columns=coulums2)  # 根据你的查询结果的列名进行修改
        return dfnew




