import duckdb
import src.base.settings as settings
import src.utils.Excel.openpyxlExcel as openpyxlExcel


dropTable='''DROP TABLE IF EXISTS 成本表;
DROP TABLE IF EXISTS 付款台账;
DROP TABLE IF EXISTS 分供结算台账;
DROP TABLE IF EXISTS 内行查询;
DROP TABLE IF EXISTS 确权台账;
DROP TABLE IF EXISTS 收款台账;
DROP TABLE IF EXISTS 外部确权台账;
DROP TABLE IF EXISTS 资金整理;
'''

createTable1='''
CREATE TABLE IF NOT EXISTS 成本表(
财年 varchar,
过帐日期 date,
输入日期 date,
凭证编号 varchar,
中台单据号 varchar,
利润中心 varchar,
利润中心描述 varchar,
成本科目 varchar,
供应商 varchar,
供应商描述 varchar,
文本 varchar,
入账成本 double,
科目分类 varchar,
成本类别 varchar,
PRIMARY KEY (财年, 凭证编号,利润中心,成本科目)
);'''
createTable2='''
CREATE TABLE IF NOT EXISTS 付款台账(
财年 varchar,
过帐日期 date,
输入日期 date,
供应商类型 varchar,
凭证编号 varchar,
利润中心 varchar,
利润中心描述 varchar,
供应商 varchar,
供应商描述 varchar,
合同 varchar,
合同文本描述 varchar,
文本 varchar,
中台单据号 varchar,
总付款金额 double,
扣履约保证金 double ,
供应链保理 double ,
冲成本 double ,
本利润中心 double ,
内行或存款 double ,
内行客商 varchar,
PRIMARY KEY (财年, 凭证编号,利润中心,供应商,合同)
);'''
createTable3='''
CREATE TABLE IF NOT EXISTS 分供结算台账(
财年 varchar,
过帐日期 date,
输入日期 date,
供应商类型 varchar,
凭证编号 varchar,
利润中心 varchar,
利润中心描述 varchar,
供应商 varchar,
供应商描述 varchar,
合同 varchar,
合同文本描述 varchar,
文本 varchar,
中台单据号 varchar,
含税结算金额 double,
进项税 double,
PRIMARY KEY (财年, 凭证编号,利润中心,供应商,合同)
);'''
createTable4='''
CREATE TABLE IF NOT EXISTS 内行查询(
财年 varchar,
过帐日期 date,
输入日期 date,
凭证编号 varchar, 
利润中心 varchar, 
利润中心描述 varchar,
总账科目长文本 varchar,
内行金额 double,
内行客商 varchar,
内行客商描述 varchar,
内行客商数量 varchar,
供应商 varchar,
供应商描述 varchar,
中台单据号 varchar,
事由 varchar,
PRIMARY KEY (财年, 凭证编号,利润中心,总账科目长文本,内行客商)
);'''


createTable6='''
CREATE TABLE IF NOT EXISTS 收款台账(
财年 varchar,
过帐日期 date,
输入日期 date,
凭证编号 varchar,
利润中心 varchar,
利润中心描述 varchar,
客户 varchar,
客户描述 varchar,
合同 varchar,
合同文本描述 varchar,
文本 varchar,
中台单据号 varchar,
总收款金额 double,
本利润中心 double,
内行或存款 double,
内部往来其他收款 double,
内行客商 varchar,
PRIMARY KEY (财年, 凭证编号,利润中心,客户,合同)
);'''

createTable7='''
CREATE TABLE IF NOT EXISTS 专项储备(
凭证编号 varchar, 
财年 varchar,
利润中心 varchar,
利润中心描述 varchar,
文本 varchar,
过帐日期 date,
输入日期 date,
安全生产费 double,
类型 varchar,
PRIMARY KEY (财年, 凭证编号,利润中心)
)
'''

createTable8='''
CREATE TABLE IF NOT EXISTS 主数据(
项目编码 varchar,
利润中心 varchar,
项目所属的核算组织 varchar,
利润中心描述 varchar,
利润中心组描述 varchar,
PRIMARY KEY (利润中心)
)
'''

createTable9='''
CREATE TABLE IF NOT EXISTS 科目对照(
总账科目长文本 varchar,
科目分类1 varchar,
科目分类2 varchar,
科目方向 varchar,
PRIMARY KEY (总账科目长文本)
)
'''

createTable10='''
CREATE TABLE IF NOT EXISTS 内部对账(
客商编码 varchar,
客商名称 varchar,
过帐日期 date,
凭证编号 varchar,
事由 varchar,
结算额 double,
付款额 double,
暂估额 double,
利润中心 varchar,
PRIMARY KEY (客商编码,过帐日期,凭证编号)
)
'''
createTable11='''
CREATE TABLE IF NOT EXISTS 一体化合同台账(
查找主键 varchar,
组织机构名称 varchar,
项目名称 varchar,
项目编号 varchar,
合同名称 varchar,
合同编号 varchar,
原合同编号 varchar,
合同业务内容 varchar,
客商名称 varchar,
客商编号 varchar,
合同类型 varchar,
合同金额 double,
税率 varchar,
结算金额 double,
预付金额 double,
已付金额 double,
发票金额 double,
付款比例 double,
应付余额 double,
拖欠款 double,
PRIMARY KEY (项目名称,合同编号)
)

'''
createTable12='''
CREATE TABLE IF NOT EXISTS 外部确权台账(
财年 varchar,
过帐日期 date,
输入日期 date,
凭证编号 varchar,
利润中心 varchar,
利润中心描述 varchar,
客户 varchar,
客户描述 varchar,
合同 varchar,
合同文本描述 varchar,
文本 varchar,
中台单据号 varchar,
含税结算金额 double,
销项税 double,
PRIMARY KEY (财年, 凭证编号,利润中心,客户,合同)
);'''

createTable13='''
CREATE TABLE IF NOT EXISTS 资金整理(
财年 varchar,
过帐日期 date,
利润中心 varchar,
利润中心描述 varchar,
凭证编号 varchar,
中台单据号2 varchar,
总账科目 varchar,
供应商描述 varchar,
事由 varchar,
内行金额 double,
内外部流水 varchar,
单据分类 varchar,
内行客商标记 varchar,
内行客商标记2 varchar,
PRIMARY KEY (财年, 凭证编号,利润中心,内行客商标记)
);'''


enhance='''
INSTALL spatial;
LOAD spatial;'''
#To export the data from a table to an Excel file, install and load the spatial extension. This is only needed once per DuckDB connection.

def createTable():
    title0=openpyxlExcel.getUsedList("数据库所用标题")
    title=[ele[0] for ele in title0]
    con = duckdb.connect(database=settings.PATH_DUCKDB+'/example.duckdb', read_only=False)
    primary_key = title[0]  # 使用列表的第一个元素作为主键
    sql_create_table = f'CREATE TABLE IF NOT EXISTS 明细帐 ({primary_key} TEXT PRIMARY KEY,'
    for title in title[1:]:
        if title== '过帐日期':
            sql_create_table += f' {title} DATE ,'
        elif title == '输入日期':
            sql_create_table += f' {title} DATE ,'
        elif title == '带符号的本位币金额':
            sql_create_table += f' {title} DOUBLE ,'
        else:
            sql_create_table += f' {title} VARCHAR,' # 其他标题默认为文本类型
    sql_create_table = sql_create_table.rstrip(',') + ')' # 去除最后一个逗号，并加上右括号
    
    # 创建表格
    con.execute(sql_create_table)

    con.execute(dropTable)
    
    con.execute(createTable1)
    con.execute(createTable2)
    con.execute(createTable3)
    con.execute(createTable4)
    con.execute(createTable6)
    con.execute(createTable7)
    con.execute(createTable8)
    con.execute(createTable9)
    con.execute(createTable10)
    con.execute(createTable11)
    con.execute(createTable12)
    con.execute(createTable13)
    #con.execute(enhance)
