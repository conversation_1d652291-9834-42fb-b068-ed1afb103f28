import flet as ft
import src.Gui.callProcess as callF
import src.base.settings as settings
import os


class RD(ft.Column):
    def __init__(self):
        super().__init__()
        self.controls = [
            ft.Container(
                content=ft.Column(
                    controls=[
                        ft.Text("研发费用管理", size=16, weight=ft.FontWeight.BOLD),
                        ft.Row(
                            controls=[
                                ft.ElevatedButton(
                                    text="打开模板文件",
                                    icon=ft.icons.FILE_OPEN,
                                    style=ft.ButtonStyle(
                                        shape=ft.RoundedRectangleBorder(radius=10),
                                        padding=15,
                                    ),
                                    on_click=lambda e: os.startfile(settings.PATH_EXCEL + "/研发费用.xlsx")
                                ),
                                ft.ElevatedButton(
                                    text="生成模板",
                                    icon=ft.icons.CREATE_NEW_FOLDER,
                                    style=ft.ButtonStyle(
                                        shape=ft.RoundedRectangleBorder(radius=10),
                                        padding=15,
                                    ),
                                    on_click=lambda e: callF.thisProcess.run({"功能": "研发生成模板"})
                                ),
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_AROUND,
                        ),
                    ],
                    spacing=10,
                ),
                padding=15,
                border_radius=10,
                bgcolor=ft.colors.SURFACE_VARIANT,
                margin=ft.margin.only(bottom=10),
            ),
            ft.Container(
                content=ft.Column(
                    controls=[
                        ft.Text("库存操作", size=16, weight=ft.FontWeight.BOLD),
                        ft.Row(
                            controls=[
                                ft.ElevatedButton(
                                    text="批量退库",
                                    icon=ft.icons.INVENTORY_2,
                                    style=ft.ButtonStyle(
                                        shape=ft.RoundedRectangleBorder(radius=10),
                                        padding=15,
                                    ),
                                    on_click=lambda e: callF.thisProcess.run({"功能": "研发批量退库"})
                                ),
                                ft.ElevatedButton(
                                    text="批量出库",
                                    icon=ft.icons.OUTBOX,
                                    style=ft.ButtonStyle(
                                        shape=ft.RoundedRectangleBorder(radius=10),
                                        padding=15,
                                    ),
                                    on_click=lambda e: callF.thisProcess.run({"功能": "研发批量出库"})
                                ),
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_AROUND,
                        ),
                    ],
                    spacing=10,
                ),
                padding=15,
                border_radius=10,
                bgcolor=ft.colors.SURFACE_VARIANT,
                margin=ft.margin.only(bottom=10),
            ),
        ]


class salaryWidget(ft.Column):
    def __init__(self):
        super().__init__()
        self.controls = [
            ft.Container(
                content=ft.Column(
                    controls=[
                        ft.Text("劳务派遣费用分割认领", size=16, weight=ft.FontWeight.BOLD),
                        ft.Row(
                            controls=[
                                ft.ElevatedButton(
                                    text="查询劳务派遣认领模板",
                                    icon=ft.icons.DOWNLOAD,
                                    style=ft.ButtonStyle(
                                        shape=ft.RoundedRectangleBorder(radius=10),
                                        padding=15,
                                    ),
                                    on_click=lambda e: callF.thisProcess.run({"功能": "查询劳务派遣认领模板"})
                                ),
                                ft.ElevatedButton(
                                    text="更新劳务派遣认领模板",
                                    icon=ft.icons.UPDATE,
                                    style=ft.ButtonStyle(
                                        shape=ft.RoundedRectangleBorder(radius=10),
                                        padding=15,
                                    ),
                                    on_click=lambda e: callF.thisProcess.run({"功能": "更新劳务派遣认领模板"})
                                ),
                                 ft.ElevatedButton(
                                    text="执行费用分割认领",
                                    icon=ft.icons.EXPLORE,
                                    style=ft.ButtonStyle(
                                        shape=ft.RoundedRectangleBorder(radius=10),
                                        padding=15,
                                    ),
                                    on_click=lambda e: callF.thisProcess.run({"功能": "执行劳派费用分割认领"})
                                ),
                            ],
                            alignment=ft.MainAxisAlignment.CENTER,
                        ),
                    ],
                    spacing=10,
                ),
                padding=15,
                border_radius=10,
                bgcolor=ft.colors.SURFACE_VARIANT,
                margin=ft.margin.only(bottom=10),
            ),
            ft.Container(
                content=ft.Column(
                    controls=[
                        ft.Text("反向保理快速提单", size=16, weight=ft.FontWeight.BOLD),
                        ft.Row(
                            controls=[
                                ft.ElevatedButton(
                                    text="查询反向保理模板",
                                    icon=ft.icons.DOWNLOAD,
                                    style=ft.ButtonStyle(
                                        shape=ft.RoundedRectangleBorder(radius=10),
                                        padding=15,
                                    ),
                                    on_click=lambda e: callF.thisProcess.run({"功能": "查询反向保理模板"})
                                ),
                                ft.ElevatedButton(
                                    text="更新反向保理模板",
                                    icon=ft.icons.UPDATE,
                                    style=ft.ButtonStyle(
                                        shape=ft.RoundedRectangleBorder(radius=10),
                                        padding=15,
                                    ),
                                    on_click=lambda e: callF.thisProcess.run({"功能": "更新反向保理模板"})
                                ),
                                 ft.ElevatedButton(
                                    text="执行反向保理提单",
                                    icon=ft.icons.EXPLORE,
                                    style=ft.ButtonStyle(
                                        shape=ft.RoundedRectangleBorder(radius=10),
                                        padding=15,
                                    ),
                                    on_click=lambda e: callF.thisProcess.run({"功能": "执行反向保理提单"})
                                ),
                            ],
                            alignment=ft.MainAxisAlignment.CENTER,
                        ),
                    ],
                    spacing=10,
                ),
                padding=15,
                border_radius=10,
                bgcolor=ft.colors.SURFACE_VARIANT,
                margin=ft.margin.only(bottom=10),
            ),
            ft.Container(
                content=ft.Column(
                    controls=[
                        ft.Text("快速物资出库", size=16, weight=ft.FontWeight.BOLD),
                        ft.Row(
                            controls=[
                                ft.ElevatedButton(
                                    text="查询物资出库模板",
                                    icon=ft.icons.DOWNLOAD,
                                    style=ft.ButtonStyle(
                                        shape=ft.RoundedRectangleBorder(radius=10),
                                        padding=15,
                                    ),
                                    on_click=lambda e: callF.thisProcess.run({"功能": "查询物资出库模板"})
                                ),
                                ft.ElevatedButton(
                                    text="更新物资出库模板",
                                    icon=ft.icons.UPDATE,
                                    style=ft.ButtonStyle(
                                        shape=ft.RoundedRectangleBorder(radius=10),
                                        padding=15,
                                    ),
                                    on_click=lambda e: callF.thisProcess.run({"功能": "更新物资出库模板"})
                                ),
                                 ft.ElevatedButton(
                                    text="执行物资出库",
                                    icon=ft.icons.EXPLORE,
                                    style=ft.ButtonStyle(
                                        shape=ft.RoundedRectangleBorder(radius=10),
                                        padding=15,
                                    ),
                                    on_click=lambda e: callF.thisProcess.run({"功能": "执行物资出库"})
                                ),
                            ],
                            alignment=ft.MainAxisAlignment.CENTER,
                        ),
                    ],
                    spacing=10,
                ),
                padding=15,
                border_radius=10,
                bgcolor=ft.colors.SURFACE_VARIANT,
                margin=ft.margin.only(bottom=10),
            )
        ]




class salaryTabView(ft.Column):
    def __init__(self):
        super().__init__()
        self.visible = True
        self.expand = True
        # self.spacing=5
        self.navigation_bar = ft.NavigationBar(
            destinations=[
                ft.NavigationBarDestination(icon=ft.icons.PERSON, label="速填模块")
            ],
            on_change=lambda e: self.changeMain(e)
        )
        self.mainRegional = ft.Column(controls=[], alignment=ft.MainAxisAlignment.CENTER)
        self.mainRegional = ft.Column(
            controls=[],
            alignment=ft.MainAxisAlignment.CENTER,
            scroll=ft.ScrollMode.AUTO,
            expand=True,
        )
        self.controls = (self.navigation_bar, self.mainRegional)

        # 创建常用模块的按钮卡片
        self.default = [salaryWidget()]
        self.mainRegional.controls = self.default

    def changeMain(self, e):
        index = e.control.selected_index
        if index == 0:
            self.mainRegional.controls = self.default
        self.mainRegional.update()