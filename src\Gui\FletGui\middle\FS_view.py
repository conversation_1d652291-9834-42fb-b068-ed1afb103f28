import flet as ft
import os
import src.base.settings as settings
import src.Gui.callProcess as callF
from tkinter import filedialog
import src.base.cache as cache



class FilePicker(ft.UserControl):
    def __init__(self):
        super().__init__()
        self.file_path_text = ft.TextField(
            label="文件路径",
            read_only=True,
            value=cache.theLastSheetPath,
            width=400,
            border_radius=10,
            border_color=ft.colors.OUTLINE_VARIANT,
            focused_border_color=ft.colors.PRIMARY,
            text_style=ft.TextStyle(size=14),
        )
        self.file_picker = ft.FilePicker(
            on_result=self.pick_file_result
        )
        self.pick_file_button = ft.ElevatedButton(
            "选择文件",
            icon=ft.icons.FOLDER_OPEN,
            on_click=lambda _: self.file_picker.pick_files(),
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
                text_style=ft.TextStyle(size=14),
            ),
        )
        self.fill_button = ft.ElevatedButton(
            "补全期初数",
            icon=ft.icons.AUTOFPS_SELECT,
            on_click=lambda e: callF.thisProcess.run({"功能": "补全期初数"}),
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
                text_style=ft.TextStyle(size=14),
            ),
        )

    def pick_file_result(self, e: ft.FilePickerResultEvent):
        if e.files:
            self.file_path_text.value = e.files[0].path
            cache.theLastSheetPath = e.files[0].path
            cache.wirteLastPath()
            self.file_path_text.update()

    def build(self):
        return ft.Container(
            content=ft.Column(
                controls=[
                    ft.Row(
                        controls=[
                            self.file_path_text,
                            self.pick_file_button,
                            self.fill_button,
                            self.file_picker,  # 必须添加到页面上，虽然不可见
                        ],
                        alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                        spacing=10,
                    ),
                ],
                spacing=10,
                alignment=ft.MainAxisAlignment.CENTER,
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            ),
            padding=15,
            border_radius=10,
            bgcolor=ft.colors.SURFACE_VARIANT,
            margin=ft.margin.only(bottom=10),
        )

class FilePicker_LastStateACP(ft.UserControl):
    def __init__(self):
        super().__init__()
        self.file_path_text = ft.TextField(
            label="上期国资委报表路径",
            read_only=True,
            value=cache.LastStateAssetsCouncilReportPath,
            width=400,
            border_radius=10,
            border_color=ft.colors.OUTLINE_VARIANT,
            focused_border_color=ft.colors.PRIMARY,
            text_style=ft.TextStyle(size=14),
        )
        self.file_picker = ft.FilePicker(
            on_result=self.pick_file_result
        )
        self.pick_file_button = ft.ElevatedButton(
            "选择文件",
            icon=ft.icons.FOLDER_OPEN,
            on_click=lambda _: self.file_picker.pick_files(),
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
                text_style=ft.TextStyle(size=14),
            ),
        )

    def pick_file_result(self, e: ft.FilePickerResultEvent):
        if e.files:
            self.file_path_text.value = e.files[0].path
            cache.LastStateAssetsCouncilReportPath = e.files[0].path
            cache.wirteLastStateAssetsCouncilReportPath()
            self.file_path_text.update()

    def build(self):
        return ft.Container(
            content=ft.Column(
                controls=[
                    ft.Row(
                        controls=[
                            self.file_path_text,
                            self.pick_file_button,
                            self.file_picker,  # 必须添加到页面上，虽然不可见
                        ],
                        alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                        spacing=10,
                    ),
                ],
                spacing=10,
                alignment=ft.MainAxisAlignment.CENTER,
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            ),
            padding=15,
            border_radius=10,
            bgcolor=ft.colors.SURFACE_VARIANT,
            margin=ft.margin.only(bottom=10),
        )

class FilePicker_CurrentFReport(ft.UserControl):
    def __init__(self):
        super().__init__()
        self.file_path_text = ft.TextField(
            label="当期财务报表路径",
            read_only=True,
            value=cache.CurrentFinancialReport,
            width=400,
            border_radius=10,
            border_color=ft.colors.OUTLINE_VARIANT,
            focused_border_color=ft.colors.PRIMARY,
            text_style=ft.TextStyle(size=14),
        )
        self.file_picker = ft.FilePicker(
            on_result=self.pick_file_result
        )
        self.pick_file_button = ft.ElevatedButton(
            "选择文件",
            icon=ft.icons.FOLDER_OPEN,
            on_click=lambda _: self.file_picker.pick_files(),
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
                text_style=ft.TextStyle(size=14),
            ),
        )
        self.fill_button = ft.ElevatedButton(
            "补全期初数",
            icon=ft.icons.AUTOFPS_SELECT,
            on_click=lambda e: callF.thisProcess.run({"模块":"utils.financialStatement.国资委报表","函数":"fill"}),
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
                text_style=ft.TextStyle(size=14),
            ),
        )

    def pick_file_result(self, e: ft.FilePickerResultEvent):
        if e.files:
            self.file_path_text.value = e.files[0].path
            cache.CurrentFinancialReport = e.files[0].path
            cache.wirteCurrentFinancialReport()
            self.file_path_text.update()

    def build(self):
        return ft.Container(
            content=ft.Column(
                controls=[
                    ft.Row(
                        controls=[
                            self.file_path_text,
                            self.pick_file_button,
                            self.fill_button,
                            self.file_picker,  # 必须添加到页面上，虽然不可见
                        ],
                        alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                        spacing=10,
                    ),
                ],
                spacing=10,
                alignment=ft.MainAxisAlignment.CENTER,
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            ),
            padding=15,
            border_radius=10,
            bgcolor=ft.colors.SURFACE_VARIANT,
            margin=ft.margin.only(bottom=10),
        )

class FSView(ft.Column):
    def __init__(self):
        super().__init__()
        self.visible = True
        self.expand = True
        #self.spacing=5
        self.navigation_bar = ft.NavigationBar(
            destinations=[
                ft.NavigationBarDestination(icon=ft.icons.NOTE_SHARP, label="financialStatement"),
                ft.NavigationBarDestination(icon=ft.icons.HOUSE,label="国资委决算"),
                ft.NavigationBarDestination(icon=ft.icons.PERSON, label="预留1"),
                ft.NavigationBarDestination(icon=ft.icons.PERSON, label="预留2"),
                ft.NavigationBarDestination(icon=ft.icons.PERSON, label="预留3"),
            ],
            on_change=lambda e:self.changeMain(e)
        )
        self.mainRegional=ft.Column(controls=[],alignment=ft.MainAxisAlignment.CENTER)
        self.controls=(self.navigation_bar,self.mainRegional)
        self.default=[ft.Column([FilePicker()])]
        self.mainRegional.controls=self.default

    def changeMain(self,e):
        index=e.control.selected_index
        if index==0:
            self.mainRegional.controls=self.default
        elif index==1:
            self.mainRegional.controls=[ft.Column([FilePicker_LastStateACP(), FilePicker_CurrentFReport()])]
        elif index==2:
            self.mainRegional.controls=[]
        elif index==3:
            self.mainRegional.controls=[]
        elif index==4:
            self.mainRegional.controls=[]
        
        self.mainRegional.update()