import src.base.cache as cache
import src.utils.Excel.excel as excel
from python_calamine import CalamineWorkbook
from pathlib import Path

def get_title_col(inputList,title):
    for i in range(3):
        for j in range(len(inputList[i])):
            if inputList[i][j]==title:
                return j

def get_excel_data(file_path, sheet_name,titleList,amountTitle,keyIFtuple=True):
    workbook = CalamineWorkbook.from_path(file_path)
    theList = workbook.get_sheet_by_name(sheet_name).to_python()
    titleCol=[get_title_col(theList,title) for title in titleList]
    if type(amountTitle)==int:
        amountCol=amountTitle
    else:
        amountCol=get_title_col(theList,amountTitle)
    d={}
    for i in range(len(theList)):
        #判断是否为文本整数
        if theList[i][0].lstrip('-').isdigit() and theList[i][0] != "":
            if keyIFtuple:
                key=tuple(theList[i][col] for col in titleCol)
                if theList[i][amountCol]!='': #如果为空字符则舍弃
                    d[key]=theList[i][amountCol]+d.get(key,[0])
            else:
                key=''.join([theList[i][col] for col in titleCol])
                if theList[i][amountCol]!='': #如果为空字符则舍弃
                    key2=tuple(theList[i][col] for col in titleCol)
                    d[key]=[theList[i][amountCol]+d.get(key,[0])[0],key2]
    return d


def getDataToExcel(balanceSheetPath,inputsheetname,titleList,amoutTitle,outputsheetname):
    d=get_excel_data(balanceSheetPath,inputsheetname,titleList,amoutTitle)
    outputList1=[]
    outputList2=[]
    for key in d:
        outputList1.append(key)
        outputList2.append([d[key]])
    theOutputSheet=excel.myBook("输出表").sheet(outputsheetname)
    theOutputSheet.Range("a2").resize(len(outputList1),len(outputList1[0])).Value=outputList1
    theOutputSheet.Range(2,len(outputList1[0])+1,2,len(outputList1[0])+1).resize(len(outputList2),len(outputList2[0])).Value=outputList2


def completBegingBalance(balanceSheetPath,inputsheetname,titleList,amoutTitle,fillTitle,keyIftuple=True):
    d=get_excel_data(balanceSheetPath,inputsheetname,titleList,amoutTitle,keyIftuple)
    ws=excel.myBook().sheet(inputsheetname)
    usedList=ws.usedrange
    titleCol=[get_title_col(usedList,title) for title in titleList]
    fillCol=get_title_col(usedList,fillTitle)+1
    fillList=[]
    for i in  range(len(usedList)):
        if usedList[i][0]!=None and usedList[i][0].lstrip('-').isdigit() and usedList[i][0] != "":
            startRow=i+1 if usedList[i][0]=="1" else startRow
            if keyIftuple:
                key=tuple(usedList[i][col] for col in titleCol)
                fillList.append([d.get(key,0)])
                d[key]=0 #把已经填过的数据从d中删除,防止重复填,这样key重复出现也是填入0
            else:
                key=''.join([usedList[i][col] if usedList[i][col]!=None else '' for col in titleCol])
                fillList.append([d.get(key,[0])[0]])
                d[key]=[0] #把已经填过的数据从d中删除,防止重复填,这样key重复出现也是填入0


    ws.Range(startRow,fillCol,ws.MaxRow,fillCol).resize(len(fillList),len(fillList[0])).Value=fillList
    #开始补全表格中不存在的key
    usedRow=ws.MaxRow+1
    for key in d:
        if keyIftuple:
            if d[key]!=0:
                    for i in range(len(titleCol)):
                        ws.Cells(usedRow,titleCol[i]+1).Value=key[i]
        else:
            if d[key][0] !=0:
                fillkey=d[key][1]
                for i in range(len(titleCol)):
                    ws.Cells(usedRow,titleCol[i]+1).Value=fillkey[i]
                ws.Cells(usedRow,fillCol).Value=d[key][0]
                usedRow+=1



def complete():
    print("要保证表格常开")
    completBegingBalance(cache.LastYearFinancialReport,"FZ2116 其他应付款",["编码","名称","项目名称","款项性质","填报单位"],10,"年初余额",False)
    getDataToExcel(cache.LastYearFinancialReport,"NBWL2022 内部往来-应付账款",["对方单位名称","科目","填报单位"],"期末余额","应付账款差额")
    getDataToExcel(cache.LastYearFinancialReport,"NBWL2004 内部往来-应收账款",["对方单位名称","科目","填报单位"],11,"应收账款差额") #个别原位币哪里没填
    completBegingBalance(cache.LastYearFinancialReport,"NBWL2004 内部往来-应收账款",["对方单位名称","科目"],"期末数","年初数原值")
    completBegingBalance(cache.LastYearFinancialReport,"ZC2113 其他应收款",["编码","名称","坏账计提方式","客户类别","款项性质","填报单位"],"期末数","期初数",False)

def main():
    print("第一个版本")
    complete()
    #print("测试补全期初数")