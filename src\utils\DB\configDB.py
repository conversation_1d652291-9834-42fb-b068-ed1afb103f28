
#将excel数据存入数据库

import pandas as pd
import sqlite3
import os
import src.base.settings as settings



class configDB:
    def __init__(self, db_path=None):
        """初始化数据库连接"""

        self.conn = sqlite3.connect(settings.PATH_DUCKDB+r"\config.db")
    
    def create_table(self):
        """创建表"""
        cursor = self.conn
        cursor.execute("""
           CREATE TABLE IF NOT EXISTS sapConnection (
    path TEXT,
    username TEXT,
    password TEXT,
    accountBook TEXT  -- 添加数据类型
)""")
        cursor.execute("""
           CREATE TABLE IF NOT EXISTS sapProfitCenter (
    profitCenterGroup TEXT,
    companyCode TEXT,
    profitCenterGroupName TEXT
)""")   
        
        cursor.execute("""
CREATE TABLE IF NOT EXISTS excludedProfitCenters (
    description TEXT,
    isEnabled integer
);""")
        
        cursor.execute("""
CREATE TABLE IF NOT EXISTS documentTypes (
    description TEXT,
    isEnabled integer
);""")
        cursor.execute("""
CREATE TABLE IF NOT EXISTS financialIntegrationUnits (
    unitName TEXT,
    unitCode TEXT
);""")
        cursor.execute("""
CREATE TABLE IF NOT EXISTS fqUserSettings (
    username TEXT,
    upassword TEXT  -- 删除多余的逗号
);""")
        cursor.execute("""
CREATE TABLE IF NOT EXISTS unitMapping (
    sourceUnitName TEXT,
    sourceUnitCode TEXT,
    targetUnitName TEXT
);""")
        cursor.execute("""

CREATE TABLE IF NOT EXISTS expenseTransfer (
    expenseTransfer TEXT  -- 删除多余的逗号
);""")
        cursor.execute("""
CREATE TABLE IF NOT EXISTS internalCustomers (
    superior_customer_code TEXT,
    superior_customer_name TEXT
);    """)   
    
    def getJson(self):
        tables=['sapProfitCenter','excludedProfitCenters','documentTypes','financialIntegrationUnits','unitMapping','expenseTransfer','internalCustomers']
        data={}
        for table in tables:
            cursor=self.conn.cursor()
            rows=cursor.execute(f'SELECT * FROM {table}').fetchall()
            columns = [desc[0] for desc in cursor.description]
            # 转换为JSON格式
            data[table] = []
            for row in rows:
                data[table].append(dict(zip(columns, row)))
        for table in ["sapConnection",'fqUserSettings',]:
            cursor=self.conn.cursor()
            rows=cursor.execute(f'SELECT * FROM {table}').fetchall()
            columns = [desc[0] for desc in cursor.description]
            data[table]=dict(zip(columns, rows[0]))
        return data

    def saveJson(self,data:dict):
        key=list(data.keys())[0]
        if key == 'sapConnection':
            self.delete("sapConnection")
            item=data[key]
            self.conn.execute("""insert into sapConnection (path,username,password,accountBook) values (?,?,?,?)""",(item['path'],item['username'],item['password'],item['accountBook']))
            self.conn.commit()
        if key == 'sapProfitCenter':
            self.delete("sapProfitCenter")
            for item in data[key]:
                self.conn.execute("""insert into sapProfitCenter (profitCenterGroup,companyCode,profitCenterGroupName) values (?,?,?)""",(item['profitCenterGroup'],item['companyCode'],item['profitCenterGroupName']))
            self.conn.commit()
        if key == 'excludedProfitCenters':
            self.delete("excludedProfitCenters")
            for item in data[key]:
                self.conn.execute("""insert into excludedProfitCenters (description,isEnabled) values (?,?)""",(item['description'],item['isEnabled']))
            self.conn.commit()
        if key == 'documentTypes':
            self.delete("documentTypes")
            for item in data[key]:
                self.conn.execute("""insert into documentTypes (documentTypeDescription,isEnabled) values (?,?)""",(item['documentTypeDescription'],item['isEnabled']))
            self.conn.commit()
        if key == 'financialIntegrationUnits':
            self.delete("financialIntegrationUnits")
            for item in data[key]:
                self.conn.execute("""insert into financialIntegrationUnits (unitName,unitCode) values (?,?)""",(item['unitName'],item['unitCode']))
            self.conn.commit()
        if key == 'fqUserSettings':
            self.delete("fqUserSettings")
            item=data[key]
            self.conn.execute("""insert into fqUserSettings (username,password) values (?,?)""",(item['username'],item['password']))
            self.conn.commit()
        if key == 'unitMapping':
            self.delete("unitMapping")
            for item in data[key]:
                self.conn.execute("""insert into unitMapping (sourceUnitName,sourceUnitCode,targetUnitName) values (?,?,?)""",(item['sourceUnitName'],item['sourceUnitCode'],item['targetUnitName']))
            self.conn.commit()
        if key == 'expenseTransfer':
            self.delete("expenseTransfer")
            for item in data[key]:
                self.conn.execute("""insert into expenseTransfer (description) values (?)""",(item['description'],))
            self.conn.commit()
        if key == 'internalCustomers':
            self.delete("internalCustomers")
            for item in data[key]:
                self.conn.execute("""insert into internalCustomers (superior_customer_code,superior_customer_name) values (?,?)""",(item['superior_customer_code'],item['superior_customer_name']))
            self.conn.commit()
        #清楚缓存
        self.conn.execute("""VACUUM;""")
        self.conn.commit()
    
    @property
    def internalCustomers(self):
        cursor=self.conn.cursor()
        rows=cursor.execute("SELECT * FROM internalCustomers").fetchall()
        return rows
    
    @property
    def sapConnection(self):
        cursor=self.conn.cursor()
        rows=cursor.execute("SELECT * FROM sapConnection").fetchall()[0]
        configDict=dict(zip([desc[0] for desc in cursor.description], rows))
        return configDict
    
    @property
    def fqUserSettings(self):
        cursor=self.conn.cursor()
        rows=cursor.execute("SELECT * FROM fqUserSettings").fetchall()[0]
        configDict=dict(zip([desc[0] for desc in cursor.description], rows))
        return configDict

    @property
    def sapProfitCenter(self):
        cursor=self.conn.cursor()
        rows=cursor.execute("SELECT  profitCenterGroup,companyCode,'是',profitCenterGroupName FROM sapProfitCenter").fetchall()
        return rows
        
    @property
    def excludedProfitCenters(self):
        cursor=self.conn.cursor()
        rows=cursor.execute("SELECT description,case when isEnabled=1 then '是' else '否' end as isEnabled FROM excludedProfitCenters").fetchall()
        rows_dict={row[0]:row[1] for row in rows}
        return rows_dict

    @property
    def documentTypes(self):
        cursor=self.conn.cursor()
        rows=cursor.execute("SELECT documentTypeDescription,case when isEnabled=1 then '是' else '否' end as isEnabled FROM documentTypes").fetchall()
        rows_dict={row[0]:row[1] for row in rows}
        return rows_dict
    
    @property
    def financialIntegrationUnits(self):
        cursor=self.conn.cursor()
        rows=cursor.execute("SELECT unitCode,unitName FROM financialIntegrationUnits").fetchall()
        return rows
    
    @property
    def unitMapping(self):
        cursor=self.conn.cursor()
        rows=cursor.execute("SELECT * FROM unitMapping").fetchall()
        return rows
    
    @property
    def expenseTransfer(self):
        cursor=self.conn.cursor()
        rows=cursor.execute("SELECT * FROM expenseTransfer").fetchall()
        return rows
    
    @property
    def internalCustomers(self):
        cursor=self.conn.cursor()
        rows=cursor.execute("SELECT superior_customer_name FROM internalCustomers").fetchall()
        return rows
    
    def delete(self,tableName:str):
        self.conn.execute(f"DELETE FROM {tableName}")
        self.conn.commit()   
          
    def close(self):
        """关闭数据库连接"""
        if isinstance(self.db, sqlite3.Connection):
            self.conn.close()




