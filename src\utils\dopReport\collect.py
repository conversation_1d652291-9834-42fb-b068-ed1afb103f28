
from playwright.sync_api import Page
from playwright.sync_api import ChromiumBrowserContext
import time
from datetime import datetime
import src.utils.cscec as cscec
import src.utils.Browser.Browser as browser
import src.utils.Excel.excel as excel
from src.utils.DB.configDB import configDB
def month_diff(date1_str, date2_str):
    date1 = datetime.strptime(date1_str, "%Y-%m-%d")
    date2 = datetime.strptime(date2_str, "%Y-%m-%d")

    # 计算月份差异
    month_diff = (date2.year - date1.year) * 12 + (date2.month - date1.month)

    return month_diff


def autoOne(page:Page,d: dict):  
    page.click("//button[@class='ant-btn ant-btn-primary list-action-add']") #点击新增收入成本表
    frame=page.frame_locator("//*[@id='portal-form-list-container']/div[4]/iframe")
#选择项目
    frame.locator("//div[@title='项目']/parent::div/following-sibling::div[1]//input[@placeholder='请选择']/parent::span").click()
    frame.locator("//div[@id='projectName']//div[@class='field__control']//div//div[@class='text']//div//input[@placeholder='请输入']").fill(d["项目名称"])
    frame.locator("//div[@class='ant-modal-body']//button[2]").click()
    frame.get_by_text(d["项目编码"]).click() #注意编码
    frame.locator("//div[@class='ant-modal-footer']//button[2]").click()
    time.sleep(3)
#选择项目完成                   
#选择日期             
    frame.locator("//input[@class='ant-calendar-picker-input ant-input']").click()

    current_date = datetime.now().strftime("%Y-%m-%d")
    date2_str = d["过帐日期"].strftime("%Y-%m-%d")
    diff = month_diff(date2_str, current_date)
    if d["金额"]!=0:
        for j in range(1,diff+1):
            frame.locator("//a[@title='上个月 (翻页上键)']").click()
        frame.locator("//td[@title='"+d["收款标准日期"]+"']").click()
    else:
        frame.locator("//td[@title='"+d["收款标准日期"]+"']").click()

    frame.locator("//*[@id='moneyReceive']/div[2]/div/div/div/input").click()
    frame.locator("//div[@class='h3-input-number h3-input-number-focused']//input[@placeholder='请输入']").fill(str(d["金额"])) #总收款
    frame.locator("//span[text()='保 存']/parent::button").click()


def autoOperate():
    dopId=configDB().fqUserSettings
    theBrowser=browser.myBrowser("3b",dopId["username"],dopId["password"])
    page=theBrowser.page
    theBrowser.goCscec3bReceive()
    ws=excel.myBook().sheet("收款登记")
    table=ws.table("表收款导入")
    start_number=int(ws.Cells(1,2).Value)+1
    max_number=table.MaxRow
    for i in range(start_number,max_number+1):  #跳过标题行+1
        d=table.toDict(i)
        autoOne(page,d)
        ws.Cells(1,2).Value=ws.Cells(1,2).Value+1
    

