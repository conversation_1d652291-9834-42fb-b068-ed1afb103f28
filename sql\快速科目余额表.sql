select 
any_value(明细帐.总账科目) as 总账科目,
any_value(明细帐.总账科目长文本) as 总账科目长文本,
sum(case when (过帐日期<'期初日期留 00:00:00')  then 带符号的本位币金额 else null end) as 期初余额,
sum(case when (过帐日期<='期末日期留 00:00:00')  then 带符号的本位币金额 else null end) as 期末余额,
sum(case when (((借贷标识 = 'H' and 反记帐 = '') or (借贷标识 = 'S' and 反记帐 = 'X')) and ((过帐日期 >= '期初日期留 00:00:00') and (过帐日期<='期末日期留 00:00:00')) ) then 带符号的本位币金额 else null end) as 本期贷方发生额,
sum(case when (((借贷标识 = 'S' and 反记帐 = '') or (借贷标识 = 'H' and 反记帐 = 'X')) and ((过帐日期 >= '期初日期留 00:00:00') and (过帐日期<='期末日期留 00:00:00'))) then 带符号的本位币金额 else null end) as 本期借方发生额,
any_value(科目分类2) as 科目再分类,
any_value(利润中心组描述) as 利润中心组描述
from 明细帐 left join 主数据 on 明细帐.利润中心 = 主数据.利润中心 left join 科目对照 on 明细帐.总账科目长文本=科目对照.总账科目长文本  where 文本 != '自动清账剩余项目'  GROUP BY 明细帐.总账科目长文本,利润中心组描述