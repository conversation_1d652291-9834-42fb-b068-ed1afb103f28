import flet as ft
from .ui_constants import (
    TEXT_COLOR,
    BG_COLOR,
)

def get_view(page: ft.Page, view_display_name: str, show_home_callback):
    """
    Returns the Flet controls for the System Collaboration view.
    """
    async def _handle_return_click(e):
        await show_home_callback()

    return ft.View(
        bgcolor=BG_COLOR,
        appbar=ft.AppBar(
            title=ft.Text(f"{view_display_name}", color=TEXT_COLOR),
            bgcolor=BG_COLOR,
            actions=[
                ft.IconButton(
                    ft.icons.HOME,
                    tooltip="Return to Home",
                    on_click=_handle_return_click,
                    icon_color=TEXT_COLOR
                )
            ]
        ),
        controls=[
            ft.Column(
                [
                    ft.Text(f"Welcome to {view_display_name}", size=20, color=TEXT_COLOR),
                    ft.Text("This is a placeholder for the System Collaboration functionality.", color=TEXT_COLOR),
                ],
                alignment=ft.MainAxisAlignment.CENTER,
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                expand=True
            )
        ]
    )
