import win32com.client
import src.utils.Excel.mylist as mylist
class mySheet():
    def __init__(self,wb,worksheet_name) -> None:
        self._sheet=wb.Worksheets(worksheet_name)
        self.Name=worksheet_name
    
    @property
    def sheet(self):
        return self._sheet
    @property
    def usedrange(self):
        arr=self._sheet.UsedRange.Value
        arr2=[]
        for i in range(len(arr)):
            arr2.append(list(arr[i]))
        return arr2
    
    @property
    def usedrangemyList(self):
        return mylist.mylist(self.usedrange)

    @property
    def MaxRow(self):
        return self._sheet.UsedRange.Rows.Count

    @property
    def MaxCol(self):
        return self._sheet.UsedRange.Columns.Count
    
    def table(self,name):
        return table(self._sheet,name)
    
    def Cells(self,row,col):
        return worksheetCell(self._sheet,row,col)
    
    def Range(self,rowStartOrname,colStart=None,rowEnd=None,colEnd=None):
        return worksheetRange(self._sheet,rowStartOrname,colStart,rowEnd,colEnd)
    
    def deleteUsedRange(self,start_row):
        self._sheet.Activate() #必须先激活
        self._sheet.AutoFilterMode=False #取消筛选
        end_Row=self._sheet.UsedRange.Rows.Count
        if end_Row>=start_row:
            self._sheet.Range(f"{start_row}:{end_Row}").Clear()
            self._sheet.Range(f"{start_row}:{end_Row}").Delete()

    def getSelectAddress(self):
        selected_cell=self._sheet.Application.Selection
        cell_reference = selected_cell.Address
        return cell_reference
    
    def getSheetName(self):
        return self._sheet.Name

    

        
    
class worksheetCell():
    def __init__(self,ws,row,col) -> None:
        self._cell=ws.Cells(row,col)

    @property
    def Value(self):
        return self._cell.Value
    @Value.setter
    def Value(self,value):
        self._cell.Value=value

class worksheetRange():
    def __init__(self,ws,rowStartOrname,colStart,rowEnd,colEnd) -> None:
        self._range=self._range_(ws,rowStartOrname,colStart,rowEnd,colEnd)

    @property
    def Value(self):
        arr=self._range.Value
        arr2=[]
        for i in range(len(arr)):
            arr2.append(list(arr[i]))
        return arr2
    @Value.setter
    def Value(self,value):
        self._range.Value=value

    def resize(self,row,col):
        self._range=self._range.GetResize(row,col)
        return self
    
    @property
    def ValuemyList(self):
        return mylist.mylist(self.Value)

    def _range_(self,ws,rowStartOrname,colStart,rowEnd,colEnd):
        if type(rowStartOrname)==str:
            return ws.Range(rowStartOrname)
        else:
            return ws.Range(ws.Cells(rowStartOrname,colStart),ws.Cells(rowEnd,colEnd))#数字调用必须传入cell对象,且有开始结束

class table():
    def __init__(self,ws,powertable_name) -> None:
        self._listObject=ws.ListObjects(powertable_name)
        self._table= ws.ListObjects(powertable_name).Range
        self._dictTitle=self._getTitle()

    def getValue(self,row,col):
        if type(col)!=str:
            return self._table.Item(row, col).Value
        else:
            return self._table.Item(row, self._dictTitle[col]).Value
    
    def setValue(self,row,col,value):
        if type(col)!=str:
            self._table.Item(row, col).Value=value
        else:
            self._table.Item(row, self._dictTitle[col]).Value=value
    def _getTitle(self):
        d={}
        for col in range(1,self._table.Columns.Count+1):
            key = self._table.Item(1, col).Value
            d[key]=col
        return d
    
    def toDict(self,rowInt:int):
        d={}
        for col in range(1,self._table.Columns.Count+1):
            key = self._table.Item(1, col).Value
            d[key]=self._table.Item(rowInt, col).Value
        return d
    def getColumns(self,title:str):
        return self._listObject.ListColumns(self._dictTitle[title]).Range.Value
    @property
    def MaxRow(self):
        return self._table.Rows.Count
    @property
    def MaxCol(self):
        return self._table.Columns.Count


class myBook():
    def __init__(self,workName=None) -> None:
        self._book=self._returnWorkbook_(workName)
    
    def _returnWorkbook_(self,workName):
        if workName==None:
            try:
                et = win32com.client.Dispatch("Excel.Application")
                wb=et.ActiveWorkbook
                path=wb.Path
            except:
                try:
                    et = win32com.client.Dispatch("Ket.Application")
                    wb=et.ActiveWorkbook
                    path=wb.Path
                except:
                    # 如果都失败了，尝试使用DispatchEx
                    et = win32com.client.DispatchEx("Excel.Application")
                    wb=et.ActiveWorkbook
                    path=wb.Path
            return wb
        else:
            try:
                et = win32com.client.Dispatch("Excel.Application")
                try:
                    wb=et.Workbooks(workName+".xlsm")
                    path=wb.Path
                except:
                    wb=et.Workbooks(workName+".xlsx")
                    path=wb.Path
            except:
                try:
                    et = win32com.client.Dispatch("Ket.Application")
                    try:
                        wb=et.Workbooks(workName+".xlsm")
                        path=wb.Path
                    except:
                        wb=et.Workbooks(workName+".xlsx")
                        path=wb.Path
                except:
                    # 如果都失败了，尝试使用DispatchEx
                    et = win32com.client.DispatchEx("Excel.Application")
                    try:
                        wb=et.Workbooks(workName+".xlsm")
                        path=wb.Path
                    except:
                        wb=et.Workbooks(workName+".xlsx")
                        path=wb.Path
            return wb
    
    def sheet(self,worksheet_name=None):
        if worksheet_name==None:
            return mySheet(self._book,self._book.ActiveSheet.Name)
        else:
            return mySheet(self._book,worksheet_name)
    
    @property
    def path(self):
        return self._book.Path
    
    def runMacro(self,macroName):
        self._book.Application.Run(macroName)

class myExcel():
    def __init__(self) -> None:
        try:
            self._excel=win32com.client.DispatchEx("Excel.Application")
            self._excel.Visible = True
        except:
            self._excel=win32com.client.DispatchEx("Ket.Application")
            self._excel.Visible = True
    
    def openWorkbook(self,file_path):
        return self._excel.Workbooks.Open(Filename=file_path)

def openExcel(file_path):
    try:
        try:
            et = win32com.client.Dispatch("Excel.Application")
            wb=et.Workbooks(file_path)
        except:
            et = win32com.client.Dispatch("Ket.Application")
            wb=et.Workbooks(file_path)
    except:
        import os
        os.startfile(file_path)