import flet as ft
from flet import (
    Page,
    View,
    AppBar,
    Text,
    Column,
    Row,
    Container,
    Icon,
    icons,
    colors,
    padding,
    margin,
    border_radius,
    border,
    MainAxisAlignment,
    CrossAxisAlignment,
    alignment,
    FontWeight,
    TextAlign,
    TextThemeStyle,
    IconButton,
    PopupMenuButton,
    PopupMenuItem,
    PopupMenuTheme,
    TextButton,
    ElevatedButton,
    OutlinedButton,
    Switch,
    Divider,
    Card,
    ListTile,
    ListView
)

def main(page: Page):
    page.title = "Flet Desktop App"
    page.theme_mode = ft.ThemeMode.LIGHT
    page.padding = 0
    
    # Set gradient background
    page.bgcolor = None
    page.window_bgcolor = None
    page.window_frameless = False
    page.theme = ft.Theme(
        color_scheme_seed=ft.colors.BLUE,
        visual_density=ft.ThemeVisualDensity.COMFORTABLE,
    )
    
    # Create a container with gradient background
    gradient_bg = ft.Container(
        gradient=ft.LinearGradient(
            begin=ft.alignment.top_center,
            end=ft.alignment.bottom_center,
            colors=[
                ft.colors.BLUE_50,
                ft.colors.BLUE_100,
                ft.colors.WHITE,
            ],
        ),
        expand=True,
    )
    
    # Create a column to hold all content
    main_content = ft.Column(expand=True, spacing=0)
    
    # Add gradient background and main content to a stack
    page.add(ft.Stack([gradient_bg, main_content]))
    
    # Update the main content reference to use our new container
    content_area = main_content
    page.window_width = 1200
    page.window_height = 800
    page.window_resizable = True

    # State variables
    selected_index = 0
    
    # Navigation items with corresponding content
    nav_items = [
        {
            "icon": icons.DASHBOARD, 
            "label": "Dashboard",
            "content": Column(
                expand=True,
                alignment=ft.MainAxisAlignment.CENTER,
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                controls=[
                    ft.Icon(icons.DASHBOARD, size=64, color=colors.BLUE_400),
                    ft.Text("Dashboard", style=ft.TextThemeStyle.HEADLINE_MEDIUM),
                    ft.Text("Welcome to your dashboard", 
                           style=ft.TextThemeStyle.BODY_MEDIUM, color=colors.GREY_600),
                ],
            )
        },
        {
            "icon": icons.ANALYTICS, 
            "label": "Analytics",
            "content": Column(
                expand=True,
                alignment=ft.MainAxisAlignment.CENTER,
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                controls=[
                    ft.Icon(icons.ANALYTICS, size=64, color=colors.GREEN_400),
                    ft.Text("Analytics", style=ft.TextThemeStyle.HEADLINE_MEDIUM),
                    ft.Text("View your analytics data here", 
                           style=ft.TextThemeStyle.BODY_MEDIUM, color=colors.GREY_600),
                ],
            )
        },
        {
            "icon": icons.SETTINGS, 
            "label": "Settings",
            "content": Column(
                expand=True,
                alignment=ft.MainAxisAlignment.CENTER,
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                controls=[
                    ft.Icon(icons.SETTINGS, size=64, color=colors.AMBER_400),
                    ft.Text("Settings", style=ft.TextThemeStyle.HEADLINE_MEDIUM),
                    ft.Text("Configure your application settings", 
                           style=ft.TextThemeStyle.BODY_MEDIUM, color=colors.GREY_600),
                ],
            )
        },
        {
            "icon": icons.PEOPLE, 
            "label": "Team",
            "content": Column(
                expand=True,
                alignment=ft.MainAxisAlignment.CENTER,
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                controls=[
                    ft.Icon(icons.PEOPLE, size=64, color=colors.PURPLE_400),
                    ft.Text("Team", style=ft.TextThemeStyle.HEADLINE_MEDIUM),
                    ft.Text("Manage your team members", 
                           style=ft.TextThemeStyle.BODY_MEDIUM, color=colors.GREY_600),
                ],
            )
        },
        {
            "icon": icons.DESCRIPTION, 
            "label": "Documents",
            "content": Column(
                expand=True,
                alignment=ft.MainAxisAlignment.CENTER,
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                controls=[
                    ft.Icon(icons.DESCRIPTION, size=64, color=colors.ORANGE_400),
                    ft.Text("Documents", style=ft.TextThemeStyle.HEADLINE_MEDIUM),
                    ft.Text("Browse your documents", 
                           style=ft.TextThemeStyle.BODY_MEDIUM, color=colors.GREY_600),
                ],
            )
        },
    ]

    # Sample messages
    messages = [
        {"sender": "John Doe", "content": "Meeting at 3 PM", "time": "2:30 PM"},
        {"sender": "Jane Smith", "content": "Project update needed", "time": "1:15 PM"},
        {"sender": "Team Updates", "content": "New feature deployed", "time": "10:45 AM"},
        {"sender": "System", "content": "Backup completed", "time": "Yesterday"},
    ]

    # Navigation rail (left sidebar)
    def change_route(e):
        nonlocal selected_index, content_area
        selected_index = e.control.selected_index
        
        # Update the content area with the selected view
        content_area.content = nav_items[selected_index]["content"]
        page.update()

    nav_rail = ft.NavigationRail(
        selected_index=selected_index,
        label_type=ft.NavigationRailLabelType.ALL,
        min_width=80,
        min_extended_width=200,
        leading=ft.Container(
            height=80,
            alignment=ft.alignment.center,
            content=ft.Icon(icons.APPS, size=32),
        ),
        group_alignment=-0.9,
        destinations=[
            ft.NavigationRailDestination(
                icon_content=ft.Icon(item["icon"]),
                selected_icon_content=ft.Icon(item["icon"], color=colors.BLUE_400),
                label=item["label"],
                padding=10,
            )
            for item in nav_items
        ],
        on_change=change_route,
        bgcolor=colors.GREY_50,
    )

    # Main content area
    content_area = ft.Container(
        expand=True,
        padding=20,
        content=nav_items[0]["content"]  # Start with the first view
    )

    # Messages panel (right sidebar)
    messages_header = ft.Container(
        padding=15,
        border=ft.border.only(bottom=ft.border.BorderSide(1, colors.GREY_200)),
        content=ft.Row(
            controls=[
                ft.Text("Messages", weight=FontWeight.BOLD, size=16),
                ft.Container(expand=True),
                ft.IconButton(icon=icons.MORE_VERT, icon_size=20),
            ]
        ),
    )

    message_items = ft.ListView(
        expand=True,
        spacing=0,
        padding=0,
        controls=[
            ft.Container(
                padding=15,
                border=ft.border.only(bottom=ft.border.BorderSide(1, colors.GREY_100)),
                on_click=lambda e, msg=msg: print(f"Selected: {msg['sender']}"),
                content=Column(
                    spacing=4,
                    controls=[
                        Row(
                            controls=[
                                Text(msg["sender"], weight=FontWeight.W_500, size=14, expand=True),
                                Text(msg["time"], size=12, color=colors.GREY_500),
                            ]
                        ),
                        Text(msg["content"], size=13, color=colors.GREY_700),
                    ]
                ),
            )
            for msg in messages
        ],
    )

    messages_panel = ft.Container(
        width=300,
        border=ft.border.only(left=ft.border.BorderSide(1, colors.GREY_200)),
        content=Column(
            expand=True,
            spacing=0,
            controls=[
                messages_header,
                message_items,
            ],
        ),
    )

    # Main layout
    main_row = ft.Row(
        expand=True,
        spacing=0,
        controls=[
            # Left navigation
            nav_rail,
            # Main content
            content_area,
            # Right messages panel
            messages_panel,
        ],
    )

    # Add everything to the page
    page.add(main_row)

if __name__ == "__main__":
    ft.app(target=main)