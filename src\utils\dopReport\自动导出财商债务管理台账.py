
import src.utils.Browser.Browser as myBrowser
import src.utils.cscec as cscec
import time
import src.base.settings as settings
import pandas as pd

def auto(month):
    id='156997'
    password='^*QJ96K*xk3AH2'
    page=myBrowser.myBrowser("3b",id,password).page
    print("启动浏览器成功")
    page.locator("//span[@title='资金管理']").click()
    page.locator("//i[@aria-label='图标: close']/parent::span/parent::button").click()
    page.locator("//span[@title='资金收支管理']").click()
    page.locator("//a[@title='债务管理台账']/parent::li").click()
    page.locator("//li[text()='债务管理台账']/following-sibling::li[1]").click()
    #等待出现
    page.locator("//*[@id='ApplicationList']/div[1]/div[1]/div").click()
    time.sleep(3)
    page.locator("//label[text()='期间']").click()
    cscec.getVisible(page,"//span[text()='导 出']/parent::button/parent::div/preceding-sibling::div[1]/div").click()
    #page.locator("//span[text()='导 出']/parent::button/parent::div/preceding-sibling::div[1]/div").click()
    #上面点击过滤

    page.locator("//div[@id='periodName']//div[@class='field__control']//div//div[@class='text']//div//input[@placeholder='请输入']").fill(month)
    page.locator("//span[text()='查 询']/parent::button").click()
    cscec.getVisible(page,"//span[text()='导 出']/parent::button").click()
    page.locator("//span[text()='全选']/preceding-sibling::label").click()
    with page.expect_download(timeout=60000) as download_info:
        page.locator("//div[@class='ant-modal-root']//button[2]").click()
        download=download_info.value
        download.save_as(settings.PATH_INTERNAL+"/财商下载/财商债务台账原始数据.xlsx")
    df=pd.read_excel(settings.PATH_INTERNAL+"/财商下载/财商债务台账原始数据.xlsx",header=1)
    df.to_excel("C:/Users/<USER>/Desktop/测试.xlsx")

    



