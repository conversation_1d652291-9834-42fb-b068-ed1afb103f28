
select 
利润中心描述,总账科目长文本,
sum(case when (过帐日期<"2019-01-01 00:00:00")  then 带符号的本位币金额 else null end) as "期初余额",
sum(带符号的本位币金额) as "期末余额",
sum(case when ((("记账方向" = "H" and 反记账 = "") or ("记账方向" = "S" and 反记账 = "X")) and ((过帐日期 >= "2019-01-01 00:00:00") and (过帐日期<="2024-04-30 00:00:00")) ) then 带符号的本位币金额 else null end) as "借方发生额",
sum(case when ((("记账方向" = "S" and 反记账 = "") or ("记账方向" = "H" and 反记账 = "X")) and ((过帐日期 >= "2019-01-01 00:00:00") and (过帐日期<="2024-04-30 00:00:00"))) then 带符号的本位币金额 else null end) as "贷方发生额"
from 明细帐 where 文本 != "自动清账剩余项目" and 利润中心描述 like "%金控%" GROUP BY 总账科目长文本,利润中心
#科目余额表


WITH a AS (
    SELECT  凭证编号, 利润中心, 财年
    FROM 明细帐
    WHERE (总账科目长文本 LIKE "应付账款%款%") and (文本!="自动清账剩余项目")
    AND (("记账方向" = "S" AND 反记账 = "") OR ("记账方向" = "H" AND 反记账 = "X"))  GROUP BY 利润中心, 凭证编号, 财年
),
--
b as (
SELECT a.凭证编号, a.财年,a.利润中心,
       SUM(case when (总账科目长文本 LIKE "%直接%费%") then 明细帐.带符号的本位币金额 ELSE NULL END) AS "冲成本",
       SUM(CASE WHEN (明细帐.总账科目长文本 LIKE "%可用存款%") THEN 明细帐.带符号的本位币金额 ELSE NULL END) AS "内行"
FROM a
LEFT JOIN  明细帐 ON a.利润中心 = 明细帐.利润中心 AND a.凭证编号 = 明细帐.凭证编号 AND a.财年 = 明细帐.财年
GROUP BY a.利润中心, a.凭证编号, a.财年 ),
--
c as (
SELECT  凭证编号, 利润中心, 财年, 客户, 客户描述
    FROM 明细帐
    WHERE (总账科目长文本 LIKE "%可用存款") 
 GROUP BY 利润中心, 凭证编号, 财年),
 --
d as (
select * from b LEFT  JOIN c ON b.利润中心 = c.利润中心 AND b.凭证编号 = c.凭证编号 AND b.财年 = c.财年),
--
e as (SELECT  凭证编号, 利润中心, 财年,供应商,供应商描述,文本,利润中心描述,过帐日期,中台单据号,
sum(总账科目长文本) as "总付款金额"
FROM 明细帐
WHERE (总账科目长文本 LIKE "应付账款%款%") and (文本!="自动清账剩余项目")
AND (("记账方向" = "S" AND 反记账 = "") OR ("记账方向" = "H" AND 反记账 = "X"))  GROUP BY 利润中心, 凭证编号, 财年,供应商)
--
SELECT * from d LEFT  JOIN e ON d.利润中心 = e.利润中心 AND d.凭证编号 = e.凭证编号 AND d.财年 = e.财年

sqllite默认是内连接，务必记得使用left join



WITH a AS (
    SELECT  凭证编号, 利润中心, 财年
    FROM 明细帐
    WHERE (总账科目长文本 LIKE "%供应链%") 
    GROUP BY 利润中心, 凭证编号, 财年
)
--
SELECT a.凭证编号, a.利润中心, a.财年,文本,利润中心描述,过帐日期,中台单据号,总账科目长文本,带符号的本位币金额,供应商,供应商描述,客户,客户描述 from a LEFT  JOIN 明细帐 ON a.利润中心 = 明细帐.利润中心 AND a.凭证编号 = 明细帐.凭证编号 AND a.财年 = 明细帐.财年



WITH a AS (
    SELECT  凭证编号, 利润中心, 财年
    FROM 明细帐
    WHERE (总账科目长文本 LIKE "%内部往来%其他%") 
    GROUP BY 利润中心, 凭证编号, 财年
),
--
 b AS (
    SELECT  凭证编号, 利润中心, 财年
    FROM 明细帐
    WHERE (总账科目长文本 LIKE "%存款%") 
    GROUP BY 利润中心, 凭证编号, 财年
),
c AS (
    SELECT  *
    from a   JOIN b ON a.利润中心 = b.利润中心 AND a.凭证编号 = b.凭证编号 AND a.财年 = b.财年
)
--
SELECT c.凭证编号, c.利润中心, c.财年,文本,利润中心描述,过帐日期,中台单据号,总账科目长文本,带符号的本位币金额,供应商,供应商描述,客户,客户描述 from c LEFT  JOIN 明细帐 ON c.利润中心 = 明细帐.利润中心 AND c.凭证编号 = 明细帐.凭证编号 AND c.财年 = 明细帐.财年