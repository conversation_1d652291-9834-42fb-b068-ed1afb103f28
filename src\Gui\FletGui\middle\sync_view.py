import flet as ft
import src.Gui.callProcess as callF
import src.base.cache as cache
import src.base.settings as settings
import os
from datetime import datetime
import src.Gui.FletGui.components.call_component as call_component


class changeData(ft.Column):
    def __init__(self):
        super().__init__()
        self.visible = True
        self.expand = True
        self.spacing = 15
        
        self.commbox = ft.Dropdown(
            width=220,
            options=[
                ft.dropdown.Option("科目对照"),
                ft.dropdown.Option("主数据"),
                ft.dropdown.Option("付款台账"),
                ft.dropdown.Option("收款台账"),
                ft.dropdown.Option("分供结算台账"),
                ft.dropdown.Option("异常数据"),
                ft.dropdown.Option("一体化合同台账"),
            ],
            border_radius=8,
            filled=True,
            hint_text="请选择数据库",
            text_style=ft.TextStyle(size=14, color=ft.colors.PRIMARY),
        )
        
        self.controls = [
            ft.Container(
                content=ft.Column(
                    controls=[
                        ft.Text("选择数据库", size=14, weight=ft.FontWeight.BOLD, color=ft.colors.PRIMARY),
                        self.commbox,
                    ],
                    spacing=5,
                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                ),
                padding=10,
                border_radius=12,
                bgcolor=ft.colors.SURFACE_VARIANT,
                shadow=ft.BoxShadow(
                    spread_radius=1,
                    blur_radius=3,
                    color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                    offset=ft.Offset(0, 1),
                ),
                width=240,
            ),
            ft.Row(
                controls=[
                    ft.ElevatedButton(
                        text="更新数据库结构",
                        icon=ft.icons.BUILD,
                        style=ft.ButtonStyle(
                            shape=ft.RoundedRectangleBorder(radius=10),
                            padding=15,
                            color=ft.colors.WHITE,
                            bgcolor=ft.colors.PRIMARY,
                        ),
                        on_click=lambda e: callF.thisProcess.run({"功能": "创建数据库"})
                    ),
                    ft.ElevatedButton(
                        text="查询数据库",
                        icon=ft.icons.SEARCH,
                        style=ft.ButtonStyle(
                            shape=ft.RoundedRectangleBorder(radius=10),
                            padding=15,
                            color=ft.colors.WHITE,
                            bgcolor=ft.colors.PRIMARY,
                        ),
                        on_click=lambda e: callF.thisProcess.run({"功能": "查询数据库", "参数": [self.commbox.value]})
                    ),
                    ft.ElevatedButton(
                        text="更新数据表",
                        icon=ft.icons.UPDATE,
                        style=ft.ButtonStyle(
                            shape=ft.RoundedRectangleBorder(radius=10),
                            padding=15,
                            color=ft.colors.WHITE,
                            bgcolor=ft.colors.PRIMARY,
                        ),
                        on_click=lambda e: callF.thisProcess.run({"功能": "更新数据库", "参数": [self.commbox.value]})
                    ),
                ],
                alignment=ft.MainAxisAlignment.SPACE_AROUND,
                vertical_alignment=ft.CrossAxisAlignment.CENTER,
            ),
        ]


class syncTabView(ft.Column):
    def __init__(self):
        super().__init__()
        self.visible = True
        self.expand = True
        self.spacing = 10

        self.navigation_bar = ft.NavigationBar(
            destinations=[
                ft.NavigationBarDestination(icon=ft.icons.DATA_EXPLORATION, label="数据导出"),
                ft.NavigationBarDestination(icon=ft.icons.REPLAY, label="数据库维护"),
            ],
            on_change=lambda e: self.changeMain(e),
            bgcolor=ft.colors.SURFACE_VARIANT,
            elevation=5,
        )
        self.default = [ft.Column(controls=[
            call_component.export(),
            call_component.export_balance(),
            call_component.button_sap(),
            call_component.export_fund(),
            call_component.button_fip_contract(),
            call_component.button_fip_other(),],expand=True,scroll=ft.ScrollMode.AUTO,width=730)
            
        ]
        self.mainRegional = ft.Column(
            controls=[], scroll=ft.ScrollMode.AUTO,
            alignment=ft.MainAxisAlignment.CENTER, 
            spacing=10, 
             
            expand=True
        )
        self.controls = [self.navigation_bar, self.mainRegional]
        self.mainRegional.controls = self.default

    def changeMain(self, e):
        index = e.control.selected_index
        if index == 0:
            self.mainRegional.controls = self.default
        elif index == 1:
            self.mainRegional.controls = [
                ft.Container(
                    content=ft.Column(
                        controls=[
                            ft.Text("数据库维护", size=18, weight=ft.FontWeight.BOLD, color=ft.colors.PRIMARY),
                            ft.Divider(height=1, color=ft.colors.with_opacity(0.2, ft.colors.PRIMARY)),
                            ft.Container(
                                content=changeData(),
                                padding=12,
                                border_radius=12,
                                bgcolor=ft.colors.SURFACE,
                                shadow=ft.BoxShadow(
                                    spread_radius=1,
                                    blur_radius=5,
                                    color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                                    offset=ft.Offset(0, 2),
                                ),
                            ),
                            ft.Container(
                                content=ft.Column(
                                    controls=[
                                        ft.Text("数据导入导出", size=16, weight=ft.FontWeight.BOLD, color=ft.colors.PRIMARY),
                                        ft.Divider(height=1, color=ft.colors.with_opacity(0.2, ft.colors.PRIMARY)),
                                        ft.Row(
                                            controls=[
                                                ft.ElevatedButton(
                                                    text="按excel模板导出明细数据",
                                                    icon=ft.icons.FILE_DOWNLOAD,
                                                    style=ft.ButtonStyle(
                                                        shape=ft.RoundedRectangleBorder(radius=8),
                                                        padding=10,
                                                        color=ft.colors.WHITE,
                                                        bgcolor=ft.colors.PRIMARY,
                                                    ),
                                                    on_click=lambda e: callF.thisProcess.run(
                                                        {"功能": "按excel模板导出明细数据"})
                                                ),
                                                ft.ElevatedButton(
                                                    text="手动选择明细数据导入",
                                                    icon=ft.icons.UPLOAD_FILE,
                                                    style=ft.ButtonStyle(
                                                        shape=ft.RoundedRectangleBorder(radius=8),
                                                        padding=10,
                                                        color=ft.colors.WHITE,
                                                        bgcolor=ft.colors.PRIMARY,
                                                    ),
                                                    on_click=lambda e: callF.thisProcess.run({"功能": "将数据写入数据库"})
                                                ),
                                            ],
                                            alignment=ft.MainAxisAlignment.SPACE_AROUND,
                                            vertical_alignment=ft.CrossAxisAlignment.CENTER,
                                        ),
                                    ],
                                    spacing=10,
                                ),
                                padding=12,
                                border_radius=12,
                            ),
                        ],
                        spacing=10,
                    ),
                    padding=15,
                    border_radius=12,
                ),
            ]
        self.mainRegional.update()