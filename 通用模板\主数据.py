import os
import sys
sys.path.append(".")
from playwright.sync_api import <PERSON><PERSON>, sync_playwright,Page
import src.utils.cscec as cscec
import src.utils.Excel.excel as excel
import time
def get_project(page:Page,projectCode:str): #主数据
    ws=excel.myBook().sheet("主数据清洗")
    for i in range(2,ws.MaxRow+1):
        get_project(ws.Cells(i,1).Value)
        ws.Cells(i,2).Value="完成"
        s="//div[text()='选择变更数据']/parent::div/parent::div"
        page.locator("//span[text()='选择变更数据']").click()
        page.locator(s+"//span[text()='施工项目']/parent::span/parent::span")
        page.locator(s+"//th[@title='项目编码']//input").fill(projectCode)
        page.locator(s+"//button/span[text()='查询']").click()
        page.locator(s+"//input[@type='checkbox']").click()
        page.locator(s+"//button/span[text()='确定']").click()
        s="//span[contains(text(),'项目主数据')]/parent::div/parent::div/parent::div"
        page.locator("//div[text()='变更类型']/parent::div").click()
        page.locator("//*[text()='公共信息']").click()
        page.locator(s+"//span/input[@type='checkbox']").click()
        page.locator("//span/div[@label='是否为工程项目']").click()
        page.locator("//li[@title='是']").click()
        page.locator(s+"//span[text()='保存']").click()
        time.sleep(5)
        page.locator(s+"//span[text()='提交']").click()
        page.locator("//span/textarea").fill("局安装南方何洲，变更项目是否为工程项目")
        page.locator("//button/span[text()='确定']").click()

def 主数据行政架构(page:Page,):
    a=page.locator("//table[contains(@class,'sunway-table-lazy-table')]//tr")
    maxN=a.count()
    print(maxN)
    if True:
        for i in range(1,maxN):
            page.locator(f"//table[contains(@class,'sunway-table-lazy-table')]//tr[{maxN-i}]/td[5]").click()
            page.locator("//span/div[@label='所属行政组织架构树']//div[@class='choose-input-icon']").click()
            s="//div[text()='机构主数据(JG)']/parent::div/parent::div"
            page.locator(s+"//th[@title='机构编码']//input").fill("2091057")
            page.locator(s+"//button/span[text()='查询']").click()
            page.locator(s+"//div[text()='中建三局安装工程有限公司南方大区直管项目']").click()
            page.locator(s+"//button/span[text()='确定']").click()
            
            s="//span[contains(text(),'项目主数据(XM)')]/parent::div/parent::div/parent::div"
            page.locator(s+"//span[text()='保存']").click()
            time.sleep(5)
            page.locator(s+"//span[text()='提交']").click()
            page.locator("//span/textarea").fill("局安装南方何洲，清洗项目主数据")
            page.locator("//button/span[text()='确定']").click()
def enterProject(page:Page,projectCode:str):
    page.locator("//span[@title='数据业务']").click()
    page.locator("//span[@title='主数据']").click()
    page.locator("//span[@title='项目主数据']").click()
    page.locator("//span[@title='项目主数据查询']").click()
    page.locator("//th[@title='项目编码']//input").fill(projectCode)
    page.locator("//button/span[text()='查询']").click()
    page.locator(f"//span[@title='{projectCode}']/parent::div/parent::td/preceding::td[4]//input[@type='checkbox']").click()
    page.locator("//button/span[text()='项目用户']").click()
    page.locator("//span[text()='用户申请']").click()

def chooseUser(page:Page,name:str,personOrg,nameCode:str=None):
    s="//span[text()='新增列表']/parent::div"
    page.locator(s+"//button/span[text()='新增']").click()
    s="//div[text()='选择用户']/parent::div/parent::div"
    if nameCode!=None:
        page.locator(s+"//th[@title='用户编码']//input").fill(nameCode)
    page.locator(s+"//th[@title='用户名称']//input").fill(name)
    page.locator(s+"//th[@title='所属单位名称']//input").fill(personOrg)
    page.locator(s+"//button[@title='刷新']").click()
    page.locator(f"//td[@title='{name}']/preceding::td[2]//input[@type='checkbox']").click()
    page.locator(s+"//span[text()='确定']").click()

def main():
    arr1=excel.myBook().sheet("人员清单").table("表1")
    arr2=excel.myBook().sheet("项目清单").table("表2")
    with sync_playwright() as playwright:
        browser = playwright.chromium.connect_over_cdp("http://localhost:9222")
        default_context = browser.contexts[0]
        page = cscec.switch_to_page(default_context,"主数据管理")
        for k in range(2,arr2.MaxRow+1):
            if arr2.getValue(k,"是否加人")=="是":
                page.reload()
                page.locator("//*[@id='弹窗关闭']").click()
                enterProject(arr2.getValue(k,"项目编码"))
                for i in range(2,arr1.MaxRow+1):
                    name=arr1.getValue(i,"姓名")
                    nameCode=arr1.getValue(i,"人员编码")
                    personOrg=arr1.getValue(i,"所属组织")
                    chooseUser(page,name,personOrg,nameCode)
                #提交部分可能有点问题
                page.locator("//span[text()='提交']").click()
                page.locator("//span/textarea").fill("变更项目人员")
                page.locator("//button/span[text()='确定']").click()





