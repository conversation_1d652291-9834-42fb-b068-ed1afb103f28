select 
any_value(利润中心描述) as 项目名称,
any_value(利润中心) as 利润中心,
any_value(供应商) as 供应商,
any_value(供应商描述) as 供应商描述,
any_value(总账科目长文本) AS 业务类型,
any_value(合同) as 合同编号,
any_value(合同文本描述) as 合同文本描述,
0-sum(case when (((借贷标识 = 'H' and 反记帐 = '') or (借贷标识 = 'S' and 反记帐 = 'X') ) and 总账科目长文本 like '应付账款%' and 总账科目长文本 not like '暂估' and 文本 not like '自动清账剩余项目')then 带符号的本位币金额 else null end) as 累计结算,
sum(case when (((借贷标识 = 'S' and 反记帐 = '') or (借贷标识 = 'H' and 反记帐 = 'X') ) and 总账科目长文本 like '应付账款%' and 总账科目长文本 not like '暂估' and 文本 not like '自动清账剩余项目' )then 带符号的本位币金额 else null end) as 累计付款
FROM 明细帐 WHERE 总账科目长文本 LIKE '应付账款%' AND 总账科目长文本 NOT LIKE '%供应链%' AND 总账科目长文本 NOT LIKE '%进项%'
GROUP by  利润中心,供应商,合同