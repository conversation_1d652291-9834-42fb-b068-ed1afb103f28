from bs4 import BeautifulSoup

# 读取 HTML 文件并解析
with open(r"C:\Users\<USER>\Desktop\123.htm", "r", encoding="utf-8") as f:
    soup = BeautifulSoup(f, "html.parser")

# 找到所有的表格
tables = soup.find_all("table")

# 假设我们要处理第一个表格
table = tables[0]

# 提取表格的每一行
table_data = []

# 获取所有行
rows = table.find_all("tr")

for row in rows:
    # 获取每一列的内容
    cols = row.find_all("td")
    cols = [col.text.strip() for col in cols]
    if cols:
        table_data.append(cols)

# 输出表格数据
for row in table_data:
    print(row)