import flet as ft
import os

class GridItem:
    def __init__(self, id):
        self.id = id
        self.name = None
        self.examples = []
        self.description = None
class ExampleItem:
    def __init__(self):
        self.name = None
        self.file_name = None
        self.order = None
        self.example = None
        # self.source_code = None

class ControlGroup:
    def __init__(self, name, label, icon, selected_icon, index):
        self.name = name
        self.label = label
        self.icon = icon
        self.selected_icon = selected_icon
        self.grid_items = []
        self.index = index


control_groups = [
            ControlGroup(
                name="统计大师",
                label="统计大师",
                icon=ft.Icons.BAR_CHART,
                selected_icon=ft.Icons.BAR_CHART_ROUNDED,
                index=0,
            ),
            ControlGroup(
                name="报表助手",
                label="报表助手",
                icon=ft.Icons.ANALYTICS,
                selected_icon=ft.Icons.ANALYTICS_OUTLINED,
                index=1,
            ),
            ControlGroup(
                name="智能稽核",
                label="智能稽核",
                icon=ft.Icons.SEARCH,
                selected_icon=ft.Icons.SEARCH,
                index=2,
            ),
            ControlGroup(
                name="自动制证",
                label="自动制证",
                icon=ft.Icons.GRID_VIEW,
                selected_icon=ft.Icons.GRID_VIEW_SHARP,
                index=3,
            ),
            ControlGroup(
                name="电子归档",
                label="电子归档",
                icon=ft.Icons.FOLDER,
                selected_icon=ft.Icons.FOLDER_OPEN,
                index=4,
            ),
            ControlGroup(
                name="一键结账",
                label="一键结账",
                icon=ft.Icons.CALCULATE,
                selected_icon=ft.Icons.CALCULATE_ROUNDED,
                index=5,
            ),
            ControlGroup(
                name="速填精灵",
                label="速填精灵",
                icon=ft.Icons.AUTO_FIX_HIGH,
                selected_icon=ft.Icons.AUTO_FIX_HIGH,
                index=6,
            ),
            ControlGroup(
                name="资金协管",
                label="资金协管",
                icon=ft.Icons.ACCOUNT_BALANCE,
                selected_icon=ft.Icons.ACCOUNT_BALANCE_WALLET,
                index=7,
            ),
            ControlGroup(
                name="智慧税务",
                label="智慧税务",
                icon=ft.Icons.RECEIPT_LONG,
                selected_icon=ft.Icons.RECEIPT_LONG_ROUNDED,
                index=8,
            ),
            ControlGroup(
                name="系统协同",
                label="系统协同",
                icon=ft.Icons.SYNC,
                selected_icon=ft.Icons.SYNC_ALT,
                index=9,
            ),
            ControlGroup(
                name="设置板块",
                label="设置板块",
                icon=ft.Icons.SETTINGS,
                selected_icon=ft.Icons.SETTINGS_ACCESSIBILITY,
                index=10,
            )
]



class PopupColorItem(ft.PopupMenuItem):
    def __init__(self, color, name):
        super().__init__()
        self.content = ft.Row(
            controls=[
                ft.Icon(name=ft.Icons.COLOR_LENS_OUTLINED, color=color),
                ft.Text(name),
            ],
        )
        self.on_click = self.seed_color_changed
        self.data = color

    def seed_color_changed(self, e):
        self.page.theme = self.page.dark_theme = ft.Theme(
            color_scheme_seed=self.data,
        )
        self.page.update()


class NavigationItem(ft.Container):
    def __init__(self, destination, item_clicked):
        super().__init__()
        self.ink = True
        self.padding = 10
        self.border_radius = 5
        self.destination = destination
        self.icon = destination.icon
        self.text = destination.label
        self.content = ft.Row([ft.Icon(self.icon), ft.Text(self.text)])
        self.on_click = item_clicked


class NavigationColumn(ft.Column):
    def __init__(self):
        super().__init__()
        self.expand = 4
        self.spacing = 0
        self.scroll = ft.ScrollMode.ALWAYS
        self.width = 150
        self.gallery = control_groups
        self.selected_index = 0
        self.controls = self.get_navigation_items()

    def before_update(self):
        super().before_update()
        self.update_selected_item()

    def get_navigation_items(self):
        navigation_items = []
        for destination in self.gallery:
            navigation_items.append(
                NavigationItem(destination, item_clicked=self.item_clicked)
            )
        return navigation_items

    def item_clicked(self, e):
        self.selected_index = e.control.destination.index
        print(self.selected_index)
        self.update_selected_item()
        self.page.go(f"/{e.control.destination.name}")

    def update_selected_item(self):
        # self.selected_index = self.gallery.control_groups.index(
        #     self.gallery.selected_control_group
        # )
        for item in self.controls:
            item.bgcolor = None
            item.content.controls[0].name = item.destination.icon
        self.controls[self.selected_index].bgcolor = ft.colors.SECONDARY_CONTAINER
        self.controls[self.selected_index].content.controls[0].name = self.controls[
            self.selected_index
        ].destination.selected_icon


class LeftNavigationMenu(ft.Column):
    def __init__(self):
        super().__init__()

        self.rail = NavigationColumn()

        self.dark_light_text = ft.Text("明亮主题")
        self.dark_light_icon = ft.IconButton(
            icon=ft.Icons.BRIGHTNESS_2_OUTLINED,
            tooltip="Toggle brightness",
            on_click=self.theme_changed,
        )

        self.explore_icon = ft.IconButton(
            icon=ft.Icons.WEB_ROUNDED,
            tooltip="打开数据展示器",
            url='http://127.0.0.1:8000'
            #on_click=lambda e: os.system(f'start chrome 127.0.0.1:8000'),  # 替换为您的实际URL,
        )

        self.controls = [
            self.rail,
            ft.Column(
                expand=1,
                controls=[
                    #ft.Row(controls=[self.dark_light_icon,self.dark_light_text]),
                    ft.Row(controls=[ft.PopupMenuButton(icon=ft.Icons.COLOR_LENS_OUTLINED, items=[PopupColorItem(color="deeppurple", name="深紫"),PopupColorItem(color="indigo", name="靛蓝"),PopupColorItem(color="blue", name="蓝色"),PopupColorItem(color="teal", name="青色"),PopupColorItem(color="green", name="绿色"),PopupColorItem(color="yellow", name="黄色"),PopupColorItem(color="orange", name="橘色"),PopupColorItem(color="deeporange", name="深橘"),PopupColorItem(color="pink", name="粉色"),],),ft.Text("选择颜色"),],),
                    ft.Row(controls=[self.explore_icon,ft.Text("打开网页")]),
                    ]
                    ),
                    ]
            
    def theme_changed(self, e):
        if self.page.theme_mode == ft.ThemeMode.LIGHT:
            self.page.theme_mode = ft.ThemeMode.DARK
            self.dark_light_text.value = "黑暗主题"
            self.dark_light_icon.icon = ft.Icons.BRIGHTNESS_HIGH
        else:
            self.page.theme_mode = ft.ThemeMode.LIGHT
            self.dark_light_text.value = "明亮主题"
            self.dark_light_icon.icon = ft.Icons.BRIGHTNESS_2
        self.page.update()
