
import os
import sys
sys.path.append(".")
from playwright.sync_api import <PERSON><PERSON>, sync_playwright,Page
import src.utils.cscec as cscec
import src.utils.Excel.excel as excel
with sync_playwright() as playwright:
    browser = playwright.chromium.connect_over_cdp("http://localhost:9222")
    default_context = browser.contexts[0]
    page = cscec.switch_to_page(default_context,"司库一体化")
    accountTable=cscec.cscecTable(page,"操作")
    accountTable.reIndex()
    ws=excel.myBook().sheet("代付账号")
    table=ws.table("表1")
    startNum=int(ws.Cells(1,2).Value)+1
    k=1
    for i in range(table.MaxRow-2):
        accountTable.appendRow()
        
    for i in range(startNum,table.MaxRow+1):
        accountTable.fillInput(k,"*收款账号",table.getValue(i,"账号"))
        accountTable.fillInput(k,"*收款方开户名称",table.getValue(i,"户名"))
        accountTable.fillInput(k,"*付款金额",str(table.getValue(i,"金额")))
        accountTable.clickInputQuery2(k,"*开户银行支行（联行号）")
        cscec.dialogInput(page,table.getValue(i,"联行号"))
        accountTable.fillInput(k,"*付款摘要",table.getValue(i,"摘要"))
        ws.Cells(1,2).Value=ws.Cells(1,2).Value+1
        k=k+1