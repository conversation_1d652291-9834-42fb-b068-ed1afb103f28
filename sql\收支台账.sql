select 
any_value(利润中心描述) as 利润中心名称,
any_value(利润中心) as 利润中心,
sum(case when (((借贷标识 = 'S' and 反记帐 = '') or (借贷标识 = 'H' and 反记帐 = 'X') ) and 总账科目长文本 like '应交税费\增值税\预交增值税\建筑服务%' )then 带符号的本位币金额 else null end) as 预缴税金,
sum(case when (((借贷标识 = 'S' and 反记帐 = '') or (借贷标识 = 'H' and 反记帐 = 'X') ) and 总账科目长文本 like '税金及附加\%' )then 带符号的本位币金额 else null end) as 本期税金,
sum(case when (((借贷标识 = 'S' and 反记帐 = '') or (借贷标识 = 'H' and 反记帐 = 'X') ) and 总账科目长文本 like '合同履约成本%职工薪酬%' )then 带符号的本位币金额 else null end) as 薪酬,
sum(case when (((借贷标识 = 'S' and 反记帐 = '') or (借贷标识 = 'H' and 反记帐 = 'X') ) and 总账科目长文本 like '合同履约成本%办公%' )then 带符号的本位币金额 else null end) as 办公,
sum(case when (((借贷标识 = 'S' and 反记帐 = '') or (借贷标识 = 'H' and 反记帐 = 'X') ) and 总账科目长文本 like '合同履约成本%差旅%' )then 带符号的本位币金额 else null end) as 差旅费,
sum(case when (((借贷标识 = 'S' and 反记帐 = '') or (借贷标识 = 'H' and 反记帐 = 'X') ) and 总账科目长文本 like '合同履约成本%业务招待%' )then 带符号的本位币金额 else null end) as 业务招待费
FROM 明细帐
where (过帐日期 >= '期初日期留 00:00:00') and (过帐日期<='期末日期留 00:00:00')
GROUP by  利润中心