import os
import sys

sys.path.append(".")
import src.utils.Excel.excel as excel

import os
from pathlib import Path
from python_calamine import CalamineWorkbook
lastPath="D:\数字公司总部\报表部分\国资委决算\中建三局安装工程有限公司-母公司 (国资委决算2023).xlsx"
FsPath="D:\数字公司总部\报表部分\国资委决算\中建三局安装工程有限公司-母公司0218.xlsx"

def get_title_col(inputList,title):
    for i in range(3):
        for j in range(len(inputList[i])):
            if inputList[i][j]==title:
                return j
def fillNow(FsPath):
    workbook = CalamineWorkbook.from_path(FsPath)
    theList = workbook.get_sheet_by_name("ZB2002 利润表").to_python()
    dictDifference={"其中：营业成本":"营业成本"} #差异字典
    d={} #取值字典
    for row in theList:
        key=row[0]

        if type(key)==str:
            d[key.strip()]=row[2]

    ws=excel.myBook().sheet()
    for i in range(2,ws.MaxRow+1):
        key=ws.Cells(i,1).Value
        if type(key)==str:
            key=key.strip()
            if key in d:
                ws.Cells(i,3).Value=d[key]
            else:
                if key in dictDifference:
                    ws.Cells(i,3).Value=d[dictDifference[key]]
                key=ws.Cells(i,1).Value
        key=ws.Cells(i,5).Value
        if type(key)==str:
            key=key.strip()
            if key in d:
                ws.Cells(i,7).Value=d[key]
            else:
                if key in dictDifference:
                    ws.Cells(i,7).Value=d[dictDifference[key]]

def fillLastTerm(lastPath,theInput,rowStart=2):
    tablename=theInput[0]
    d_table={"Z26 集团人力资源补充情况表":"Z25 人力资源补充情况表"
             ,"Z19 融资担保情况表":"Z18 融资担保情况表"
             ,"Z21 成本费用情况表":"Z20 成本费用情况表"
             ,"Z13 存货情况表":"Z12 存货情况表"
             ,"Z22 企业集团基本情况表":"Z21 企业集团基本情况表"
             ,"Z18 资金集中管理情况表":"Z17 资金集中管理情况表"}
    tablename2=d_table.get(tablename,tablename)
    workbook = CalamineWorkbook.from_path(lastPath)
    theList = workbook.get_sheet_by_name(tablename2).to_python()
    ws=excel.myBook().sheet(tablename)
    d={}
    for item in theInput[1]:
        for i in range(rowStart-1,len(theList)):
                if len(theList[i])>=item[2] and len(theList[i])>=item[1]:
                    key=theList[i][item[0]-1]
                    if type(key)==str :
                        key=key.strip()
                        d[key]=theList[i][item[2]-1]

    for tiem in theInput[1]:
        for i in range(rowStart,ws.MaxRow):
            key=ws.Cells(i,tiem[0]).Value
            if type(key)==str:
                key=key.strip()
                ws.Cells(i,tiem[1]).Value=d.get(key.strip(),"去年表未找到同名该项目")
    print("完成"+tablename+"期初数")

theInput1=["Z01 资产负债表",[(1,4,3),(5,8,7)]] #字典key列，期初数，期末数
fillLastTerm(lastPath,theInput1)
theInput2=["Z02 利润表",[(1,4,3),(5,8,7)]]
fillLastTerm(lastPath,theInput2)
theInput3=["Z03 现金流量表",[(1,4,3),(5,8,7)]]
fillLastTerm(lastPath,theInput3)
for i in range(3,17):
    theInput4=["Z04 所有者权益变动表",[(1,i+14,i)],6]
    fillLastTerm(lastPath,theInput4)

fillLastTerm(lastPath,theInput1)
fillLastTerm(lastPath,theInput2)


#Z05 国有资本权益变动情况表 样式不同跳过
theInput6=["Z06 资产减值准备情况表",[(1,3,13)],4]
fillLastTerm(lastPath,theInput6)

for i in range(5,7):
    theInput7=["Z07 应上交应弥补款项表",[(1,i,i-2)],3]
    fillLastTerm(lastPath,theInput7)

theInput8=["Z08 基本情况表",[(1,4,3),(5,8,7)]]
fillLastTerm(lastPath,theInput8)
theInput9=["Z09 人力资源情况表",[(1,4,3),(5,8,7)]]
fillLastTerm(lastPath,theInput9)
theInput10=["Z10 带息负债情况表",[(1,4,3),(5,8,7)],4]
fillLastTerm(lastPath,theInput10)

theInput11=["Z11 应收款项情况表",[(9,13,11),(14,18,16)],4] #右边部分
fillLastTerm(lastPath,theInput11)

theInput11=["Z11 应收款项情况表",[(1,7,3)],4] #左边部分1
fillLastTerm(lastPath,theInput11)
theInput11=["Z11 应收款项情况表",[(1,8,5)],4] #左边部分2
fillLastTerm(lastPath,theInput11)
if False: #上年不存在该表
    theInput12=["Z12 应付账款情况表",[(1,5,3)],5]
    fillLastTerm(lastPath,theInput12)

theInput13=["Z13 存货情况表",[(1,6,4)],4]
theInput13=["Z13 存货情况表",[(1,6,4)],4]
fillLastTerm(lastPath,theInput13)

#14,15,16结构不一致
#17无需上年数

theInput18=["Z18 资金集中管理情况表",[(1,5,3)]]
fillLastTerm(lastPath,theInput18)
theInput19=["Z19 融资担保情况表",[(1,4,3)],4]
fillLastTerm(lastPath,theInput18)
#20需要研讨
theInput21=["Z21 成本费用情况表",[(1,4,3),(5,8,7)]]
fillLastTerm(lastPath,theInput21)
theInput21=["Z21 成本费用情况表",[(9,12,11)]]
fillLastTerm(lastPath,theInput21)
theInput22=["Z22 企业集团基本情况表",[(1,4,3),(5,8,7)]]
fillLastTerm(lastPath,theInput22)

#23,24情况不一致
#25需要提取
theInput26=["Z26 集团人力资源补充情况表",[(1,4,3),(5,8,7)]]
fillLastTerm(lastPath,theInput26)
