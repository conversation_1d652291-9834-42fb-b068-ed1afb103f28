
import src.base.settings as settings
import src.utils.DB.midIntrSQLiteDB as midIntrSQLiteDB
from src.utils.DB.midIntrSQLiteDB import excelDB
import pandas as pd

def closeDataProcess(data:list):
    pass




def saveDataProcess(data):
    conn=midIntrSQLiteDB.webDB()
    conn.writeData("表格缓存",data)
    conn.close()

def deleteDataProcess(data):
    conn=midIntrSQLiteDB.webDB()
    conn.deleteData("表格缓存",data)
    conn.close()
def saveDataProcess2(data):
    conn=midIntrSQLiteDB.webDB()
    conn.updateLastData("表格缓存",data)
    conn.close()

def getDataProcess(request):
    conn=midIntrSQLiteDB.webDB()
    primaryID=request["periodId"]
    data=conn.queryData("表格缓存",primaryID)
    conn.close()
    return data

def queryDatalist():
    conn=midIntrSQLiteDB.webDB()
    data=conn.queryDatalist("表格缓存")
    conn.close()
    return data


def pushDataToColse(datas:dict):
    conn=excelDB()
    for key,value in datas.items():
        df=pd.DataFrame(value[1:],columns=value[0])
        df.to_sql(key,conn.conn,if_exists="replace",index=False)
    conn.close()
        