
from python_calamine import CalamineWorkbook
import src.base.cache as cache
import excelize
import re
import src.base.settings as settings

def completion_verification(check_path):
    f,error=excelize.open_file(check_path)
    fread=CalamineWorkbook.from_path(check_path).get_sheet_by_name("检查结果汇总").to_python()
    f.set_cell_value("检查结果汇总","a1",fread[0][4])
    f.save()
    f.close()

def read_formula(check_path):
    f,error=excelize.open_file(check_path)
    print(f.get_cell_formula("QY2008 外币结算金融资产及负债","b6"))
    print(f.get_cell_formula("QY2008 外币结算金融资产及负债","c59"))
    print(f.get_cell_formula("ZC2001 货币资金","d41"))
    f.close()

def replace_formula(check_path,sheet_name):
    f2,error=excelize.open_file(r"C:\Users\<USER>\Desktop\中建三局安装工程有限公司.xlsx")
    f3,error=excelize.open_file(r"C:\Users\<USER>\Desktop\中建三局安装工程有限公司.xlsx")
    f,error=excelize.open_file(check_path)
    fread=CalamineWorkbook.from_path(check_path).get_sheet_by_name(sheet_name).to_python()
    print(len(fread))
    for i in range(0,len(fread)):
        for j in range(0,len(fread[i])):
            formula,error=f.get_cell_formula(sheet_name,excelize.coordinates_to_cell_name(j+1,i+1)[0])
            if "[2]" in formula:
                pattern = r"'\[2\].*'![A-Za-z]{1,2}\d+"
                group_str = re.findall(pattern, formula)
                for group in group_str:
                    query_sheetname = re.search(r"\].*'", group).group().replace("[","").replace("]","").replace("'","")
                    query_cellname = re.search(r"[A-Za-z]{1,2}\d+$", group).group()
                    query_value = f2.get_cell_value(query_sheetname,query_cellname)[0]
                    if query_value.replace(",","").strip().isnumeric():
                        formula = formula.replace(group, query_value.replace(",","").strip())
                    elif query_value!='':
                        formula = formula.replace(group, '"'+query_value+'"')
                    else:
                        formula = formula.replace(group, '0')
                f.set_cell_formula(sheet_name,excelize.coordinates_to_cell_name(j+1,i+1)[0],formula)
                
            if "[1]" in formula:
                pattern = r"'\[2\].*'![A-Za-z]{1,2}\d+"
                group_str = re.findall(pattern, formula)
                for group in group_str:
                    query_sheetname = re.search(r"\].*'", group).group().replace("[","").replace("]","").replace("'","")
                    query_cellname = re.search(r"[A-Za-z]{1,2}\d+$", group).group()
                    query_value = f3.get_cell_value(query_sheetname,query_cellname)[0]
                    if query_value.replace(",","").strip().isnumeric():
                        formula = formula.replace(group, query_value.replace(",","").strip())
                    elif query_value!='':
                        formula = formula.replace(group, '"'+query_value+'"')
                    else:
                        formula = formula.replace(group, '0')
                f.set_cell_formula(sheet_name,excelize.coordinates_to_cell_name(j+1,i+1)[0],formula)
    f.save()
    f.close()

import importlib.util
import sys
import os

def execute_function_from_pyc(pyc_path, function_name, *args, **kwargs):
    """
    从 pyc 文件中导入模块并执行指定函数

    参数:
        pyc_path (str): pyc 文件的路径
        function_name (str): 要执行的函数名称
        *args: 传递给函数的位置参数
        **kwargs: 传递给函数的关键字参数

    返回:
        function: 执行函数的结果
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(pyc_path):
            raise FileNotFoundError(f"找不到文件: {pyc_path}")

        # 获取模块名称（不包含扩展名）
        module_name = os.path.splitext(os.path.basename(pyc_path))[0]

        # 创建模块规范
        spec = importlib.util.spec_from_file_location(module_name, pyc_path)
        if spec is None:
            raise ValueError(f"无法为 {pyc_path} 创建模块规范")

        # 创建模块
        module = importlib.util.module_from_spec(spec)
        sys.modules[module_name] = module

        # 执行模块（加载 pyc）
        spec.loader.exec_module(module)

        # 检查函数是否存在
        if not hasattr(module, function_name):
            raise AttributeError(f"模块 '{module_name}' 中没有名为 '{function_name}' 的函数")

        # 获取函数
        function = getattr(module, function_name)

        # 检查是否为可调用对象
        if not callable(function):
            raise TypeError(f"'{function_name}' 不是可调用对象")

        # 执行函数
        return function(*args, **kwargs)

    except Exception as e:
        print(f"执行过程中发生错误: {e}")
        raise
def main(inputFile,configWS,group):
    #print(cache.LastYearFinancialReport)
    #print(cache.CurrentYearFinancialReport)
    print("目前直接引用的安永版本")
    path=settings.CACHE_PATH+"/main.pyc"
    inputFile=inputFile
    configWS=configWS
    group=group
    result = execute_function_from_pyc(path, 'data_op', inputFile, configWS,group)
    #14 (('01-资产类', '02-负债类', '03-损益类', '04-税务类', '05-主表&权益类', '06-关联方&分部报告'))
