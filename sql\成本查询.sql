with 
a as (
SELECT any_value(凭证编号) as 凭证编号, 
       any_value(中台单据号) as 中台单据号, 
       any_value(财年) as 财年,
       any_value(利润中心) as 利润中心,
       any_value(利润中心描述) as 利润中心描述,
       any_value(文本) as 文本,
       any_value(过帐日期) as 过帐日期,
       any_value(输入日期) as 输入日期,
       any_value(总账科目长文本) as 成本科目,
       SUM(带符号的本位币金额) AS 入账成本
FROM 明细帐 where (总账科目长文本 LIKE '%合同履约成本%' 
or 总账科目长文本 LIKE '管理费用%'
or 总账科目长文本 LIKE '信用减值损失%'
or 总账科目长文本 LIKE '资产减值损失%'
or 总账科目长文本 LIKE '研发费用%'
or 总账科目长文本 LIKE '税金及附加%'
or 总账科目长文本 LIKE '资产处置损益%'
or 总账科目长文本 LIKE '财务费用%'
or 总账科目长文本 LIKE '营业外支出%') 
and 总账科目长文本 not like '%结转%'
GROUP BY 利润中心,凭证编号,财年,总账科目长文本),
b as (SELECT any_value(凭证编号) as 凭证编号, 
any_value(利润中心) as 利润中心, 
any_value(财年) as 财年, 
any_value(case when (供应商 != '') then 供应商 ELSE NULL END) as 供应商,
any_value(case when (供应商描述 != '') then 供应商描述 ELSE NULL END) as 供应商描述,
any_value(case when (总账科目长文本 LIKE '%暂估%') then '暂估成本' when (总账科目长文本 LIKE '%内部往来%' ) then '内部往来划转' ELSE NULL END) AS 科目分类
FROM 明细帐
GROUP BY 利润中心,凭证编号,财年
),
c as (
select  a.财年,a.过帐日期,a.输入日期,a.凭证编号,a.中台单据号,a.利润中心,a.利润中心描述, a.成本科目,b.供应商,b.供应商描述,a.文本,a.入账成本,b.科目分类
from a LEFT JOIN b ON a.利润中心 = b.利润中心 AND a.凭证编号 = b.凭证编号 AND a.财年 = b.财年 where a.入账成本 > 0.001 or a.入账成本 < -0.001)
--
INSERT OR REPLACE INTO 成本表 SELECT * EXCLUDE (总账科目长文本 , 科目方向 , 科目分类2) FROM c left join 科目对照 on c.成本科目 = 科目对照.总账科目长文本