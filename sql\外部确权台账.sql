
with 
a as (
SELECT any_value(凭证编号) as 凭证编号, any_value(财年) as 财年,any_value(利润中心) as 利润中心,
       0-SUM(case when (总账科目长文本 LIKE '%销项%') then 带符号的本位币金额 ELSE NULL END) AS 销项税
FROM 明细帐 GROUP BY 利润中心, 凭证编号, 财年),
--
b as (SELECT  any_value(凭证编号) as 凭证编号, any_value(利润中心) as 利润中心, any_value(财年) as 财年,
any_value(总账科目长文本) as 供应商类型,
any_value(合同) as 合同,
any_value(合同文本描述) as 合同文本描述,
any_value(输入日期) as 输入日期,
any_value(客户) as 客户,any_value(客户描述) as 客户描述,any_value(文本) as 文本,
any_value(利润中心描述) as 利润中心描述,any_value(过帐日期) as 过帐日期,any_value(中台单据号) as 中台单据号,
sum(带符号的本位币金额) as 含税结算金额
FROM 明细帐
WHERE (总账科目长文本 LIKE '应收账款%' or 总账科目长文本 LIKE '合同资产%质保金%') and 总账科目长文本 not like '%税%' and (文本!='自动清账剩余项目')
AND ((借贷标识 = 'S' AND 反记帐 = '') OR (借贷标识 = 'H' AND 反记帐 = 'X'))  GROUP BY 利润中心, 凭证编号, 财年,客户,合同
),
c as (
select  b.财年,过帐日期,输入日期,b.凭证编号,b.利润中心,利润中心描述,客户,客户描述,合同,合同文本描述,文本,中台单据号,含税结算金额,销项税
from b LEFT  JOIN a ON b.利润中心 = a.利润中心 AND b.凭证编号 = a.凭证编号 AND b.财年 = a.财年)
INSERT OR REPLACE INTO 外部确权台账 SELECT * FROM c