with 
a as (
SELECT any_value(凭证编号) as 凭证编号, 
       any_value(财年) as 财年,
       any_value(利润中心) as 利润中心,
       any_value(利润中心描述) as 利润中心描述,
       any_value(文本) as 文本,
       any_value(过帐日期) as 过帐日期,
       any_value(输入日期) as 输入日期,
       SUM(case when 总账科目长文本 like '专项储备\安全生产费\发生数' then 带符号的本位币金额 else null end) AS 安全生产费,
       any_value(case when 总账科目长文本 like '应付账款%劳务%' or 总账科目长文本 like '应付账款%分包%' then '分包结算安全费' else null end ) AS 类型
FROM 明细帐 
GROUP BY 利润中心,凭证编号,财年)
--
SELECT * FROM a where 安全生产费 > 0.001 or 安全生产费 < -0.001

SELECT any_value(中台单据号) AS 中台单据号,
any_value(自定义的凭证编号) AS 凭证流水号,
ANY_VALUE(过帐日期) AS 过账日期,
any_value(利润中心描述) AS 项目名称
FROM 明细帐 
WHERE 过帐日期 >='2024-07-01 00:00:00' AND 过帐日期<='2024-09-12 00:00:00'
GROUP BY 利润中心,凭证编号



SELECT * FROM 明细帐