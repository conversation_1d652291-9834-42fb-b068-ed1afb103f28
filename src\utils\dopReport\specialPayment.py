import src.utils.dop as dop
import src.utils.cscec as cscec
import src.utils.Browser.Browser as browser
import src.utils.Excel.openpyxlExcel as openpyxlExcel
from playwright.sync_api import Playwright, sync_playwright,Page
import src.base.settings as settings
import src.utils.Excel.excel as excel
from datetime import datetime
import time
from src.utils.DB.midIntrSQLiteDB import excelDB
import pandas as pd

def queryData():
    db=excelDB()
    headers = ['序号','是否填入', '项目编号', '项目名称','合同编号','金额','季度',"期间","资金主管","财务经理","上一级财务经理","上一级总会","是否历史项目","累计结算","累计应付","累计已付","累计欠付","比例","结果"]
    datarows=[1,'是',"123456789","测试项目","中建三局12345678",1000,"2025年2季度","2025年03月","杨主管","杨经理","周经理","周总会","否",1000,800,600,200,0.8]
    db.queryData("财商特殊支付",headers,datarows)
    db.close()

def writeData():
    db=excelDB()
    db.writeExcel("财商特殊支付")

def main():
    """
    批量提交特殊支付
    """
    db=excelDB()
    df=db.getDataframe("财商特殊支付")

    with sync_playwright() as playwright:
        browser = playwright.chromium.connect_over_cdp("http://localhost:9222")
        default_context = browser.contexts[0]
        page = cscec.switch_to_page(default_context,"财商")
        print(f"共有{df.index.size}条数据")
        for i in range(df.index.size):
            print(f"正在处理第{i+1}条数据")
            db.updateData("财商特殊支付",i+1,"是否填入","处理中")
            if df.loc[i,"是否填入"]=="是":
                frame1=page.frame_locator("//*[@id='portal-form-list-container']/div[4]/iframe")
                frame1.locator("//*[contains(text(),'财商申请期间')]/parent::div/following-sibling::div[1]//input").click()
                quarter=df.loc[i,"季度"]
                if isinstance(quarter,datetime):
                    quarter=str(quarter.year)+"年"+str(quarter.month)+"季度"
                frame1.locator(f"//span[@title='{quarter}']/preceding-sibling::span[1]").click()
                month=df.loc[i,"期间"]
                if isinstance(month,datetime):
                    month=month.strftime("%Y年%m月")
                cscec.getVisible(frame1,f"//span[@title='{month}']").click()
                frame1.locator("//div[contains(text(),'期间')]/parent::div/parent::div//span[contains(text(),'确 定')]").click()
                frame1.locator("//*[contains(text(),'分供方合同编码')]/parent::div/following-sibling::div[1]//input[@placeholder='请选择']").click()
                frame1.locator("//div[@id='contractCode']//div[@class='field__control']//div//div[@class='text']//div//input[@placeholder='请输入']").fill(df.loc[i,"合同编号"])
                frame1.locator("//div[@class='ant-modal-body']//button[2]").click()
                frame1.locator("//div[@class='sheet']//div[1]//div[2]//div[1]//div[1]//div[1]//div[1]//div[1][contains(text(),'"+df.loc[i,"合同编号"]+"')]").click()
                #frame1.get_by_text("中建三局020020230045060056").click() #注意编码
                frame1.locator("//div[@class='ant-modal-footer']//button[2]").dblclick()
                #合同选择
                # 
                frame1.locator("//*[contains(text(),'特殊支付种类')]/parent::div/following-sibling::div[1]").click() #注意默认为预付款
                frame1.get_by_text("其他",exact=True).click()  

                if df.loc[i,"是否历史项目"]=="是":
                    frame1.locator("//*[contains(text(),'累计月度结算:')]/parent::div/following-sibling::div[1]//input").fill(str(df.loc[i,"累计结算"]))
                    frame1.locator("//*[contains(text(),'累计应付款:')]/parent::div/following-sibling::div[1]//input").fill(str(df.loc[i,"累计应付"]))
                    frame1.locator("//*[contains(text(),'累计已付款:')]/parent::div/following-sibling::div[1]//input").fill(str(df.loc[i,"累计已付"]))
                    frame1.locator("//*[contains(text(),'至本月合同实际欠款:')]/parent::div/following-sibling::div[1]//input").fill(str(df.loc[i,"累计欠付"]))

                s="//*[contains(text(),'累计月度结算:')]/parent::div/following-sibling::div[1]//input"
                valueMoney=cscec.getVisible(frame1,s).input_value()
                tk=3
                try:
                    valueMoney=float(valueMoney)
                except:
                    valueMoney=0
                while valueMoney<=0 and tk>0 :
                    s="//*[contains(text(),'累计月度结算:')]/parent::div/following-sibling::div[1]//input"
                    valueMoney=cscec.getVisible(frame1,s).input_value()
                    try:
                        valueMoney=float(valueMoney)
                    except:
                        valueMoney=0
                    time.sleep(1)
                    tk=tk-1


            
                s="//*[contains(text(),'款项类型:')]/parent::div/following-sibling::div[1]//div[@title='进度款']"
                #cscec.getVisible(frame1,s).click()  已经默认是进度款不用选择
                #frame1.get_by_text("进度款",exact=True).click()  

                frame1.locator("//*[contains(text(),'本次支付')]/parent::div/following-sibling::div[1]//input").fill(str(df.loc[i,"金额"]))

                frame1.locator("//*[@title='资金主管']/parent::div/following-sibling::div[1]//span[contains(text(),'请选择')]").click()
                funderManager=df.loc[i,"资金主管"] if isinstance(df.loc[i,"资金主管"],str) else str(int(df.loc[i,"资金主管"]))
                frame1.locator("//input[@placeholder='搜索组织、姓名']").fill(funderManager)
                frame1.locator("//i[@aria-label='图标: search']").click()
                cscec.getVisible(frame1,"//span[contains(text(),'搜索结果:')]/parent::div/following-sibling::div/div").click()
                cscec.getVisible(frame1,"//span[contains(text(),'确 定')]").click()

                frame1.locator("//*[@title='财务经理']/parent::div/following-sibling::div[1]//span[contains(text(),'请选择')]").click()
                funacialManager=df.loc[i,"财务经理"] if isinstance(df.loc[i,"财务经理"],str) else str(int(df.loc[i,"财务经理"]))
                frame1.locator("//input[@placeholder='搜索组织、姓名']").nth(1).fill(funacialManager)
                frame1.locator("//i[@aria-label='图标: search']").nth(1).click()
                cscec.getVisible(frame1,"//span[contains(text(),'搜索结果:')]/parent::div/following-sibling::div/div").click()
                cscec.getVisible(frame1,"//span[contains(text(),'确 定')]").click()

                if df.loc[i,"上一级财务经理"]!='' and (not pd.isna(df.loc[i,"上一级财务经理"])) :
                    frame1.locator("//*[contains(@title,'上一级财务经理')]/parent::div/following-sibling::div[1]//span[contains(text(),'请选择')]").click()
                    upperFunderManager=df.loc[i,"上一级财务经理"] if isinstance(df.loc[i,"上一级财务经理"],str) else str(int(df.loc[i,"上一级财务经理"]))
                    frame1.locator("//input[@placeholder='搜索组织、姓名']").nth(2).fill(upperFunderManager)
                    frame1.locator("//i[@aria-label='图标: search']").nth(2).click()
                    cscec.getVisible(frame1,"//span[contains(text(),'搜索结果:')]/parent::div/following-sibling::div/div").click()
                    cscec.getVisible(frame1,"//span[contains(text(),'确 定')]").click()

                if df.loc[i,"上一级总会"]!='' and (not pd.isna(df.loc[i,"上一级总会"])) :
                    frame1.locator("//*[contains(@title,'上一级总会计师')]/parent::div/following-sibling::div[1]//span[contains(text(),'请选择')]").click()
                    upperMasterAccountant=df.loc[i,"上一级总会"] if isinstance(df.loc[i,"上一级总会"],str) else str(int(df.loc[i,"上一级总会"]))
                    frame1.locator("//input[@placeholder='搜索组织、姓名']").nth(3).fill(upperMasterAccountant)
                    frame1.locator("//i[@aria-label='图标: search']").nth(3).click()
                    cscec.getVisible(frame1,"//span[contains(text(),'搜索结果:')]/parent::div/following-sibling::div/div").click()
                    cscec.getVisible(frame1,"//span[contains(text(),'确 定')]").click()

                s="//*[contains(text(),'截至目前项目资金账户余额:')]/parent::div/following-sibling::div[1]//input"
                valueMoney=cscec.getVisible(frame1,s).input_value()
                if float(valueMoney)<0:
                    s="//*[contains(text(),'预计收款:')]/parent::div/following-sibling::div[1]//input"
                    valueMoney=cscec.getVisible(frame1,s).fill(str(-float(valueMoney)))

                frame1.locator("//span[text()='提 交']/parent::button").click()
                db.updateData("财商特殊支付",i+1,"是否填入","已处理")
        db.close()   

