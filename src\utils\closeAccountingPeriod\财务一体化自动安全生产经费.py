
from playwright.sync_api import Playwright, sync_playwright
import src.utils.Excel.excel as excel
import src.utils.Browser.Browser as browser
import src.utils.cscec as cscec
import math
from src.utils.DB.midIntrSQLiteDB import excelDB
import pandas as pd
def autofill():
    db=excelDB()
    df=db.getDataframe("独立结账模板安全费")
    print(f"共{df.index.size}行")
    page = browser.myBrowser("cscec").page
    for i,row in df.iterrows():
            d=row.to_dict()
            if d["是否"]=="是":
                print(f"开始执行{i+1}行")
                db.updateData("独立结账模板安全费","是否","开始执行",i+1)
                cscec.changeProjectCscec(page,d["组织机构"],d["项目名称"]) #切换项目
                page.locator("//*[@id='root']/section/section/aside/div/div/div[2]/div/div[1]/ul[3]/li/span").click()
                page.locator("//span[contains(text(),'应付及付款')]").click()
                page.locator("//span[contains(@class,'txt')][text()='安全生产经费计提']").click() 

                s="//label[contains(text(),'成本费用属性')]/parent::div/following-sibling::div[1]/div/div/div"
                page.locator(s).click()
                page.get_by_placeholder("请输入查询关键字").fill("工程施工成本")
                page.locator("//*[text()='帮助字典']/parent::div/parent::div/parent::div//span[contains(text(),'查询')]/parent::div/parent::div").click()
                page.locator("//*[text()='帮助字典']/parent::div/parent::div/parent::div//div[contains(text(),'工程施工成本')]").first.click()
                page.locator("//*[text()='帮助字典']/parent::div/parent::div/parent::div//div[contains(text(),'确定')]").click()

                s="//label[contains(text(),'项目计提比例')]/parent::div/following-sibling::div[1]/div/div/div"
                page.locator(s).click()
                provisionRate=d["专项储备计提比例"] #数值型
                if str(provisionRate).find(".5")<0:
                    page.get_by_text(f"{int(provisionRate)}%").click()
                else:
                    page.get_by_text(f"{math.floor(provisionRate*10)/10}%").click()
                page.locator(s).click()


                s="//label[contains(text(),'计提基数')]/parent::div/following-sibling::div[1]//input"
                page.locator(s).fill(str(d["专项储备基数"]))
                page.locator("//label[contains(text(),'计提基数')]").click()

                s="//label[contains(text(),'应计提比例')]/parent::div/following-sibling::div[1]//input"
                page.locator(s).fill(str(provisionRate))
                page.locator("//label[contains(text(),'应计提比例')]").click()
                page.get_by_placeholder("事由不允许超过").fill(d["事由"])
                s="//label[contains(text(),'前期专项储备余额')]/parent::div/following-sibling::div[1]//input"
                page.locator(s).fill("0") #步骤无实际意义，只因为事由虽然上去不自动保存
                
                s="//label[contains(text(),'含原始纸质附件：')]/parent::div/following-sibling::div[1]/div/div/div"
                page.locator(s).click()
                cscec.getVisible(page,"//div[text()='否']/following-sibling::div[text()='是']/preceding-sibling::div[1]").click()

                page.locator("//span[text()='保存']/parent::div/parent::div/parent::div/parent::div").click()
                page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='确定']").click()
                page.locator("//span[text()='保存']/parent::div/parent::div/parent::div/parent::div/parent::div//span[text()='提交']/parent::div/parent::div/parent::div/parent::div").click() #通过保存来确定提交按钮
                
                def fillapprover(rowint,approvalSelectorTable,personCode:str):
                    approvalSelectorTable.clickInputQuery(rowint, "人员编号")
                    cscec.fillLalbel_input(page, "人员编号或名称", personCode)
                    cscec.getVisible(page, "//div[text()='查询']").click()
                    cscec.locatorDigalog(page, "审核人").locator(f"//div[text()='{personCode}']").dblclick()
                if d['安全费计提审批人1']!="" and d['安全费计提审批人1'] is not None and (not pd.isna(d['安全费计提审批人1'])):
                    approvalSelectorTable = cscec.cscecTable(page, "流程节点")
                    fillapprover(1,approvalSelectorTable,d['安全费计提审批人1']) 
                    if d['安全费计提审批人2']!="" and d['安全费计提审批人2'] is not None and (not pd.isna(d['安全费计提审批人2'])):
                        fillapprover(2,approvalSelectorTable,d['安全费计提审批人2']) 
                    if d['安全费计提审批人3']!="" and d['安全费计提审批人3'] is not None and (not pd.isna(d['安全费计提审批人3'])):
                        fillapprover(3,approvalSelectorTable,d['安全费计提审批人3']) 
                    cscec.clickDigalog(page,"设置节点审批人","确定")
                cscec.clickDigalog(page,"意见","确定")
                cscec.clickDigalog(page,"提示","确定")
                page.locator("//button[@aria-label='remove']//span[@aria-label='close']//*[name()='svg']//*[name()='path' and contains(@d,'M563.8 512')]").click() #关闭结算单
                db.updateData("独立结账模板安全费","是否","执行完毕",i+1)
    print("完成任务")


