
with a as (
select 
(case when 总账科目长文本 like '应收%' or 总账科目长文本 like '应付%' then '应收应付' else (case when (总账科目长文本 like '其他应付%' or 总账科目长文本 like '其他应收%') and 总账科目长文本 not like '%内部往来%' and 总账科目长文本 not like '%可用存款%' then '其他应收应付' else 总账科目长文本 end) end) as 总账科目长文本,
df.利润中心 as 利润中心,利润中心名称,df.客户,客户名称,期末余额,dfBasedata.利润中心 as 利润中心2 
from df Left join dfBasedata on df.客户 = dfBasedata.项目所属的核算组织
where (总账科目长文本 like '%内部往来%' or 总账科目长文本 like '%应收%' or 总账科目长文本 like '%应付%' or 总账科目长文本 like '%可用存款%') and 利润中心2 != ''),
--
b as (
select any_value(利润中心名称) as 利润中心名称,any_value(总账科目长文本) as 总账科目长文本 ,any_value(利润中心) as 利润中心,any_value(利润中心2) as 利润中心2,any_value(客户) as 客户,any_value(客户名称) as 客户名称,sum(期末余额) as 期末余额 from a
GROUP by 总账科目长文本,利润中心,利润中心2,客户
),
--
c as (
    select * from b
),
--
d as (
select b.总账科目长文本,b.利润中心,b.利润中心名称,b.客户,b.客户名称,b.期末余额,b.利润中心2,c.利润中心名称 as 对方,c.期末余额 as 对方期末余额,(b.期末余额 + c.期末余额) as 差额 from b left join c on b.利润中心 = c.利润中心2 and b.总账科目长文本 =  c.总账科目长文本 and b.利润中心2 = c.利润中心
)
--
select d.*,dfBasedata.利润中心组描述 from d left join dfBasedata on d.利润中心 = dfBasedata.利润中心 where 差额 > 0.001 or  差额 < -0.001 or (对方期末余额 is null and (期末余额>0.001 or 期末余额<-0.001))