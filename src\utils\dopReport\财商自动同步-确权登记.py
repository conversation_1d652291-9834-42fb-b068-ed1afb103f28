import os
import sys


from playwright.sync_api import Playwright, sync_playwright
import win32com.client
import time
import pythoncom

def autofill():
    try:
        et = win32com.client.Dispatch("Excel.Application")
        wb=et.ActiveWorkbook
        ws=wb.Worksheets("确权收款台账")
    except:
        et = win32com.client.Dispatch("Ket.Application")
        wb=et.ActiveWorkbook
        ws=wb.Worksheets("确权收款台账")
    table=excelto.table(wb,"确权收款台账","表确权收款")
    start_number=int(ws.Cells(1,2).Value)
    with sync_playwright() as playwright:   
        browser = playwright.chromium.connect_over_cdp("http://localhost:9222")
        default_context = browser.contexts[0]
        page = switch_to_page(default_context, title="中建三局", url=None)      
        for i in range(start_number+1,table.max_row+1):
                print("开始第"+str(i-1)+"行")
                if round(table.value(i,"确权差额"),2)!=0 and table.value(i,"商务无法取数")=="是":
                    page.click("//button[@class='ant-btn ant-btn-primary list-action-add']") #点击新增表
                    frame=page.frame_locator("//*[@id='portal-form-list-container']/div[4]/iframe") #选择项目
                    frame.locator("//div[@id='project']//div[@class='field__control']//div//div[@class='relevanceform']//div//input[@placeholder='请选择']").click()
                    frame.locator("//div[@class='ant-col ant-col-12']//div[@id='projectName']//div[@class='field__control']//div//div[@class='text']//div//input[@placeholder='请输入']").click()
                    frame.locator("//div[@class='ant-col ant-col-12']//div[@id='projectName']//div[@class='field__control']//div//div[@class='text']//div//input[@placeholder='请输入']").fill(table.value(i,"财商名称"))
                    frame.locator("//div[@class='ant-modal-body']//button[2]").click()

                    frame.get_by_text(table.value(i,"编码")).click() #注意编码
                    frame.locator("//div[@class='ant-modal-footer']//button[2]").click()
                    time.sleep(3)          
                    frame.locator("//div[@id='submissionTime']//div[@class='field__control']//div//div[@class='date']//div//span[@class='ant-calendar-picker']//div//i[@aria-label='图标: calendar']//*[name()='svg']").click()
                    if table.value(i,"确权月份差额")!=0:
                        for j in range(1,int(table.value(i,"确权月份差额"))+1):
                            frame.locator("//a[@title='上个月 (翻页上键)']").click()
                        frame.locator("//td[@title='"+table.value(i,"确权标准日期")+"']").click()
                    else:
                        frame.locator("//td[@title='"+table.value(i,"确权标准日期")+"']").click()

                    frame.locator("//*[@id='selfPartEx']/div[2]/div/div/div/input").click()
                    frame.locator("//div[@class='h3-input-number h3-input-number-focused']//input[@placeholder='请输入']").fill(str(table.value(i,"确权差额"))) #总收款
                    frame.locator("//span[text()='保 存']/parent::button").click()
                ws.Cells(1,2).Value=ws.Cells(1,2).Value+1
        print("完成任务")

