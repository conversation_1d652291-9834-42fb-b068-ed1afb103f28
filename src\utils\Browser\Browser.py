


from playwright.sync_api import <PERSON><PERSON>, sync_playwright
import os
import json
import src.base.settings as settings
import time
thePath=settings.PATH_INTERNAL
def readTxtFiles(filename):
    current_directory = thePath+"/Browser"
    for filename in os.listdir(current_directory):
        if filename.endswith(".txt"):
            file_path = os.path.join(current_directory, filename)
            with open(file_path, "r+") as file:
                content = file.read()
                #file.seek(0)
                #file.truncate()
                #print("Original content:", content)
                return content
def writeTxtFiles(filename,content):
    current_directory = thePath+"/Browser"
    for filename in os.listdir(current_directory):
        if filename.endswith(".txt"):
            file_path = os.path.join(current_directory, filename)
            with open(file_path, "r+") as file:
                file.seek(0)
                file.truncate()
                file.write(content)

def clearCookies():
    current_filepath = thePath+"/Browser"+"/cookies.txt"
    with open(current_filepath, 'w', encoding='utf-8') as file:
        file.write("[]")
    
    current_filepath = thePath+"/Browser"+"/cookiesshangwu.txt"
    with open(current_filepath, 'w', encoding='utf-8') as file:
        file.write("[]")

class myBrowser():
    def __init__(self,url,id=None,password=None):
        self.url=url
        self.id=id
        self.password=password
        self.Context=None
        self._page_=None

    @property
    def page(self):
        playwright = sync_playwright().start()
        newContext = playwright.chromium.launch_persistent_context(channel="chrome",user_data_dir="C:/user_data",headless=False,no_viewport=True)
        self.Context =newContext 
        if self.url.find("cscec")!=-1:
            cookies= json.loads(readTxtFiles("cookies.txt"))
            newContext.add_cookies(cookies)
            page=newContext.new_page()
            page.goto(f"https://iam.cscec.com/cas/login?service=https%3A%2F%2Ffip.cscec.com%2FOSPPortal%2Fcallback")
            
            try:
                page.locator("//label[text()='我的单据']").click(timeout=15000)
            except:
                try:
                    page.get_by_label("通知公告").get_by_label("Close", exact=True).click()
                except:
                    pass
            #查看是否有通知
            page.locator("//label[text()='我的单据']").click(timeout=360000) #通过六分钟等待
            cookies = newContext.cookies()
            my_string=json.dumps(cookies)
            writeTxtFiles("cookies.txt",my_string)
        elif self.url=="3b":
            page=newContext.new_page()
            page.goto("http://dmp.cscec3b.com.cn/login")
            page.locator("//input[@placeholder='请输入账号']").fill(self.id)
            page.locator("//input[@placeholder='请输入密码']").fill(self.password)
            page.locator("//div[@class='login-btn']").click()
        elif self.url=="shangwu":
            cookies= json.loads(readTxtFiles("cookiesshangwu.txt"))
            newContext.add_cookies(cookies)
            page=newContext.new_page()
            page.goto(f"https://dop-auth.cscec3b-iti.com/portal/#/")
            with newContext.expect_page(timeout=360000) as new_page_info:
                page.locator("//span[contains(text(),'商务管理')]/parent::div/preceding-sibling::div[1]").click(timeout=360000)
            page = new_page_info.value #否则自动关闭了
            cookies = newContext.cookies()
            my_string=json.dumps(cookies)
            writeTxtFiles("cookiesshangwu.txt",my_string)
        self._page_=page
        return page
    def goCscec3bPlan(self):
        self._page_.locator("//span[@title='资金管理']").click()
        self._page_.locator("//td[normalize-space()='1']").click()
        self._page_.locator("//span[@title='资金收支管理']").click()
        self._page_.locator("//a[@title='资金收支计划']/parent::li").click()
        self._page_.locator("//li[text()='资金计划录入']/following-sibling::li[1]").click()
    
    def cscecLablechoose(self,s1,s2,s3,s4):
        s=f"//label[contains(text(),'{s1}')]/parent::div/following-sibling::div[1]/div/div/div"
        self._page_.locator(s).click()
        self._page_.get_by_placeholder(s2).fill(s4)
        self._page_.locator(f"//*[text()='{s3}']/parent::div/parent::div/parent::div//span[contains(text(),'查询')]/parent::div/parent::div").click()
        self._page_.get_by_text(s4,exact=True).first.dblclick() 
    
    def goCscec3bReceivableLedger(self):
        self._page_.locator("//span[@title='资金管理']").click()
        self._page_.locator("//td[normalize-space()='1']").click()
        self._page_.locator("//span[@title='资金收支管理']").click()
        self._page_.locator("//a[@title='债权管理台账']/parent::li").click()
        self._page_.locator("//li[text()='债权管理台账']/following-sibling::li[1]").click()    
        self._page_.locator("//div[text()='当前项目：']/following-sibling::div[1]/i").click() 

    def goCscec3bReceive(self):
        self._page_.locator("//span[@title='资金管理']").click()
        self._page_.locator("//td[normalize-space()='1']").click()
        self._page_.locator("//span[@title='资金收支管理']").click()
        self._page_.locator("//a[@title='债权管理台账']/parent::li").click()
        self._page_.locator("//li[text()='收款登记']/following-sibling::li[1]").click()    
        self._page_.locator("//div[text()='当前项目：']/following-sibling::div[1]/i").click() 
        time.sleep(2)

