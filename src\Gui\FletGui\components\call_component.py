
import flet as ft
import datetime
from src.Gui.FletGui.components.Container import LabeledContainer
import src.Gui.callProcess as callF
import src.base.cache as cache
from datetime import datetime


class export(ft.Column):
    def __init__(self):
        super().__init__()
        self.visible = True
        self.expand = True
        self.spacing = 5

        # 创建日期选择器
        self.startDate = ft.DatePicker(
            first_date=datetime(2015, 1, 1),
            last_date=datetime(2030, 12, 31),
            on_change=self.start_date_changed
        )
        self.endDate = ft.DatePicker(
            first_date=datetime(2018, 1, 1),
            last_date=datetime(2030, 12, 31),
            on_change=self.end_date_changed
        )

        # 创建日期显示按钮
        date_button_style = ft.ButtonStyle(
            shape=ft.RoundedRectangleBorder(radius=8),
            padding=10,
            color=ft.colors.ON_SURFACE,
            bgcolor=ft.colors.SURFACE,
            elevation=2,
            overlay_color=ft.colors.BLUE_50,
            shadow_color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
        )
        
        self.startDateButton = ft.ElevatedButton(
            text=cache.Lastdate,
            icon=ft.icons.CALENDAR_TODAY,
            style=date_button_style,
            on_click=lambda _: self.startDate.pick_date(),
            width=140,
            height=45
        )

        self.endDateButton = ft.ElevatedButton(
            text=cache.Nowdate,
            icon=ft.icons.CALENDAR_TODAY,
            style=date_button_style,
            on_click=lambda _: self.endDate.pick_date(),
            width=140,
            height=45
        )

        # 创建日期选择器容器
        self.startDateContainer = ft.Container(
            content=ft.Row(
                controls=[
                    ft.Text("开始日期", size=10, weight=ft.FontWeight.BOLD, color=ft.colors.PRIMARY),
                    self.startDateButton
                ],
                spacing=10,
                alignment=ft.MainAxisAlignment.START,
            ),
            padding=10,
            border_radius=12,
            bgcolor=ft.colors.SURFACE_VARIANT,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=3,
                color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                offset=ft.Offset(0, 1),
            ),
            width=200,
        )

        self.endDateContainer = ft.Container(
            content=ft.Row(
                controls=[
                    ft.Text("结束日期", size=10, weight=ft.FontWeight.BOLD, color=ft.colors.PRIMARY),
                    self.endDateButton
                ],
                spacing=10,
                alignment=ft.MainAxisAlignment.START,
            ),
            padding=10,
            border_radius=12,
            bgcolor=ft.colors.SURFACE_VARIANT,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=3,
                color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                offset=ft.Offset(0, 1),
            ),
            width=200,
        )

        # 创建导出按钮
        primary_button_style = ft.ButtonStyle(
            shape=ft.RoundedRectangleBorder(radius=8),
            padding=ft.padding.symmetric(horizontal=20, vertical=12),
            color=ft.colors.WHITE,
            bgcolor=ft.colors.BLUE_600,
            overlay_color=ft.colors.BLUE_700,
            elevation=2,
            shadow_color=ft.colors.with_opacity(0.2, ft.colors.BLUE_900),
            animation_duration=200,
        )
        
        self.exportButton = ft.ElevatedButton(
            text="执行导出",
            icon=ft.icons.DOWNLOAD,
            style=primary_button_style,
            on_click=lambda e: callF.thisProcess.run(
                {"功能": "sap明细数据导出", "参数": [self.startDateButton.text, self.endDateButton.text]}
            ),
            width=150,
            height=45
        )

        self.controls = [LabeledContainer(
        label_text="导出同步会计凭证明细",
        content=ft.Column(
                    controls=[
                        ft.Row(
                            [self.startDateContainer, self.endDateContainer, self.exportButton],
                            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                            vertical_alignment=ft.CrossAxisAlignment.CENTER,
                            spacing=5,
                        ),
                    ],
                    spacing=10,
                ),bgcolor=ft.colors.WHITE,
        border_color=ft.colors.BLUE_400,
        width=730,
            ),
            self.startDate,
            self.endDate
        ]

    def start_date_changed(self, e):
        if e.data:
            self.startDateButton.text = e.data[:10]
            self.startDateButton.update()

    def end_date_changed(self, e):
        if e.data:
            self.endDateButton.text = e.data[:10]
            self.endDateButton.update()

class export_balance(ft.Column):
    def __init__(self):
        super().__init__()
        self.visible = True
        self.expand = True
        self.spacing = 5

        self.period_dropdown = ft.Dropdown(
            width=200,
            options=[
                ft.dropdown.Option("1", "往年科目余额表"),
                ft.dropdown.Option("2", "本年科目余额表"),
            ],
            value="2",  # 默认选择第一期
            border_radius=8,
            filled=True,
            text_style=ft.TextStyle(size=14, color=ft.colors.BLUE_ACCENT),
        )

        # 创建日期选择器
        self.startDate = ft.DatePicker(
            first_date=datetime(2015, 1, 1),
            last_date=datetime(2030, 12, 31),
            on_change=self.start_date_changed
        )
        self.endDate = ft.DatePicker(
            first_date=datetime(2018, 1, 1),
            last_date=datetime(2030, 12, 31),
            on_change=self.end_date_changed
        )

        # 创建日期显示按钮
        current_year = datetime.now().year
        date_button_style = ft.ButtonStyle(
            shape=ft.RoundedRectangleBorder(radius=8),
            padding=10,
            color=ft.colors.ON_SURFACE,
            bgcolor=ft.colors.SURFACE,
            elevation=2,
            overlay_color=ft.colors.BLUE_50,
            shadow_color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
        )
        
        self.startDateButton = ft.ElevatedButton(
            text=f"{current_year}-01-01",
            icon=ft.icons.CALENDAR_TODAY,
            style=date_button_style,
            on_click=lambda _: self.startDate.pick_date(),
            width=140,
            height=45
        )

        self.endDateButton = ft.ElevatedButton(
            text=datetime.now().strftime("%Y-%m-%d"),
            icon=ft.icons.CALENDAR_TODAY,
            style=date_button_style,
            on_click=lambda _: self.endDate.pick_date(),
            width=140,
            height=45
        )

        # 创建日期选择器容器
        self.startDateContainer = ft.Container(
            content=ft.Row(
                controls=[
                    ft.Text("开始日期", size=10, weight=ft.FontWeight.BOLD, color=ft.colors.PRIMARY),
                    self.startDateButton
                ],
                spacing=10,
                alignment=ft.MainAxisAlignment.START,
            ),
            padding=10,
            border_radius=12,
            bgcolor=ft.colors.SURFACE_VARIANT,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=3,
                color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                offset=ft.Offset(0, 1),
            ),
            width=200,
        )

        self.endDateContainer = ft.Container(
            content=ft.Row(
                controls=[
                    ft.Text("结束日期", size=10, weight=ft.FontWeight.BOLD, color=ft.colors.PRIMARY),
                    self.endDateButton
                ],
                spacing=10,
                alignment=ft.MainAxisAlignment.START,
            ),
            padding=10,
            border_radius=12,
            bgcolor=ft.colors.SURFACE_VARIANT,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=3,
                color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                offset=ft.Offset(0, 1),
            ),
            width=200,
        )

        # 创建导出按钮
        primary_button_style = ft.ButtonStyle(
            shape=ft.RoundedRectangleBorder(radius=8),
            padding=ft.padding.symmetric(horizontal=20, vertical=12),
            color=ft.colors.WHITE,
            bgcolor=ft.colors.BLUE_600,
            overlay_color=ft.colors.BLUE_700,
            elevation=2,
            shadow_color=ft.colors.with_opacity(0.2, ft.colors.BLUE_900),
            animation_duration=200,
        )
        
        self.exportButton = ft.ElevatedButton(
            text="执行导出",
            icon=ft.icons.DOWNLOAD,
            style=primary_button_style,
            on_click=lambda e: self.export_balance_table(e),
            width=150,
            height=45
        )

        self.controls = [LabeledContainer(
        label_text="导出科目余额表",
        content=ft.Column(
                    controls=[
                        ft.Row(
                            [ft.Text("请选择导出位置:", size=14, weight=ft.FontWeight.BOLD), self.period_dropdown],
                            vertical_alignment=ft.CrossAxisAlignment.CENTER,
                            spacing=50,
                        ),
                        ft.Row(
                            [self.startDateContainer, self.endDateContainer, self.exportButton],
                            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                            vertical_alignment=ft.CrossAxisAlignment.CENTER,
                            spacing=5,
                        ),
                    ],
                    spacing=10,
                ),bgcolor=ft.colors.BLUE_50,
        border_color=ft.colors.GREEN,
        width=730,
            ),
            self.startDate,
            self.endDate
        ]

    def start_date_changed(self, e):
        if e.data:
            self.startDateButton.text = e.data[:10]
            self.startDateButton.update()

    def end_date_changed(self, e):
        if e.data:
            self.endDateButton.text = e.data[:10]
            self.endDateButton.update()

    
    def export_balance_table(self, e):
        period = self.period_dropdown.value
        callF.thisProcess.run({"功能": "科目余额表导出SAP", "参数": [self.startDateButton.text, self.endDateButton.text,period]})


class export_fund(ft.Column):
    def __init__(self):
        super().__init__()
        self.visible = True
        self.expand = True
        self.spacing = 5

        # 创建日期选择器
        self.startDate = ft.DatePicker(
            first_date=datetime(2015, 1, 1),
            last_date=datetime(2030, 12, 31),
            on_change=self.start_date_changed
        )
        self.endDate = ft.DatePicker(
            first_date=datetime(2018, 1, 1),
            last_date=datetime(2030, 12, 31),
            on_change=self.end_date_changed
        )

        # 创建日期显示按钮
        current_year = datetime.now().year
        current_month = datetime.now().month
        # 日期按钮样式
        date_button_style = ft.ButtonStyle(
            shape=ft.RoundedRectangleBorder(radius=8),
            padding=10,
            color=ft.colors.ON_SURFACE,
            bgcolor=ft.colors.SURFACE,
            elevation=2,
            overlay_color=ft.colors.BLUE_50,
            shadow_color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
        )
        
        self.startDateButton = ft.ElevatedButton(
            text=f"{current_year}-{current_month}-01",
            icon=ft.icons.CALENDAR_TODAY,
            style=date_button_style,
            on_click=lambda _: self.startDate.pick_date(),
            width=140,
            height=45
        )

        self.endDateButton = ft.ElevatedButton(
            text=datetime.now().strftime("%Y-%m-%d"),
            icon=ft.icons.CALENDAR_TODAY,
            style=date_button_style,
            on_click=lambda _: self.endDate.pick_date(),
            width=140,
            height=45
        )

        # 创建日期选择器容器
        self.startDateContainer = ft.Container(
            content=ft.Row(
                controls=[
                    ft.Text("开始日期", size=10, weight=ft.FontWeight.BOLD, color=ft.colors.PRIMARY),
                    self.startDateButton
                ],
                spacing=10,
                alignment=ft.MainAxisAlignment.START,
            ),
            padding=10,
            border_radius=12,
            bgcolor=ft.colors.SURFACE_VARIANT,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=3,
                color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                offset=ft.Offset(0, 1),
            ),
            width=200,
        )

        self.endDateContainer = ft.Container(
            content=ft.Row(
                controls=[
                    ft.Text("结束日期", size=10, weight=ft.FontWeight.BOLD, color=ft.colors.PRIMARY),
                    self.endDateButton
                ],
                spacing=10,
                alignment=ft.MainAxisAlignment.START,
            ),
            padding=10,
            border_radius=12,
            bgcolor=ft.colors.SURFACE_VARIANT,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=3,
                color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                offset=ft.Offset(0, 1),
            ),
            width=200,
        )

        # 创建导出按钮
        self.exportButton = ft.ElevatedButton(
            text="执行导出",
            icon=ft.icons.DOWNLOAD,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=8),
                padding=10,
                color=ft.colors.WHITE,
                bgcolor=ft.colors.PRIMARY,
            ),
            on_click=lambda e: callF.thisProcess.run(
                {"功能": "自动查询资金余额", "参数": [self.startDateButton.text, self.endDateButton.text]})
        )

        self.controls = [LabeledContainer(
        label_text="导出一体化存量",
        content=ft.Column(
                    controls=[
                        ft.Row(
                            [self.startDateContainer, self.endDateContainer, self.exportButton],
                            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                            vertical_alignment=ft.CrossAxisAlignment.CENTER,
                            spacing=5,
                        ),
                    ],
                    spacing=10,
                ),bgcolor=ft.colors.WHITE,
        border_color=ft.colors.YELLOW_900,
        width=730,
            ),
            self.startDate,
            self.endDate
        ]

    def start_date_changed(self, e):
        if e.data:
            self.startDateButton.text = e.data[:10]
            self.startDateButton.update()

    def end_date_changed(self, e):
        if e.data:
            self.endDateButton.text = e.data[:10]
            self.endDateButton.update()


class button_sap(ft.Column):
    def __init__(self):
        super().__init__()
        self.visible = True
        self.expand = True
        self.spacing = 5
        # 创建SAP按钮样式
        sap_button_style = ft.ButtonStyle(
            shape=ft.RoundedRectangleBorder(radius=8),
            padding=ft.padding.symmetric(horizontal=16, vertical=12),
            color=ft.colors.WHITE,
            bgcolor=ft.colors.INDIGO_600,
            overlay_color=ft.colors.INDIGO_700,
            elevation=2,
            shadow_color=ft.colors.with_opacity(0.2, ft.colors.INDIGO_900),
            animation_duration=200,
        )
        
        self.exportButton1 = ft.ElevatedButton(
            text="导出主数据",
            icon=ft.icons.DATA_ARRAY,
            style=sap_button_style,
            on_click=lambda e: callF.thisProcess.run(
                {"功能": "导出主数据"}
            ),
            width=140,
            height=45
        )

        self.exportButton2 = ft.ElevatedButton(
            text="导出内部对账",
            icon=ft.icons.COMPARE_ARROWS,
            style=sap_button_style,
            on_click=lambda e: callF.thisProcess.run(
                {"功能": "导出内部对账"}
            ),
            width=140,
            height=45
        )
        self.controls = [LabeledContainer(
        label_text="导出sap其他",
        content=ft.Column(
                    controls=[
                        ft.Row(
                            [
                                ft.Container(
                                    content=ft.Text("此处为导出需要的SAP其他数据",
                                                 size=14,
                                                 weight=ft.FontWeight.W_500,
                                                 color=ft.colors.BLUE_GREY_800),
                                    padding=10,
                                    expand=True
                                ),
                                self.exportButton1,
                                ft.VerticalDivider(width=10, color=ft.colors.TRANSPARENT),
                                self.exportButton2
                            ],
                            alignment=ft.MainAxisAlignment.START,
                            vertical_alignment=ft.CrossAxisAlignment.CENTER,
                            spacing=10,
                        ),
                    ],
                    spacing=10,
                ),bgcolor=ft.colors.WHITE,
        border_color=ft.colors.GREEN_900,
        width=730,
            ),]

class button_fip_contract(ft.Column):
    def __init__(self):
        super().__init__()
        self.visible = True
        self.expand = True
        self.spacing = 5
        # 创建合同按钮样式
        contract_button_style = ft.ButtonStyle(
            shape=ft.RoundedRectangleBorder(radius=8),
            padding=ft.padding.symmetric(horizontal=20, vertical=12),
            color=ft.colors.WHITE,
            bgcolor=ft.colors.TEAL_600,
            overlay_color=ft.colors.TEAL_700,
            elevation=2,
            shadow_color=ft.colors.with_opacity(0.2, ft.colors.TEAL_900),
            animation_duration=200,
        )
        
        self.exportButton1 = ft.ElevatedButton(
            text="导出一体化合同台账",
            icon=ft.icons.CONTACT_EMERGENCY_OUTLINED,
            style=contract_button_style,
            on_click=lambda e: callF.thisProcess.run(
                {"功能": "导出一体化合同台账"}
            ),
            width=200,
            height=45
        )
        self.controls = [LabeledContainer(
        label_text="导出一体化合同台账",
        content=ft.Column(
                    controls=[
                        ft.Row(
                            [ft.Text("此处为导出一体化合同履行情况"),self.exportButton1],
                            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                            vertical_alignment=ft.CrossAxisAlignment.CENTER,
                            spacing=5,
                        ),
                    ],
                    spacing=10,
                ),bgcolor=ft.colors.WHITE,
        border_color=ft.colors.GREEN_900,
        width=730,
            ),]
        
class button_fip_other(ft.Column):
    def __init__(self):
        super().__init__()
        self.visible = True
        self.expand = True
        self.spacing = 5
        
        # 创建按钮样式
        primary_button_style = ft.ButtonStyle(
            shape=ft.RoundedRectangleBorder(radius=8),
            padding=ft.padding.symmetric(horizontal=20, vertical=12),
            color=ft.colors.WHITE,
            bgcolor=ft.colors.BLUE_600,
            overlay_color=ft.colors.BLUE_700,
            elevation=2,
            shadow_color=ft.colors.with_opacity(0.2, ft.colors.BLUE_900),
            animation_duration=200,
        )
        
        warning_button_style = ft.ButtonStyle(
            shape=ft.RoundedRectangleBorder(radius=8),
            padding=ft.padding.symmetric(horizontal=20, vertical=12),
            color=ft.colors.WHITE,
            bgcolor=ft.colors.ORANGE_600,
            overlay_color=ft.colors.ORANGE_700,
            elevation=2,
            shadow_color=ft.colors.with_opacity(0.2, ft.colors.ORANGE_900),
            animation_duration=200,
        )
        
        self.exportButton1 = ft.ElevatedButton(
            text="重新开始",
            icon=ft.icons.REFRESH,
            style=warning_button_style,
            on_click=lambda e: callF.thisProcess.run({"功能": "更新预付款及保证金台账","参数":["no"]}),
            width=120,
            height=45
        )

        self.exportButton2 = ft.ElevatedButton(
            text="失败继续",
            icon=ft.icons.PLAY_ARROW,
            style=primary_button_style,
            on_click=lambda e: callF.thisProcess.run({"功能": "更新预付款及保证金台账","参数":["yes"]}),
            width=120,
            height=45
        )
        
        self.controls = [
            LabeledContainer(
                label_text="导出一体化保证金及预付台账",
                content=ft.Column(
                    controls=[
                        ft.Row(
                            [
                                ft.Container(
                                    content=ft.Text(
                                        "导出预付款及保证金台账用于稽核",
                                        size=14,
                                        weight=ft.FontWeight.W_500,
                                        color=ft.colors.BLUE_GREY_800
                                    ),
                                    padding=10,
                                    expand=True
                                ),
                                self.exportButton1,
                                ft.VerticalDivider(width=10, color=ft.colors.TRANSPARENT),
                                self.exportButton2
                            ],
                            alignment=ft.MainAxisAlignment.START,
                            vertical_alignment=ft.CrossAxisAlignment.CENTER,
                            spacing=10,
                        ),
                    ],
                    spacing=10,
                ),
                bgcolor=ft.colors.WHITE,
                border_color=ft.colors.DEEP_ORANGE_900,
                width=730,
            )
        ]