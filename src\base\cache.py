import os
import sys
import shelve
import src.base.settings as settings
#获取日期
from datetime import datetime

Lastdate=None
Nowdate= datetime.now().strftime('%Y-%m-%d')
theYearFirstDay=datetime.now().strftime('%Y-01-01')
LastStateAssetsCouncilReportPath=''
CurrentFinancialReport=''
LastYearFinancialReport=''
CurrentYearFinancialReport=''

with shelve.open(settings.CACHE_PATH+r'/mydata') as cache:
    if 'last_date' not in cache:
        Lastdate = datetime.now().strftime('%Y-%m-%d')
    else:
        Lastdate=cache['last_date']
    
    if 'state_assets_council_report_path' not in cache:
        LastStateAssetsCouncilReportPath = ''
    else:
        LastStateAssetsCouncilReportPath=cache['state_assets_council_report_path']
    if 'current_financial_report' not in cache:
        CurrentFinancialReport = ''
    else:
        CurrentFinancialReport=cache['current_financial_report']
    
    if 'last_year_financial_report' not in cache:
        LastYearFinancialReport = ''
    else:
        LastYearFinancialReport=cache['last_year_financial_report']
    
    if 'current_year_financial_report' not in cache:
        CurrentYearFinancialReport = ''
    else:
        CurrentYearFinancialReport=cache['current_year_financial_report']



def wirteNowdate():
    with shelve.open(settings.CACHE_PATH+'/mydata',writeback=True) as cache:
        cache['last_date'] = Nowdate

def wirteLastPath():
    with shelve.open(settings.CACHE_PATH+'/mydata',writeback=True) as cache:
        cache['last_sheet_path'] = theLastSheetPath
def wirteLastStateAssetsCouncilReportPath():
    with shelve.open(settings.CACHE_PATH+'/mydata',writeback=True) as cache:
        cache['state_assets_council_report_path'] = LastStateAssetsCouncilReportPath

def wirteCurrentFinancialReport():
    with shelve.open(settings.CACHE_PATH+'/mydata',writeback=True) as cache:
        cache['current_financial_report'] = CurrentFinancialReport

def wirteLastYearFinancialReport():
    with shelve.open(settings.CACHE_PATH+'/mydata',writeback=True) as cache:
        cache['last_year_financial_report'] = LastYearFinancialReport

def wirteCurrentYearFinancialReport():
    with shelve.open(settings.CACHE_PATH+'/mydata',writeback=True) as cache:
        cache['current_year_financial_report'] = CurrentYearFinancialReport