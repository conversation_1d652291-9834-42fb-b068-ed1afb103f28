
from itertools import accumulate
from playwright.sync_api import Playwright, sync_playwright
import src.utils.sapPublic.GetSAPSession as GetSAPSession
from src.utils.DB.midIntrSQLiteDB import excelDB
from src.utils.DB.mainDB import mainDB
import pandas as pd

def autofill(year,month):
    conn=mainDB().conn
    projectList=conn.execute("select any_value(WBS元素) as WBS元素,any_value(利润中心) as 利润中心 from 科目余额表第二期 WHERE WBS元素 != '' and WBS元素 not like 'QCQH%' GROUP by 利润中心,WBS元素").fetchall()
    conn.close()
    session = GetSAPSession.creatSAP()
    accumulateList=[["利润中心","项目编码","累计收入","累计成本"]]
    for i in range(len(projectList)):
        print("开始序号"+str(i+1)+"行")
        session.StartTransaction("ZARAP0003")
        try:
            session.findById("wnd[0]/usr/ctxtS_PRCTR-LOW").text = projectList[i][1]#填写利润中心
            session.findById("wnd[0]/usr/ctxtS_PSPNR-LOW").text = projectList[i][0]#填写项目编码
            session.findById("wnd[0]/usr/txtS_GJAHR-LOW").text = year#年份
            session.findById("wnd[0]/usr/txtS_MONAT-LOW").text = month #月份
            session.findById("wnd[0]/usr/ctxtS_PSPNR-LOW").setFocus()
            session.findById("wnd[0]/usr/radP_CSGN").select() #选中收入测算功能
            session.findById("wnd[0]/tbar[1]/btn[8]").press() #执行
            CB=session.findById("wnd[0]/usr/subSUB02:ZARAPRP0002:9220/txtGS_CSD-VERDATA-CB_LJYGZ").text #cost成本
            SR=session.findById("wnd[0]/usr/subSUB02:ZARAPRP0002:9220/txtGS_CSD-VERDATA-SR_LJYGZ").text #income收入  
            CB=float(CB.replace(",",""))
            SR=float(SR.replace(",",""))
            session.findById("wnd[0]/tbar[0]/btn[3]").press() #返回
            accumulateList.append([projectList[i][1],projectList[i][0],SR,CB])
        except:
            print("项目编码"+projectList[i][0]+"利润中心"+projectList[i][1]+"获取失败")
            pass
    session.findById("wnd[0]").Close()
    df=pd.DataFrame(data=accumulateList[1:],columns=accumulateList[0])
    conn=excelDB().conn
    df.to_sql("累计过账收入成本", conn, if_exists='replace', index=False)
    conn.close()