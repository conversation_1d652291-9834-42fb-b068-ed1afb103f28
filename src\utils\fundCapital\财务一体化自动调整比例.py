
from playwright.sync_api import Page
from playwright.sync_api import ChromiumBrowserContext
import time
import src.utils.cscec as cscec
import src.utils.Browser.Browser as browser
import src.utils.Excel.excel as excel


def label_question(s):
    s="//label[contains(text(),'"+s+"')]/parent::div/following-sibling::div[1]/div/div/div"
    return s

def autoOne(page:Page,d: dict):  
    cscec.changeProjectCscec(page,d["组织机构"],d["项目名称"])
                    #先切换项目
    page.locator("//*[@id='root']/section/section/aside/div/div/div[2]/div/div[1]/ul[2]/li").click()
    page.locator("//span[contains(text(),'合同履行状态变更申请')]").click()

    page.get_by_placeholder("事由不能超过").fill(d["事由"]) 
    page.keyboard.type(d["事由"])
    page.locator(label_question("项目名称")).first.click()
    s="//div[text()='名称']/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table/tbody[2]//td[1]"
    page.locator(s).first.dblclick() #XN0+公司编码级次1
    s="//div[contains(text(),'原合同状态')]//parent::span/parent::div/parent::td/preceding-sibling::td[7]" #通过科目编号找到增加按钮
    page.locator(s).click()
    page.get_by_text("增加").click()
    s="//div[contains(text(),'原合同状态')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tbody[2]/tr[1]/td[2]"
    page.locator(s).click()
    time.sleep(0.2)
    cscec.clickMouse(page,s,95,50) #点击问号
    page.get_by_placeholder("请输入查询合同编号关键字").fill(d["合同编号"])
    page.locator("//*[text()='合同帮助']/parent::div/parent::div/parent::div//span[contains(text(),'查询')]/parent::div/parent::div").click()
    page.get_by_text(d["合同编号"]).first.dblclick()

    s="//div[contains(text(),'原合同状态')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tbody[2]/tr[1]/td[11]"
    page.locator(s).dblclick()
    cscec.getInputAbove(page,s).fill(str(d["调整比例"])) #调整金额0.01
    page.get_by_placeholder("事由不能超过").click()

    page.locator("//span[text()='保存']/parent::div/parent::div/parent::div/parent::div").click()
    page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='确定']").click()
    page.locator("//span[text()='保存']/parent::div/parent::div/parent::div/parent::div/parent::div//span[text()='提交']/parent::div/parent::div/parent::div/parent::div").click() #通过保存来确定提交按钮
    page.locator("//*[text()='处理意见']/parent::div/parent::div/parent::div//div[text()='确定']").click()
    page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='确定']").click()
    page.locator("//button[@aria-label='remove']//span[@aria-label='close']//*[name()='svg']//*[name()='path' and contains(@d,'M563.8 512')]").click()

def autoOperate():
    page=browser.myBrowser("cscec").page
    wb=excel.myBook()
    ws=wb.sheet("一体化调整比例")
    start_number=int(ws.Cells(1,2).Value)
    table=ws.table("表付款比例")
    max_number=ws.MaxRow
    for i in range(start_number+1,max_number+1):  #跳过标题行+1
        d=table.toDict(i)
        autoOne(page,d)
        ws.Cells(1,2).Value=ws.Cells(1,2).Value+1
