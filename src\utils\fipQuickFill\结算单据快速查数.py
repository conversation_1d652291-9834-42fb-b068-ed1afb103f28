
import openpyxl.workbook
from playwright.sync_api import Playwright, sync_playwright,Page
import src.utils.cscec as cscec
import openpyxl 
import  src.base.settings as settings
def queryData():
    wb=openpyxl.workbook.Workbook()
    ws=wb.active
    row=["不含税金额","进项税额","客商","合同名称","存货类型编号"]
    ws.append(row)
    with sync_playwright() as playwright:
        browser = playwright.chromium.connect_over_cdp("http://localhost:9222")
        default_context = browser.contexts[0]
        page = cscec.switch_to_page(default_context,"中国建筑司库一体化")
        table=cscec.cscecTable(page,"组织机构")
        for i in range(1,table.count+1):
            businesstype=table.getValue(i,"业务类型/发起时间") 
            if "结算单" in businesstype:
                table.click(i,"单据") 
                page.locator("//div[text()='账期信息']").click()
                a1=cscec.getLalbel_inputValue(page,"*合同名称：")
                a2=cscec.getLalbel_inputValue(page,"*不含税金额：")
                a3=cscec.getLalbel_inputValue(page,"*进项税额：")
                a4=cscec.getLalbel_inputValue(page,"*客　　商：")
                a5=''
                if "物资材料" in businesstype:
                    table2=cscec.cscecTable(page,"存货类型编号")
                    a5=table2.getValue(1,"存货类型编号")
                row=[a2,a3,a4,a1,a5]
                ws.append(row)
                cscec.closeTab(page)
    wb.save(settings.PATH_EXCEL+"\结算单据数据.xlsx")
    print("数据保存在->打开文件夹->\Excel中间文件\结算单据数据.xlsx")