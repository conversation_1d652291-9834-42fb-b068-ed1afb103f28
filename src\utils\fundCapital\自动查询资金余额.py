
import os
import sys
import src.base.settings as settings
import src.utils.Browser.Browser as browser
import time
import src.utils.cscec as cscec
import src.utils.Excel.openpyxlExcel as openpyxlExcel



def main(date1,date2):
    B=browser.myBrowser("cscec")
    page=B.page
    jsText='''
        js_need=>{var spread = GC.Spread.Sheets.findControl(js_need);
        sheet=spread.getSheet(0);
        var rowCount = sheet.getRowCount(); // 获取最大行数
        var colCount = sheet.getColumnCount(); // 获取最大列数
        var arr = new Array(rowCount);


        for (var i = 0; i < rowCount; i++) { // 遍历行
        arr[i] = new Array(colCount);
        for (var j = 0; j < colCount; j++) { // 遍历列
            var cellValue = sheet.getValue(i, j); // 获取单元格的值
            arr[i][j]=(cellValue);// 将单元格的值存入数组
        }
        }
        return arr;}
        '''
    from src.utils.DB.configDB import configDB
    circularList=configDB().financialIntegrationUnits
    systemArr=[]
    for i in range(len(circularList)):
        cscec.toFunction(page,"司库系统","数据查询","账户系统余额查询")
        page.locator("//span[text()='核算账户余额查询']").click()
        organizationCode=circularList[i][0]
        print("当前组织机构为："+circularList[i][1])
        cscec.Lablechoose2(page,"单位名称：","请输入查询关键字","帮助字典",organizationCode)
        time.sleep(1)
        js1 = 'jsneed=>{jsneed.removeAttribute("readonly");}'
        jsneed=cscec.getVisible(page,"//input[@id='DATE1-input']")
        jsneed.evaluate(js1)
        jsneed.fill(date1)
        try:
            page.locator("//*[text()='系统提示']//parent::div/parent::div/parent::div//span[text()='确 定']").click(timeout=1000)
        except:
            pass
        time.sleep(1)
        jsneed=cscec.getVisible(page,"//input[@id='DATE2-input']")
        jsneed.evaluate(js1)
        jsneed.fill(date2)
        try:
            page.locator("//*[text()='系统提示']//parent::div/parent::div/parent::div//span[text()='确 定']").click(timeout=1000)
        except:
            pass
        cscec.getVisible(page,page.locator("//label").filter(has_text="基本信息查询")).click()
        cscec.getVisible(page,"//span[text()='查询']").click()
        time.sleep(1)
        js_need=cscec.getVisible(page,"//div[@gcuielement='gcSpread']") #
        waitCount=10
        while waitCount>0:
            systemArr0=js_need.evaluate(jsText)
            if len(systemArr0)<6:
                time.sleep(3)
                waitCount = waitCount-1
            else:
                systemArr=systemArr+js_need.evaluate(jsText)
                waitCount =-1
                break
        cscec.closeTab(page)
        time.sleep(1)
    import openpyxl
    wb = openpyxl.Workbook()
    ws = wb.active
    for i in range(len(systemArr)):
        ws.append(systemArr[i])
    wb.save(settings.PATH_EXCEL+"\中台资金余额.xlsx")
    print("数据保存在->打开"+settings.PATH_EXCEL+"\中台资金余额.xlsx")