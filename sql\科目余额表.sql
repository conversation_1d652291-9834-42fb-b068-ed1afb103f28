select 
any_value(总账科目) as 总账科目,
any_value(总账科目长文本) as 总账科目长文本,
any_value(利润中心) as 利润中心,
any_value(利润中心描述) as 利润中心名称,
any_value(WBS元素) as WBS元素,
any_value(WBS元素描述) as WBS元素描述,
any_value(合同) as 合同,
any_value(合同文本描述) as 合同文本描述,
any_value(供应商) as 供应商,
any_value(供应商描述) as 供应商名称,
any_value(客户) as 客户,
any_value(客户描述) as 客户名称,
sum(case when (过帐日期<'期初日期留 00:00:00')  then 带符号的本位币金额 else null end) as 期初余额,
sum(带符号的本位币金额) as 期末余额,
sum(case when (((借贷标识 = 'H' and 反记帐 = '') or (借贷标识 = 'S' and 反记帐 = 'X')) and ((过帐日期 >= '期初日期留 00:00:00') )) then 带符号的本位币金额 else null end) as 本期贷方发生额,
sum(case when (((借贷标识 = 'S' and 反记帐 = '') or (借贷标识 = 'H' and 反记帐 = 'X')) and ((过帐日期 >= '期初日期留 00:00:00') )) then 带符号的本位币金额 else null end) as 本期借方发生额,
sum(case when (((借贷标识 = 'H' and 反记帐 = '') or (借贷标识 = 'S' and 反记帐 = 'X')) ) then 带符号的本位币金额 else null end) as 累计贷方发生额,
sum(case when (((借贷标识 = 'S' and 反记帐 = '') or (借贷标识 = 'H' and 反记帐 = 'X')) ) then 带符号的本位币金额 else null end) as 累计借方发生额,
(case when 期末余额>0.0001 then '借' when 期末余额<-0.0001 then '贷' else '平' end) as 期末方向 
from 明细帐 where  文本 != '自动清账剩余项目'  and 过帐日期<='期末日期留 00:00:00' GROUP BY 总账科目长文本,利润中心,合同,供应商,客户,WBS元素