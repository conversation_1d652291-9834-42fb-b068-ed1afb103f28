import tkinter as tk
from tkinter import ttk
import tksheet
import traceback # 用于打印详细的错误堆栈
import tkinter as tk
from tkinter import ttk
import tksheet
import traceback # 用于打印详细的错误堆栈

class EditableTableApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Editable Table with Auto Calculation")
        self.root.geometry("600x400")

        self.sheet_frame = ttk.Frame(self.root)
        self.sheet_frame.pack(fill="both", expand=True, padx=10, pady=10)

        self.sheet = tksheet.Sheet(self.sheet_frame)
        self.sheet.pack(fill="both", expand=True)

        # Sample data: Last column will be auto-calculated (e.g., Col1 + Col2)
        self.data = [
            [10, 20, 0],
            [5, 15, 0],
            [25, 30, 0]
        ]
        self.headers = ["Value 1", "Value 2", "Sum (Auto)"]

        self.sheet.set_sheet_data(self.data, headers=self.headers)
        self.sheet.enable_bindings("edit_cell")
        self.sheet.extra_bindings([("cell_edited", self.cell_edited_handler)])

        # Make first two columns editable
        self.sheet.readonly_columns([2], readonly = True)


    def cell_edited_handler(self, event):
        print(f"Cell edited: {event}")
        try:
            row = event.row
            # col = event.column # column that was edited
            # new_value = event.value

            # Assuming Value 1 and Value 2 are in columns 0 and 1
            # and Sum is in column 2
            if 0 <= row < len(self.data):
                val1_str = self.sheet.get_cell_data(row, 0)
                val2_str = self.sheet.get_cell_data(row, 1)

                # Ensure values are treated as numbers, default to 0 if not
                try:
                    val1 = float(val1_str) if val1_str else 0
                except ValueError:
                    val1 = 0
                    # self.sheet.set_cell_data(row, 0, 0, redraw=True) # Optionally reset invalid input

                try:
                    val2 = float(val2_str) if val2_str else 0
                except ValueError:
                    val2 = 0
                    # self.sheet.set_cell_data(row, 1, 0, redraw=True) # Optionally reset invalid input

                calculated_sum = val1 + val2
                self.sheet.set_cell_data(row, 2, calculated_sum, redraw=True)
                # Update underlying data store if you have one
                self.data[row][2] = calculated_sum
                print(f"Row {row} updated. New sum: {calculated_sum}")

        except Exception as e:
            print("Error in cell_edited_handler:")
            traceback.print_exc()


if __name__ == "__main__":
    root = tk.Tk()
    app = EditableTableApp(root)
    root.mainloop()
