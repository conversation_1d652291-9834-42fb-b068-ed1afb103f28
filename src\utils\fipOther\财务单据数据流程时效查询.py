import sys
import os
def initRuntimeEnvironment(startup_script):
    """初始化运行环境。startup_script: 启动脚本路径"""

    import site
    # 定义一个最简单的消息弹窗
    # 初始化工作目录和Python搜索路径
    script = os.path.abspath(startup_script)  # 启动脚本.py的路径
    home = os.path.dirname(script)  # 工作目录
    os.chdir(home)  # 重新设定工作目录（不在最顶层，而在UmiOCR-data文件夹下）
    for n in ['.', '.site-packages']:  # 将模块目录添加到 Python 搜索路径中
        path = os.path.abspath(os.path.join(home, n))
        if os.path.exists(path):
            site.addsitedir(path)
sys.path.append("..")

initRuntimeEnvironment(__file__)  # 初始化运行环境

import Gui.ui as ui
import excelto
import time
import cscec
import threading

def main():
    lock= threading.Lock()
    wsObject=excelto.myWrokSheet("数据")
    context=cscec.getContext("9222")
    page0=context.pages[0]
    page1=context.pages[1]
    page2=context.pages[2]

    listNeed=wsObject.getUsedrangeValue()
    d={}
    for key in listNeed:
        d[key[0]] =1
    def singlePageQuery(page):
        tr=cscec.getTr(page,"单据编号")
        for i in range(0,tr.count()):
            tr.nth(i).locator("//td[5]").click()
            d1=tr.nth(i).locator("//td[2]/div").text_content()
            lock.acquire()
            whetherToDownload=d[d1]
            d[d1]=0
            lock.release()
            if whetherToDownload==1:
                d2=tr.nth(i).locator("//td[7]/div").text_content()
                cscec.getVisible(page,"//span[text()='流程']").click()
                time.sleep(1)
                tr2=cscec.getTr(page,"处理意见")
                a=[]
                for j in range(tr2.count()):
                    b=[]
                    b.append(d1)
                    b.append(d2)
                    for l in range(1,14):
                        try:
                            b.append(tr2.nth(j).locator(f"//td[{l}]/div").text_content(timeout=2000))
                        except Exception as e:
                            print(e)
                    #ws.Range(k,1).GetResize(1,len(a)).Value=a
                    a.append(b)
                cscec.getVisible(page,"//table[@class='x-panel-toolbar']").click()
                lock.acquire()
                maxRow=wsObject.getMaxRow()
                wsObject.rangeResize(f"a{maxRow}",len(a),len(a[0])).Value=a
                lock.release()

    t1 = threading.Thread(target=singlePageQuery,args=(page0,))
    t1.setDaemon(True)
    t1.start()
    t2 = threading.Thread(target=singlePageQuery,args=(page1,))
    t2.setDaemon(True)
    t2.start().start()
    t3 = threading.Thread(target=singlePageQuery,args=(page2,))
    t3.setDaemon(True)
    t3.start().start().start()

import Gui.ui as ui
if __name__=='__main__':
    ui.runAuto(main)