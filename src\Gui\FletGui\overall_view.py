from src.Gui.FletGui.left_navigation_menu import LeftNavigationMenu
from src.Gui.FletGui.middle_view import middleView
from src.Gui.FletGui.right_view import rightView

import flet as ft

class overallView(ft.Row):
    def __init__(self):
        super().__init__()
        self.left_nav = LeftNavigationMenu()
        self.middle_view = middleView()
        self.right_view = rightView()
        self.expand = True
        self.controls = [
            self.left_nav,
            ft.VerticalDivider(width=1),
            self.middle_view,
            ft.VerticalDivider(width=1),
            self.right_view,
        ]
    def changeMiddleView(self,name):
        self.middle_view.changeView(name)


