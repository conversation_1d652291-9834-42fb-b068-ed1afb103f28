import sys
import os
import time
import re
sys.path.append(".")
import src.utils.Excel
import src.utils.sapPublic
import src.utils.cscec as cscec
import src.utils.Excel.excel as excel
import src.utils.Excel.openpyxlExcel
import src.utils.Browser.Browser as myBrowser
import src.utils.dopReport.财商自动同步应收账款台账
import src.utils.fipOther.财务一体化自动物资结算2
import src.utils.DB.creatDuckdbTable
import src.utils.dopReport.moneyIOLedger
import src.utils.fundCapital.财务一体化自动下载资金计划
import duckdb
import src.base.settings as settings
import src.utils.DB.outputSQL  as Sql
import pandas as pd
import src.utils.sapPublic.sapExport
import tkinter.filedialog
import src.utils.DB.readtxttolist
import src.utils.closeAccountingPeriod.obtainData
import src.utils.fipOther.财务一体化自动物资出库
import src.utils.dopReport.collect
import src.utils.fundCapital.财务一体化自动调整比例

import src.utils.dopReport.specialPayment
#src.utils.dopReport.specialPayment.createExcelTamplate()
#src.utils.dopReport.specialPayment.batchSubmitSpecialPayment()

import src.utils.financialStatement.completion 
import src.utils.Excel.openpyxlExcel as openpyxlExcel

concatArray=openpyxlExcel.getUsedList("总部客商表")
concatArrayTuple=tuple(concatArray[i][0] for i in range(1,len(concatArray)))
replaceCondition1=str(concatArrayTuple)
print(replaceCondition1)
