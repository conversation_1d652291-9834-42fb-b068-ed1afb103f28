
from playwright.sync_api import Playwright, sync_playwright,Page
import src.utils.cscec as cscec
from src.utils.DB.midIntrSQLiteDB import excelDB
import src.utils.fileui as fileui
import time
import pandas as pd

def queryVoucherCount(year,month):
    import calendar
    # 获取当月的最后一天
    last_day = calendar.monthrange(int(year), int(month))[1]
    import src.utils.DB.mainDB as mainDB
    db=mainDB.mainDB()
    df=db.conn.execute(''' select any_value(明细帐.利润中心) as 利润中心,any_value(明细帐.利润中心描述) as 利润中心描述,
any_value(利润中心组描述) as 利润中心组描述,
count(DISTINCT 凭证编号) as 凭证数量                      
from 明细帐
left join 主数据 on 明细帐.利润中心 = 主数据.利润中心
where 过帐日期 BETWEEN STRPTIME(?, '%Y-%m-%d') and STRPTIME(?, '%Y-%m-%d') 
GROUP BY 明细帐.利润中心 ''',(f"{year}-{month}-01",f"{year}-{month}-{last_day}")).df()
    db.close()
    file_dir = fileui.select_directory()
    df.to_excel(file_dir + "/凭证数量.xlsx", index=False)

def uploadFile( ):
    conn=excelDB()
    file_path = fileui.select_file()
    if file_path:
        df = pd.read_excel(file_path)
        df.to_sql("档案成册管理", conn.conn, if_exists='replace', index=False)
        conn.conn.close()

def downloadFile():
    conn=excelDB()
    try:
        df = pd.read_sql("SELECT * FROM 档案成册管理", conn.conn)
    except:
        df = pd.DataFrame(columns=["是否", "组织编码", "利润中心编码", "利润中心名称", "利润中心组描述"])
    conn.conn.close()
    file_dir = fileui.select_directory()
    df.to_excel(file_dir + "/档案成册管理.xlsx", index=False)
  
def main():
    with sync_playwright() as playwright:
        browser = playwright.chromium.connect_over_cdp("http://localhost:9222")
        default_context = browser.contexts[0]
        page = cscec.switch_to_page(default_context,"中国建筑司库")
        conn=excelDB()
        df= pd.read_sql("SELECT * FROM 档案成册管理", conn.conn)
   
        print("共有"+str(df.index.size)+"条数据")
        for i in range(df.index.size):
            print("正在处理第"+str(i+1)+"条数据")
            if df.loc[i,"是否"]=="是":
                conn.updateData("档案成册管理","是否","本行执行中",i+1)
                cscec.clickLabel(page,"组织机构：")
                cscec.dialogInput(page,df.loc[i,"组织编码"])
                cscec.Lablechoose(page,"利润中心：","编号/名称","利润中心",df.loc[i,"利润中心编码"])
                cscec.getVisible(page,"//span[text()='查询']").click()
                cscec.getVisible(page,f"//td/div[text()='{df.loc[i,"利润中心编码"]}']")
                cscec.getVisible(page,"//span[text()='同步流水号']").click()
                cscec.getVisible(page,"//td/div[text()='000001']")
                cscec.clickDigalog(page,"系统提示")
                cscec.getVisible(page,"//span[text()='确认整理完成']").click()
                cscec.clickDigalog(page,"系统提示")
                try:
                    cscec.clickDigalog(page,"录入册信息",timeWait=30)
                    cscec.clickDigalog(page,"系统提示","否")
                except:
                    cscec.getVisible(page,"//span[text()='档案成册']").click()
                    cscec.clickDigalog(page,"系统提示")
                    cscec.clickDigalog(page,"系统提示")
                    cscec.clickDigalog(page,"系统提示")
                print("档案成册+1")
                conn.conn.execute("UPDATE 档案成册管理 SET 是否='本行执行完毕' where rowid=?",(i+1,))
                conn.conn.commit()
                time.sleep(1)
        conn.conn.close()  