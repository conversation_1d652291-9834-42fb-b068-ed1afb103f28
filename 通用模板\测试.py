import sys
sys.path.append(".")

import src.utils.sapPublic.GetSAPSession as GetSAPSession
import openpyxl
session = GetSAPSession.creatSAP()
shellgird=session.findById("wnd[0]/usr/cntlGRID1/shellcont/shell")
titles=["<PERSON><PERSON><PERSON>H<PERSON>","<PERSON>ON<PERSON>"
    ,"DOC_NR"
    ,"DOC_VER"
    ,"Z<PERSON><PERSON><PERSON>H"
    ,"P<PERSON>ID"
    ,"POST1"
    ,"PRCTR"
    ,"LTEXT"
    ,"BUKRS"
    ,"BUTXT"
    ,"KUNNR"
    ,"ZDQ"
    ,"PGSBR"
    ,"ZYJZSR"
    ,"ZYJZCB"
    ,"ZYJML"
    ,"ZYJMLL"
    ,"Z<PERSON>JYGZC<PERSON>"
    ,"Z<PERSON><PERSON>JD"
    ,"ZLJBQSR"
    ,"ZLJQQSR"
    ,"ZBQSR"
    ,"ZLJBQCB"
    ,"ZLZQQCB"
    ,"ZBQCB"
    ,"ZLJBQML"
    ,"<PERSON><PERSON>JQQ<PERSON>"
    ,"<PERSON><PERSON><PERSON><PERSON>"
    ,"<PERSON>LJBQY<PERSON><PERSON>"
    ,"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"
    ,"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"
    ,"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"
    ,"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"
    ,"<PERSON><PERSON><PERSON><PERSON>WW<PERSON>"
    ,"<PERSON><PERSON>J<PERSON><PERSON>"
    ,"ZBNJLR"
    ,"ZBQJLR"
    ,"CB_QZZDSBTR"]

first=[]
second=[]
for title in titles:
    first.append(shellgird.getCellValue(-1, title))
    needTrans=shellgird.getCellValue(0, title).replace(",","")
    if needTrans[-1]=="-":
        needTrans=float("-"+needTrans[:-1])
    if isinstance(needTrans,str):
        try:
            needTrans=float(needTrans)
        except:
            pass
    second.append(needTrans)


wb=openpyxl.Workbook()
ws=wb.active
ws.append(first)
ws.append(second)
wb.save(r"C:\Users\<USER>\Desktop\123.xlsx")



