import os
import sys
sys.path.append(".")

from playwright.sync_api import Page
from playwright.sync_api import ChromiumBrowserContext
import src.utils.cscec as cscec
import src.utils.Excel.excel as excel
import time
def label_question(s):
    s="//label[contains(text(),'"+s+"')]/parent::div/following-sibling::div[1]/div/div/div"
    return s


def automaticCollectin(page:Page,contractCode,reasons):  
    tr=cscec.getTr(page,"单据编号")#自带可见匹配
    print(tr.count())
    for i in range(tr.count()):
        cscec.getVisible(page,"//div[text()='查询']").click()
        time.sleep(2)
        tr.nth(0).locator("//td[1]").click()
        cscec.getVisible(page,"//div[text()='认领']").click()
        s="//div[contains(text(),'现金流项目')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//td[3]"
        page.locator(s).dblclick()
        time.sleep(0.05)
        cscec.clickMouse(page,s,95,50) #点击问号
        page.get_by_text(contractCode).dblclick()


        s="//div[contains(text(),'现金流项目')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//td[5]"
        page.locator(s).dblclick()
        time.sleep(0.05)
        cscec.clickMouse(page,s,95,50) #点击问号
        page.get_by_text("工程款-进度结算").dblclick()

        s="//div[contains(text(),'现金流项目')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//td[6]"
        page.locator(s).dblclick()
        time.sleep(0.05)
        cscec.clickMouse(page,s,95,50) #点击问号
        page.get_by_text("A110101").dblclick()
        m0=cscec.getVisible(page,"//label[contains(text(),'收款币种金额')]/parent::div/following-sibling::div[1]//input").get_attribute("value")
        m0=float(m0.replace(',',''))
        s="//div[contains(text(),'应收账款单号')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table[1]"
        c_number=cscec.getVisible(page,s).locator("//tbody[2]/tr").count()
        print(c_number)

        if c_number>2:
            for j in range(1,c_number-1):
                    s="//div[contains(text(),'本次核销金额')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table[1]/tbody[2]/tr["+str(j)+"]//td[6]/div"
                    m1=float(cscec.getVisible(page,s).text_content().replace(',',''))
                    s="//div[contains(text(),'本次核销金额')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table[1]/tbody[2]/tr["+str(j)+"]//td[7]/div"
                    cscec.getVisible(page,s).click()
                    s="//div[contains(text(),'本次核销金额')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table[1]/tbody[2]/tr["+str(j)+"]//td[7]"
                    print(m0)
                    print(m1)
                    if m0>m1 and m0>0:
                        #cscec.getInputAbove(page,s).fill(str(round(m0,2))) 
                        cscec.getInputAbove(page,s).fill(str(round(m1,2))) 
                    elif m0>0:
                        #cscec.getInputAbove(page,s).fill(str(round(m1,2)))
                        cscec.getInputAbove(page,s).fill(str(round(m0,2)))
                    m0=m0-m1
        page.get_by_text("收款币种金额：").click()
        page.get_by_placeholder("事由不能超过").fill(reasons) 
        cscec.chooseIfPaper(page)
        page.get_by_text("收款币种金额：").click()
        page.locator("//span[text()='保存']/parent::div/parent::div/parent::div/parent::div").click()
        time.sleep(0.5)
        page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='确定']").click()
        page.locator("//span[text()='保存']/parent::div/parent::div/parent::div/parent::div/parent::div//span[text()='提交']/parent::div/parent::div/parent::div/parent::div").click() #通过保存来确定提交按钮
        page.locator("//*[text()='处理意见']/parent::div/parent::div/parent::div//div[text()='确定' or text()='提交']").click()
        cscec.clickDigalog(page,"系统提示")
        page.locator("//div[@class='ant-tabs-tab ant-tabs-tab-with-remove ant-tabs-tab-active']//span[@aria-label='close']//*[name()='svg']").click()

def autoOperate():
    context=cscec.getContext("9222")
    contractCode="中建三局020020230399020001"
    reasons="南方韶关项目收总包融信代付款"
    page=cscec.switch_to_page(context,title="中国建筑司库一体化")
    automaticCollectin(page,contractCode,reasons)
    
autoOperate()
