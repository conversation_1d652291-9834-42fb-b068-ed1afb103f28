import os
import sys
import time
sys.path.append(".")
from playwright.sync_api import Playwright, sync_playwright,Page
import src.utils.cscec as cscec
import src.utils.Excel.excel as excel
from tkinter import filedialog
import openpyxl

def fipMaterial(page:Page,s):
    theWs=excel.myBook().sheet("研发费用")
    theTable=theWs.table("表1")
    startCount=int(theWs.Cells(1,2).Value)+1
    for i in range(startCount,theTable.MaxRow+1):
        with sync_playwright() as playwright:
            browser = playwright.chromium.connect_over_cdp("http://localhost:9222")
            default_context = browser.contexts[0]
            page = cscec.switch_to_page(default_context,"中建司库一体化")
            cscec.changeProjectCscec(page,theTable.getValue(i,"组织机构"),theTable.getValue(i,"项目名称"))
            cscec.toFunction(page,"报账系统","物资及资产","出库汇总单-施工行业")
            cscec.chooseIfPaper(page,True)
            table=cscec.cscecTable(page,"存货类型编号")
            table.clickInputQuery2(1,"*存货类型编号")
            cscec.dialogInput(page,"1001012")
            table.fillInput(1,"*存货名称/备注","机电材料及设备")
            table.clickInputQuery2(1,"*计量单位")
            cscec.dialogInput(page,"批")
            if s=="研发":
                cscec.fillLalbel_input(page,"*事       由：",theTable.getValue(i,"研发事由"))
                table.fillInput(1,"*出库单价",theTable.getValue(i,"研发金额"))
                table.fillInput(1,"*物资领用数量","1")
                table.clickInputQuery2(1,"*物资用途") 
                cscec.dialogInput(page,"1000040") #研发支出，其他材料
                time.sleep(1)
                table.reIndex()
                table.clickInputQuery2(1,"*科研课题")
                rdCode=theTable.getValue(i,"课题编码") if type(theTable.getValue(i,"课题编码"))==str else str(int(theTable.getValue(i,"课题编码")))
                cscec.dialogInput(page,rdCode) #选择科研课题
            else:
                cscec.fillLalbel_input(page,"*事       由：",theTable.getValue(i,"退库事由"))
                table.fillInput(1,"*出库单价",theTable.getValue(i,"退库金额"))
                table.fillInput(1,"*物资领用数量","1")
                table.clickInputQuery2(1,"*物资用途") 
                cscec.dialogInput(page,"110")  #工程实体耗用
            cscec.uploadAttachment(page,theTable.getValue(i,"附件地址"))
            page.locator("//span[text()='保存']/parent::div/parent::div/parent::div/parent::div").click()
            cscec.clickDigalog(page,"提示")
            cscec.getVisible(page,"//span[text()='提交']").click()
            cscec.clickDigalog(page,"提示")
            cscec.closeTab(page)
            theWs.Cells(1,2).Value=theWs.Cells(1,2).Value+1
def excelToTamplate():
    filePaths=filedialog.askopenfilenames()
    table=excel.myBook().sheet("研发费用").table("表1")
    k=2
    for filePath in filePaths:
        wb=openpyxl.load_workbook(filePath,data_only=True)
        for sheet in wb.sheetnames:
            if "材料费用归集表" in sheet:
                ws = wb[sheet]
                month=ws["f4"].value
                rdCode=ws["c5"].value
                rdName=ws["f5"].value
                projectName=ws["f6"].value
                for row in ws.iter_rows(min_row=8,max_row=ws.max_row):
                    if type(row[0].value)==str and r"合计" in row[0].value:
                        amount=row[6].value
                table.setValue(k,"期间",month)
                table.setValue(k,"课题编码",rdCode)
                table.setValue(k,"课题名称",rdName)
                table.setValue(k,"项目名称",projectName)
                table.setValue(k,"金额",amount)
                table.setValue(k,"附件地址",filePath)
                k=k+1
        wb.close()
