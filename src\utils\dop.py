from playwright.sync_api import Play<PERSON>, sync_playwright,Page
import re
import src.utils.cscec as cscec
def goFund(page:Page,ifColse:bool):
    page.locator("//span[@title='资金管理']").click()
    page.locator("//td[normalize-space()='1']").click()
    if ifColse:
        page.locator("//div[text()='当前项目：']/following-sibling::div[1]/i").click() 

def checkStr(s):
    if type(s)==float or type(s)==int :
        return str(int(s))
    else:
        return s
def fundChooseProject(page:Page,project_code:str):
    page.locator("//div[text()='当前项目：']/following-sibling::div[1]/div[1]").click()
    page.locator("//input[@placeholder='请输入项目编码']").fill(checkStr(project_code))
    page.locator("//*[@id='portal-form-list-container']/div[3]/div/div/div[2]/div/div[2]/div[2]/div/div/div/div[1]/div[5]/button").click()
    page.locator("//td[normalize-space()='"+checkStr(project_code)+"']").click()

def goFundContract(page:Page):
    page.locator("//span[@title='资金收支管理']").click()
    page.locator("//a[@title='债务管理台账']/parent::li").click()
    page.locator("//li[text()='分供方合同']/following-sibling::li[1]").click() #因为分供方合同维护左右有空格

def pageCount(page:Page,count):
    page.locator("//div[@title='20 条/页']").click()
    page.locator(f"//li[text()='{count} 条/页']").click()

def getPageCount(page:Page):
    s=cscec.getVisible(page,"//li[@title='上一页']/preceding::li[contains(text(),'共')]").text_content()
    print(s)
    return int(re.findall("\d+",s)[0])
    

def getChoosePanel(page:Page):
    tyrCount=5
    while tyrCount>0:
        try:
            page.locator("//i[@class='icon aufontAll h-icon-all-filter-o']").click()
            if cscec.getVisible(page,"//div[@class='query-form']",timeout=30)!=None:
                tyrCount=-1
        except Exception as e:
            print(e)
            tyrCount=tyrCount-1
