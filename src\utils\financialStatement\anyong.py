from tkinter import Entry, StringVar, Button, Label, Text, Scrollbar, END, W, E, N, S, Tk, NORMAL, DISABLED
from tkinter import ttk
from os import path, remove
import tkinter.filedialog
import sys
import threading
import os
import pandas as pd
import xlwings as xw
from openpyxl.utils import get_column_letter, column_index_from_string
from datetime import datetime

def get_desc(i, configWS, inputSheet, descrip, rowNum, colNum):
    if 'XX' in descrip:
        reStr_colNum = column_index_from_string(configWS[(i, 4)].value) - 1
        replace_string = inputSheet[(rowNum, reStr_colNum)].value.replace(' ', '').replace('\t', '')
        new_descrip = descrip.replace('XX', replace_string)
        return new_descrip
    if '+' in str(configWS[(i, 5)].value):
        reStr_rowList = str(configWS[(i, 5)].value).split('+')
        replace_string = ''
        for idx in reStr_rowList:
            reStr_rowNum = int(idx) - 1
            if replace_string != '':
                replace_string += '-' + merged_value
            if not replace_string == '':
                pass
            replace_string += merged_value
            replace_string = replace_string.replace(' ', '').replace('\t', '')
            new_descrip = descrip.replace('XX', replace_string)
            return new_descrip
            reStr_rowNum = int(configWS[(i, 5)].value) - 1
            replace_string = inputSheet[(reStr_rowNum, colNum)].value.replace(' ', '').replace('\t', '')
            new_descrip = descrip.replace('XX', replace_string)
            return new_descrip
            return None
            return None
            return descrip


def get_desc_adv(i, configWS, inputSheet, descrip, rowNum, colNum, row_diff, col_diff):
    if 'XX' in descrip and descrip.count('XX') == 1:
        reStr_colNum = column_index_from_string(configWS[(i, 4)].value) - 1
        replace_string = str(merged_value).replace(' ', '').replace('\t', '')
        new_descrip = descrip.replace('XX', replace_string)
        return new_descrip
    if '+' in str(configWS[(i, 5)].value):
        reStr_rowList = str(configWS[(i, 5)].value).split('+')
        replace_string = ''
        for idx in reStr_rowList:
            reStr_rowNum = int(idx) - 1
            if replace_string != '':
                replace_string += '-' + merged_value
            if not replace_string == '':
                pass
            replace_string += merged_value
            replace_string = replace_string.replace(' ', '').replace('\t', '')
            new_descrip = descrip.replace('XX', replace_string)
            return new_descrip
            reStr_rowNum = int(configWS[(i, 5)].value) - 1
    replace_string = str(merged_value).replace(' ', '').replace('\t', '')
    new_descrip = descrip.replace('XX', replace_string)
    return new_descrip
    reStr_rowNum = int(rowNum) + int(row_diff)
    reStr_colNum = int(colNum) + int(col_diff)
    replace_string = str(merged_value).replace(' ', '').replace('\t', '')
    new_descrip = descrip.replace('XX', replace_string)
    return new_descrip
    print(f'''{configWS.name}绗瑊i}琛屽嚭鐜版柊閫昏緫锛岃鑱旂郴DA...''')
    return None
    if 'XX' in descrip and descrip.count('XX') == 2:
        reStr_colNum = column_index_from_string(configWS[(i, 4)].value) - 1
        reStr_colNum2 = column_index_from_string(configWS[(i, 6)].value) - 1
        index = descrip.find('XX')
        new_descrip = index + 2 + None
        new_descrip_f = new_descrip.replace('XX', replace_string2)
        return new_descrip_f
    reStr_rowNum = int(configWS[(i, 5)].value) - 1
    reStr_rowNum2 = int(configWS[(i, 7)].value) - 1
    index = descrip.find('XX')
    new_descrip = index + 2 + None
    new_descrip_f = new_descrip.replace('XX', replace_string2)
    return new_descrip_f
    reStr_rowNum = int(configWS[(i, 5)].value) - 1
    reStr_colNum = column_index_from_string(configWS[(i, 6)].value) - 1
    index = descrip.find('XX')
    new_descrip = index + 2 + None
    new_descrip_f = new_descrip.replace('XX', replace_string2)
    return new_descrip_f
    print(f'''{configWS.name}绗瑊i + 1}琛屽嚭鐜版柊閫昏緫锛岃鑱旂郴DA鎴栦慨鏀笴onfig琛?Unsupported opcode: POP_JUMP_IF_NOT_NONE (238)
