from playwright.sync_api import Play<PERSON>, sync_playwright,Page
import src.utils.cscec as cscec
import src.utils.Excel.excel as excel

def enterProject(page:Page,projectCode:str):
    page.locator("//span[@title='数据业务']").click()
    page.locator("//span[@title='主数据']").click()
    page.locator("//span[@title='项目主数据']").click()
    page.locator("//span[@title='项目主数据查询']").click()
    page.locator("//th[@title='项目编码']//input").fill(projectCode)
    page.locator("//button/span[text()='查询']").click()
    page.locator(f"//span[@title='{projectCode}']/parent::div/parent::td/preceding::td[4]//input[@type='checkbox']").click()
    page.locator("//button/span[text()='项目用户']").click()
    page.locator("//span[text()='用户申请']").click()

def chooseUser(page:Page,name:str,nameCode:str=None):
    s="//span[text()='新增列表']/parent::div"
    page.locator(s+"//button/span[text()='新增']").click()
    s="//div[text()='选择用户']/parent::div/parent::div"
    if nameCode!=None:
        page.locator(s+"//th[@title='用户编码']//input").fill(nameCode)
    page.locator(s+"//th[@title='用户名称']//input").fill(name)
    page.locator(s+"//th[@title='所属单位名称']//input").fill("中建三局安装工程有限公司南方大区")
    page.locator(s+"//button[@title='刷新']").click()
    page.locator(f"//td[@title='{name}']/preceding::td[2]//input[@type='checkbox']").click()
    page.locator(s+"//span[text()='确定']").click()

def main():
    with sync_playwright() as playwright:
        browser = playwright.chromium.connect_over_cdp("http://localhost:9222")
        default_context = browser.contexts[0]
        page = cscec.switch_to_page(default_context,"主数据管理")
        page.reload()
        page.locator("//*[@id='弹窗关闭']").click()
        ws=excel.myBook().sheet("人员清单")
        table=ws.table("表1")
        startCount=int(ws.Cells(1,2).Value)+1
        enterProject(page,ws.Cells(2,2).Value)
        for i in range(startCount,table.MaxRow+1):
            if table.getValue(i,"是否新增")=="是":
                name=table.getValue(i,"姓名")
                nameCode=table.getValue(i,"人员编码")
                chooseUser(page,name,nameCode)
  