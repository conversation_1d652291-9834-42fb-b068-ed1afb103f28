import flet as ft
import os
import src.base.settings as settings
import src.Gui.callProcess as callF
from tkinter import filedialog
import src.base.cache as cache


class FilePicker(ft.UserControl):
    def __init__(self):
        super().__init__()
        self.file_path = ""
        
    def pick_files_dialog(self, e):
        # Create and hide tkinter root window
        # Open file picker dialog
        file_path = filedialog.askopenfilename()
        if file_path:
            self.file_path = file_path
            self.file_path_text.value = file_path
            cache.wirteLastPath(file_path)
            cache.theLastSheetPath = file_path
            self.update()

    def build(self):
        self.file_path_text = ft.TextField(
            label="期初报表路径",
            read_only=True,
            width=400,
            value=cache.theLastSheetPath
        )
        
        return ft.Container(
            content=ft.Column(
                controls=[
                    ft.Row(
                        controls=[
                            self.file_path_text,
                            ft.ElevatedButton(
                                "选择期初报表",
                                on_click=self.pick_files_dialog,
                                style=ft.ButtonStyle(
                                    shape=ft.RoundedRectangleBorder(radius=10),
                                    padding=15,
                                ),
                            ),
                        ]
                    ),
                    ft.ElevatedButton(
                        "补全期初数",
                        on_click=lambda e: callF.thisProcess.run({"功能": "补全期初数"}),
                        style=ft.ButtonStyle(
                            shape=ft.RoundedRectangleBorder(radius=10),
                            padding=15,
                        ),
                    )
                ]
            ),
            padding=15,
            border_radius=10,
            bgcolor=ft.colors.SURFACE_VARIANT,
            margin=ft.margin.only(bottom=10),
        )

class getData(ft.Column):
    def __init__(self):
        super().__init__()
        self.visible = True
        self.expand = True
        self.spacing = 10
        
        # 创建打开结账文件按钮
        self.open_file_button = ft.ElevatedButton(
            text="打开数据展示器",
            icon=ft.icons.FILE_OPEN,
            #on_click=lambda e: os.system(f'start chrome 127.0.0.1:8000'),  # 替换为您的实际URL
            url="http://127.0.0.1:8000",
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
                text_style=ft.TextStyle(size=14),
            ),
        )
        
        # 创建快速取数按钮
        self.quick_data_button = ft.ElevatedButton(
            "查询后台结账模板",
            icon=ft.icons.DOWNLOAD,
            on_click=lambda e: callF.thisProcess.run({"功能": "查询后台结账模板"}),
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
                text_style=ft.TextStyle(size=14),
            ),
        )

        self.update_data_button = ft.ElevatedButton(
            "更新后台结账模板",
            icon=ft.icons.DOWNLOAD,
            on_click=lambda e: callF.thisProcess.run({"功能": "更新后台结账模板"}),
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
                text_style=ft.TextStyle(size=14),
            ),
        )
        
        # 创建卡片式布局
        self.card = ft.Container(
            content=ft.Column(
                [
                    ft.Row(
                        [
                            ft.Text("数据更新", size=16, weight=ft.FontWeight.BOLD),
                        ],
                        alignment=ft.MainAxisAlignment.START,
                    ),
                    ft.Row(
                        [
                            self.open_file_button,
                            self.quick_data_button,
                            self.update_data_button
                        ], 
                        alignment=ft.MainAxisAlignment.SPACE_AROUND,
                        spacing=10,
                    ),
                ],
                spacing=10,
                alignment=ft.MainAxisAlignment.CENTER,
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            ),
            padding=15,
            border_radius=10,
            bgcolor=ft.colors.SURFACE_VARIANT,
            margin=ft.margin.only(bottom=10),
        )

        self.controls = [self.card]

class getData_cumulativePosting(ft.Column):
    def __init__(self):
        super().__init__()
        self.visible = True
        self.expand = True
        self.spacing = 10
        
        # 创建年份选择框
        current_year = int(settings.YEAR)
        years = [str(year) for year in range(current_year-5, current_year+6)]
        self.startDate = ft.Dropdown(
            label="年份",
            value=settings.YEAR,
            options=[ft.dropdown.Option(year) for year in years],
            width=120,
        )
        
        # 创建月份选择框
        months = [str(month).zfill(2) for month in range(1, 13)]
        self.endDate = ft.Dropdown(
            label="月份",
            value=settings.MONTH,
            options=[ft.dropdown.Option(month) for month in months],
            width=120,
        )

        self.update_data_button = ft.ElevatedButton(
            "累计过账数据获取",
            icon=ft.icons.DOWNLOAD,
            on_click=lambda e: callF.thisProcess.run({"功能": "累计过账数据获取", "参数": [self.startDate.value, self.endDate.value]}),
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
                text_style=ft.TextStyle(size=14),
            ),
        )

        self.confirmation_button = ft.ElevatedButton(
            "快速内部确认",
            icon=ft.icons.DOWNLOAD,
            on_click=lambda e: callF.thisProcess.run({"功能": "快速内部确认", "参数": [self.startDate.value, self.endDate.value]}),
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
                text_style=ft.TextStyle(size=14),
            ),
        )
        
        # 创建卡片式布局
        self.card = ft.Container(
            content=ft.Column(
                [
                    ft.Row(
                        [
                            ft.Text("累计过账数据获取及内部确认", size=16, weight=ft.FontWeight.BOLD),
                        ],
                        alignment=ft.MainAxisAlignment.START,
                    ),
                    ft.Row(
                        [
                            self.startDate,
                            self.endDate,
                            self.update_data_button,
                            self.confirmation_button
                        ], 
                        alignment=ft.MainAxisAlignment.SPACE_AROUND,
                        spacing=10,
                    ),
                ],
                spacing=10,
                alignment=ft.MainAxisAlignment.CENTER,
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            ),
            padding=15,
            border_radius=10,
            bgcolor=ft.colors.SURFACE_VARIANT,
            margin=ft.margin.only(bottom=10),
        )

        self.controls = [self.card]

def returnCosle1():
    # 创建第一行按钮
    safety_fee_btn = ft.ElevatedButton(
        "安全费计提",
        on_click=lambda e: callF.thisProcess.run({"功能": "安全费计提"}),
        style=ft.ButtonStyle(
            shape=ft.RoundedRectangleBorder(radius=10),
            padding=15,
        ),
    )
    
    # 创建第二行按钮
    estimate_btn = ft.ElevatedButton(
        "自动生成暂估单据",
        on_click=lambda e: callF.thisProcess.run({"功能": "自动生成暂估单据"}),
        style=ft.ButtonStyle(
            shape=ft.RoundedRectangleBorder(radius=10),
            padding=15,
        ),
    )
    
    # 创建第三行按钮
    sap_income_btn = ft.ElevatedButton(
        "SAP收入测算",
        on_click=lambda e: callF.thisProcess.run({"功能": "SAP收入测算"}),
        style=ft.ButtonStyle(
            shape=ft.RoundedRectangleBorder(radius=10),
            padding=15,
        ),
    )
    
    sap_confirm_btn = ft.ElevatedButton(
        "SAP匡算确认",
        on_click=lambda e: callF.thisProcess.run({"功能": "SAP匡算确认"}),
        style=ft.ButtonStyle(
            shape=ft.RoundedRectangleBorder(radius=10),
            padding=15,
        ),
    )
    
    platform_estimate_btn = ft.ElevatedButton(
        "生成中台匡算",
        on_click=lambda e: callF.thisProcess.run({"功能": "生成中台匡算"}),
        style=ft.ButtonStyle(
            shape=ft.RoundedRectangleBorder(radius=10),
            padding=15,
        ),
    )
    
    final_voucher_btn = ft.ElevatedButton(
        "最终制证",
        on_click=lambda e: callF.thisProcess.run({"功能": "最终制证"}),
        style=ft.ButtonStyle(
            shape=ft.RoundedRectangleBorder(radius=10),
            padding=15,
        ),
    )
    
    return ft.Container(
        content=ft.Column([
            ft.Row(
                [
                    ft.Text("结账处理", size=16, weight=ft.FontWeight.BOLD),
                ],
                alignment=ft.MainAxisAlignment.START,
            ),
            # 第一行
            ft.Container(
                content=safety_fee_btn,
                alignment=ft.alignment.center,
            ),
            ft.Divider(height=1, color=ft.colors.with_opacity(0.2, ft.colors.PRIMARY)),
            # 第二行
            ft.Container(
                content=estimate_btn,
                alignment=ft.alignment.center,
            ),
            ft.Divider(height=1, color=ft.colors.with_opacity(0.2, ft.colors.PRIMARY)),
            # 第三行
            ft.Row(
                [
                    sap_income_btn,
                    sap_confirm_btn,
                    platform_estimate_btn,
                    final_voucher_btn,
                ],
                alignment=ft.MainAxisAlignment.SPACE_AROUND,
                vertical_alignment=ft.CrossAxisAlignment.CENTER,
            ),
        ],
        spacing=10,
        ),
        padding=15,
        border_radius=10,
        bgcolor=ft.colors.SURFACE_VARIANT,
        margin=ft.margin.only(bottom=10),
    )

class closeTabView(ft.Column):
    def __init__(self):
        super().__init__()
        self.visible = True
        self.expand = True
        self.spacing = 10
        
        self.mainRegional = ft.Column(
            controls=[],
            alignment=ft.MainAxisAlignment.CENTER,
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            spacing=10,
        )
        
        self.controls = [self.mainRegional]
        self.mainRegional.controls = [
            getData(),
            returnCosle1(),
            getData_cumulativePosting()
        ]
    