import os
import sys

def initRuntimeEnvironment(startup_script):
    """初始化运行环境。startup_script: 启动脚本路径"""

    import site
    # 定义一个最简单的消息弹窗
    # 初始化工作目录和Python搜索路径
    script = os.path.abspath(startup_script)  # 启动脚本.py的路径
    home = os.path.dirname(script)  # 工作目录
    os.chdir(home)  # 重新设定工作目录（不在最顶层，而在UmiOCR-data文件夹下）
    for n in ['.', '.site-packages']:  # 将模块目录添加到 Python 搜索路径中
        path = os.path.abspath(os.path.join(home, n))
        if os.path.exists(path):
            site.addsitedir(path)


initRuntimeEnvironment(__file__)  # 初始化运行环境

sys.path.append("..") 
from playwright.sync_api import Playwright, sync_playwright
import win32com.client
import time
import pythoncom
import excelto


def switch_to_page(context, title=None, url=None):
    """切换到指定title 名称 或 url 的 标签页"""
    for item_page in context.pages:
        if title:
            if title in item_page.title():
                # 激活当前选项卡
                item_page.bring_to_front()
                return item_page
        elif url:
            if url in item_page.url:
                # 激活当前选项卡
                item_page.bring_to_front()
                return item_page
    else:
        print("not found title or url")
    return context.pages[0]

def autofill():
    try:
        et = win32com.client.Dispatch("Excel.Application")
        wb=et.ActiveWorkbook
        ws=wb.Worksheets("确权收款台账")
    except:
        et = win32com.client.Dispatch("Ket.Application")
        wb=et.ActiveWorkbook
        ws=wb.Worksheets("确权收款台账")
    table=excelto.table(wb,"确权收款台账","表确权收款")
    start_number=int(ws.Cells(1,2).Value)
    with sync_playwright() as playwright:   
        browser = playwright.chromium.connect_over_cdp("http://localhost:9222")
        default_context = browser.contexts[0]
        page = default_context.pages[0]
        page = switch_to_page(default_context, title="中建三局", url=None)      
        for i in range(start_number+1,table.max_row+1):
            print("开始序号"+str(i-1)+"行")
            if round(table.value(i,"确权差额"),2)!=0 and table.value(i,"商务无法取数")=="是":
                page.click("//button[@class='ant-btn ant-btn-primary list-action-add']") #点击新增表
                frame=page.frame_locator("//*[@id='portal-form-list-container']/div[4]/iframe")
                frame.locator("//div[@id='externalColl']//div[@class='field__control']//div//div[@class='relevanceform']//div//input[@placeholder='请选择']").click()
                frame.locator("//div[@class='ant-col ant-col-12']//div[@id='projectName']//div[@class='field__control']//div//div[@class='text']//div//input[@placeholder='请输入']").click()
                frame.locator("//div[@class='ant-col ant-col-12']//div[@id='projectName']//div[@class='field__control']//div//div[@class='text']//div//input[@placeholder='请输入']").fill(table.value(i,"财商名称"))
                frame.locator("//div[@class='ant-modal-body']//button[2]").click()
                #frame.get_by_text(ws.Cells(i,4).Value).first.click()
                frame.locator("//*[@id='list-selector']/div[2]/div[2]/div[1]/div[2]/div[1]/div[2]/div/div[1]/div/div/div[text()='"+table.value(i,"财商名称")+"']").click()
                frame.locator("//div[@class='ant-modal-footer']//button[2]").click()
                time.sleep(3)       
                frame.locator("//div[@id='auditTimeOwner']//div[@class='field__control']//div//div[@class='date']//div//span[@class='ant-calendar-picker']//div//input[@placeholder='请选择']").click()
                if table.value(i,"审核日期差额")!=0:
                    for j in range(1,int(table.value(i,"审核日期差额"))+1):
                        frame.locator("//a[@title='上个月 (翻页上键)']").click()
                    frame.locator("//td[@title='"+table.value(i,"审核标准日期")+"']").click()
                else:
                    frame.locator("//td[@title='"+table.value(i,"审核标准日期")+"']").click()

                frame.locator("//div[@id='payCycleRece']//div[@class='field__control']//div//input[@placeholder='请输入']").click()
                frame.locator("//div[@id='payCycleRece']//div[@class='field__control']//div//input[@placeholder='请输入']").fill("1") #总收款


                frame.get_by_text("预收款").click()
                #frame.locator("//div[@class='ant-select ant-select-enabled ant-select-allow-clear ant-select-focused']//div[@class='ant-select-selection__rendered']").click()
                frame.get_by_text("进度款").click()
                yingshou=float(frame.locator("//*[@id='accumuRece']/div[2]/div/div/div/input").input_value())
                frame.locator("//div[@id='selfPartOwner']//div[@class='field__control']//div//input[@placeholder='请输入']").click()
                frame.locator("//div[@id='selfPartOwner']//div[@class='field__control']//div//input[@placeholder='请输入']").fill(str(table.value(i,"按比例应收款")-yingshou)) #自施部分应收款
                frame.locator("//span[text()='保 存']/parent::button").click()

            ws.Cells(1,2).Value=ws.Cells(1,2).Value+1
        print("完成任务")

import Gui.ui as ui
if __name__=='__main__':
    ui.runAuto(autofill)  