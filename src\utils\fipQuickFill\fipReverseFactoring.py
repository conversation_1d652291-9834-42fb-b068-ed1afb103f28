
import src.utils.Browser.Browser as browser
import src.utils.cscec as cscec
from src.utils.DB.midIntrSQLiteDB import excelDB
import time
def main():
    db=excelDB()
    df=db.getDataframe("反向保理提单")
    page = browser.myBrowser("cscec").page
    for i,row in df.iterrows():
        if row["是否"]=="是":
            cscec.changeProjectCscec(page,row["组织机构"],row["项目名称"])
            cscec.toFunction(page,"报账系统","物资及资产","出库汇总单-施工行业")
            cscec.chooseIfPaper(page,True)
            table=cscec.cscecTable(page,"存货类型编号")
            table.clickInputQuery2(1,"*存货类型编号")
            cscec.dialogInput(page,"1001012")
            table.fillInput(1,"*存货名称/备注","机电材料及设备")
            table.clickInputQuery2(1,"*计量单位")
            cscec.dialogInput(page,"批")
            if s=="研发":
                cscec.fillLalbel_input(page,"*事       由：",row["研发事由"])
                table.fillInput(1,"*出库单价",row["研发金额"])
                table.fillInput(1,"*物资领用数量","1")
                table.clickInputQuery2(1,"*物资用途") 
                cscec.dialogInput(page,"1000040") #研发支出，其他材料
                time.sleep(1)
                table.reIndex()
                table.clickInputQuery2(1,"*科研课题")
                rdCode=row["课题编码"] if type(row["课题编码"])==str else str(int(row["课题编码"]))
                cscec.dialogInput(page,rdCode) #选择科研课题
            else:
                cscec.fillLalbel_input(page,"*事       由：",row["退库事由"])
                table.fillInput(1,"*出库单价",row["退库金额"])
                table.fillInput(1,"*物资领用数量","1")
                table.clickInputQuery2(1,"*物资用途") 
                cscec.dialogInput(page,"110")  #工程实体耗用
            cscec.uploadAttachment(page,row["附件地址"])
            page.locator("//span[text()='保存']/parent::div/parent::div/parent::div/parent::div").click()
            cscec.clickDigalog(page,"提示")
            cscec.getVisible(page,"//span[text()='提交']").click()
            cscec.clickDigalog(page,"提示")
            cscec.closeTab(page)
def queryData():
    db=excelDB()
    headers = ['序号','是否','单位','项目','事由','存货类型','金额','用途' ]
    datarows=[1,'是',"中建","第一项目","物资出库","机电材料及设备","1000.00","研发支出"]
    db.queryData("反向保理提单",headers,datarows)
    db.close()

def writeData():
    db=excelDB()
    db.writeExcel("反向保理提单")

