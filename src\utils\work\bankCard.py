# python --version 2.7.18
# -*- coding: utf-8  -*-
from __future__ import print_function

import uuid, ssl, hmac, base64, hashlib, requests
from datetime import datetime as pydatetime

# 云市场分配的密钥Id
secretId = "xxxx"
# 云市场分配的密钥Key
secretKey = "xxxx"

# 签名
datetime = pydatetime.utcnow().strftime('%a, %d %b %Y %H:%M:%S GMT')
signStr = "x-date: %s" % (datetime)
sign = base64.b64encode(hmac.new(secretKey.encode('utf-8'), signStr.encode('utf-8'), hashlib.sha1).digest())
auth = '{"id": "%s", "x-date": "%s" , "signature": "%s"}' % (secretId, datetime, sign.decode('utf-8'))

# 请求方法
method = 'POST'
# 请求头
headers = {
    'request-id': str(uuid.uuid1()),
    'Authorization': auth,
    'Content-Type': 'application/x-www-form-urlencoded'
}

# 查询参数
queryParams = {}

# body参数（POST方法下存在）
bodyParams = {
   "bankcardno": "",
   "bankname": "",
   "city": "",
   "keyword": "",
   "pageindex": "",
   "province": ""
}

# url参数拼接
url = 'https://ap-guangzhou.cloudmarket-apigw.com/service-o5slxyum/1b003'

# 使用requests发送请求
response = requests.post(
    url,
    params=queryParams,
    headers=headers,
    data=bodyParams,
    verify=False  # 禁用SSL验证，与原代码行为一致
)

if response.text:
    print(response.text)