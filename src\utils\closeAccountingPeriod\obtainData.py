import src.utils.DB.readtxttolist as readtxttolist
import src.utils.Excel.excel as excel
import src.utils.Excel.openpyxlExcel as openpyxlExcel
import pandas as pd
import duckdb
import src.base.settings as settings
import src.utils.DB.Sql as Sql
import src.utils.DB.outputSQL as outputSQL
import os
from datetime import datetime, timedelta
from src.utils.DB.mainDB import mainDB
from src.utils.DB.midIntrSQLiteDB import excelDB
def getCloseDataByBalance():
    df0=mainDB().getCloseDataByBalance()

def queryData():
    db=excelDB()
    
    db.queryData(tableName=["独立结账模板收入成本","独立结账模板安全费","收入成本测算","批量暂估"],fileName="结账数据总")
    db.close()

def writeData():
    db=excelDB()
    db.writeExcel(tableName=["独立结账模板收入成本","独立结账模板安全费","收入成本测算","批量暂估"])
    db.close()
    
            
