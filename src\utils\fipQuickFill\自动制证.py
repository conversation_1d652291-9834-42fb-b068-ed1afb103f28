
import sys
import os

from playwright.sync_api import Playwright, sync_playwright,Page
import time
import src.utils.cscec as cscec
import src.utils.fipQuickFill.downloadAttachment as downloadAttachment
import datetime
import src.base.settings as settings
from src.utils.DB.midIntrSQLiteDB import saveAutoCertification
from src.utils.DB.configDB import configDB




def recurringTable(page:Page,projectDict:dict,downloadDict:dict,typeDict:dict,lock=None,duplicateDict=None):
    cscec.getVisible(page,"//div[text()='每页']/following-sibling::input[1]").fill("500")
    cscec.getVisible(page,"//div[text()='每页']/preceding-sibling::div[6]").click()
    time.sleep(5)
    table=cscec.getTr(page,"组织机构")
    maxRow=table.count()
    '''
    for i in range(maxRow):
        processCodeList.append(table.nth(maxRow-1-i).locator("//td[5]/div").text_content()) #先循环一轮，获取所有单据号，这样是避免自动刷新导致顺序错乱 保留措施
    '''
    for i in range(maxRow):
        table.nth(maxRow-1-i).locator("//td[3]/div").click()
        businessType=table.nth(maxRow-1-i).locator("//td[3]/div").text_content()
        processCode=table.nth(maxRow-1-i).locator("//td[5]/div").text_content() #单据号
        projectName=table.nth(maxRow-1-i).locator("//td[11]/div").text_content()
        organization=table.nth(maxRow-1-i).locator("//td[2]/div").text_content()
        dateOfBillOfLading=table.nth(maxRow-1-i).locator("//td[4]/div").text_content()
        businessReasons=table.nth(maxRow-1-i).locator("//td[6]/div").text_content()
        amount=table.nth(maxRow-1-i).locator("//td[7]/div").text_content()

        print(processCode+"开始")
        if lock!=None:
            with lock:
                if duplicateDict.get(processCode,"否")=="否":
                    ifCountinue=True
                    duplicateDict[processCode]="是"
                else:
                    ifCountinue=False
        else:
            ifCountinue=True
        if ifCountinue:
            if typeDict.get(businessType)=="是" and projectDict.get(projectName,"否")=="否" and projectDict.get(organization,"否")=="否" : 
                if downloadDict.get(businessType)=="是":
                    current_date = datetime.date.today()
                    current_formatted_date = current_date.strftime("%Y-%m-%d")
                    projectName=projectName.replace("/","") #去除特殊字符
                    projectName=projectName.replace("\\","")
                    projectName=projectName.replace("|","")
                    path=settings.PATH_FIP_DOWNLOAD+"/"+current_formatted_date+"/"+organization+"/"+projectName+"/"
                    path2=path+processCode+"a流程单.pdf"
                    cscec.getVisible(page,"//span[text()='查看单据']/parent::div").click()
                    downloadAttachment.download(page,processCode,path)
                table2=cscec.getTr(page,"付款日期")
                table2.nth(0).locator("//td[1]/div").click()
                payDate=table2.nth(0).locator("//td[1]/div").text_content()
                determineWhetherThereIsAPaymentDate="否"
                if payDate[0]!="2":
                    determineWhetherThereIsAPaymentDate="是"
                #table.nth(maxRow-1-i).locator("//td[3]/div").click()
                table.locator(f"//td[5]/div[text()='{processCode}']").click() #避免不相符情况
                cscec.getVisible(page,"//span[text()='制 证']/parent::div").click()
                if "发票开具" in businessType:
                    time.sleep(1)
                    cscec.getVisible(page,"//*[text()='系统提示']//parent::div/parent::div/parent::div//div[text()='确定']").click()
                page.locator("//div[contains(text(),'借方金额')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table[1]/tbody[2]/tr[2]/td[1]").click()
                #需要等待凭证内容加载出来，不然日期改了又会改回去,长条加速

                if changeDate(page,payDate,determineWhetherThereIsAPaymentDate):
                    voucherCanBePrepared=True
                else:
                    voucherCanBePrepared=False

                voucherDate=cscec.getVisible(page,"//input[@id='FormDateFieldRQ-input']").get_attribute("value") #凭证日期

                if voucherCanBePrepared:
                    try:
                        if businessType in ["支付前期挂账款项","付款失败重新支付申请"]:
                            supplementCashFlow(page,"A2499")
                        
                        if businessType in ["收款确认单"] and "退票" in businessReasons:
                            supplementCashFlow(page,"A1399")
                        
                        if businessType in ["反向保理到期付款"]:
                            supplementCashFlow(page,"A2499",False)
                        
                        if businessType in ["内行资金划转确认单"]:
                            supplementCashFlow(page,"A1399",False)
                        
                        if businessType in ["差旅费报销","通用报销单","对外成本费用付款申请"]:
                            elementWelfare=page.locator("//div[text()='应付职工薪酬\职工福利费']/parent::td")
                            if elementWelfare.count()>0:
                                if "探亲" in businessReasons:
                                    supplementExpenseItem(page,"00400090",elementWelfare) #员工医药费
                                elif "食堂" in businessReasons:
                                    supplementExpenseItem(page,"00400020",elementWelfare)
                                elif "医药" in businessReasons:
                                    supplementExpenseItem(page,"00400010",elementWelfare)
                                else:
                                    supplementExpenseItem(page,"00409999",elementWelfare)
                            
                            elementSafe=page.locator("//div[text()='专项储备\安全生产费\发生数']/parent::td")
                            if elementSafe.count()>0:
                                if "CI" in businessReasons or "ci" in businessReasons:
                                    supplementExpenseItem(page,"00300160",elementSafe) #CI费用广告
                                elif "奖金" in businessReasons:
                                    supplementExpenseItem(page,"00300070",elementSafe) #安全奖励
                                elif "培训" in businessReasons:
                                    supplementExpenseItem(page,"00300150",elementSafe) #培训
                                elif "会议" in businessReasons:
                                    supplementExpenseItem(page,"00300010",elementSafe) #培训2
                                else:
                                    supplementExpenseItem(page,"00300080",elementSafe)
                        cscec.getVisible(page,"//div[text()='保存凭证']/parent::div").click()
                        msg=cscec.locatorDigalog(page,"提示").text_content()
                        if "后台服务报错" in msg:
                            loadMsg=msg
                            cscec.clickDigalog(page,"提示")
                            cscec.getVisible(page,"//div[text()='退出']/parent::div").click()
                        else:
                            if "附件张数是" in msg:
                                cscec.clickDigalog(page,"提示") #注意，这里是保存提示，不是系统提示,多一个提示
                            time.sleep(0.1) #避免系统销毁过慢
                            temporaryVoucherNumber=page.locator("//label[text()='凭证编号:']/parent::div/following-sibling::div[1]/label").text_content()
                            if downloadDict.get(businessType)=="是":
                                    downloadAttachment.pdf_wirte(path2,temporaryVoucherNumber)
                            cscec.clickDigalog(page,"提示") #保存凭证提示
                            cscec.clickDigalog(page,"提示")
                            loadMsg=temporaryVoucherNumber
                        saveResults(processCode,loadMsg,organization,businessType,dateOfBillOfLading,businessReasons,amount,payDate,voucherDate,projectName)
                    except Exception as e:
                        cscec.getVisible(page,"//div[text()='退出']/parent::div").click()
                        saveResults(processCode,"程序异常",organization,businessType,dateOfBillOfLading,businessReasons,amount,payDate,voucherDate,projectName)
                else:
                    cscec.getVisible(page,"//div[text()='退出']/parent::div").click()
                    saveResults(processCode,"失败，无凭证号",organization,businessType,dateOfBillOfLading,businessReasons,amount,payDate,voucherDate,projectName)
            else:
                saveResults(processCode,"跳过该项目或业务类型凭证",organization,businessType,dateOfBillOfLading,businessReasons,amount,"","",projectName)



def saveResults(processCode,temporaryVoucherNumber,organization,businessType,dateOfBillOfLading,businessReasons,amount,paymentDate,voucherDate,projectName):
    
    current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    inserData=(current_time,processCode,organization,businessType,dateOfBillOfLading,businessReasons,amount,paymentDate,voucherDate,projectName,temporaryVoucherNumber)
    saveAutoCertification().insertData(inserData) #插入数据到表格中，data是一个列表，每个元素是一个元组，元组的元素是表格的


def supplementCashFlow(page:Page,item,netxPage=True): #修复了现金流补充
    def fillItem(item):
        innerCash=page.locator("//div[text()='内部存款\可用存款']/parent::td")
        if innerCash.count()>0:
            for i in range(innerCash.count()):
                if innerCash.nth(i).is_visible():
                    innerCash.nth(i).click()
                    table3=cscec.getTr(page,"现金流量")
                    for i in range(table3.count()):
                        table3.nth(i).locator("//td[3]").dblclick()
                        cscec.getInputAbove(page,table3.nth(i).locator("//td[3]")).fill(item)
                        table3.nth(i).locator("//td[2]").click()
        
        bankCash=page.locator("//div[text()='银行存款\银行存款']/parent::td")
        if bankCash.count()>0:
            for i in range(bankCash.count()):
                if bankCash.nth(i).is_visible():
                    bankCash.nth(i).click()
                    table3=cscec.getTr(page,"现金流量")
                    for i in range(table3.count()):
                        table3.nth(i).locator("//td[2]").dblclick()
                        cscec.getInputAbove(page,table3.nth(i).locator("//td[2]")).fill(item)
                        table3.nth(i).locator("//td[3]").click()
    fillItem(item)
    nextPage=page.locator("//label[text()='下一张']")
    if nextPage.count()>0:
        for i in range(nextPage.count()):
            if nextPage.nth(i).is_visible():
                nextPage.nth(i).click()
                fillItem(item)

def supplementExpenseItem(page,item,welfareEle):
    for i in range(welfareEle.count()):
        welfareEle.nth(i).click()
        table3=cscec.getTr(page,"收支项目")
        for i in range(table3.count()):
            table3.nth(i).locator("//td[3]").dblclick()
            cscec.getInputAbove(page,table3.nth(i).locator("//td[3]")).fill(item)
            table3.nth(i).locator("//td[2]").click()

def dateOfModificationOfVoucher(page,date1,date2):
    js1 = 'jsneed=>{jsneed.removeAttribute("readonly");}'
    jsneed=cscec.getVisible(page,"//input[@id='FormDateFieldRQ-input']")
    jsneed.evaluate(js1)
    jsneed.fill(f"{date1}")
    page.locator("//input[@id='FormDateFieldRQ-input']/following-sibling::div").click()
    page.locator(f"//td[@title='{date2}']").dblclick()

def changeDate(page,payDate,determineWhetherThereIsAPaymentDate):
    current_date = datetime.date.today()
    current_formatted_date = current_date.strftime("%Y-%m-%d")
    if determineWhetherThereIsAPaymentDate=="是":
        my_string=current_formatted_date
    else:
        my_string=payDate
    newData1=my_string[0:5]+str(int(my_string[5:7])+1).rjust(2,'0')+"-02"
    newData2=my_string[0:4]+"年"+str(int(my_string[5:7])+1)+"月"+"1"+"日"

    if my_string[-1]=="1":
        newData3=my_string[0:5]+str(int(my_string[5:7])).rjust(2,'0')+"-02"
    else:
        newData3=my_string[0:5]+str(int(my_string[5:7])).rjust(2,'0')+"-01"

    newData4=my_string[0:4]+"年"+str(int(my_string[5:7]))+"月"+str(int(my_string[8:10]))+"日"

    if my_string[5:7] in ["01","02","03","04","05","07","08","09","10","11"] and int(my_string[8:10])>25:
        dateOfModificationOfVoucher(page,newData1,newData2)
        date_string=cscec.getVisible(page,"//input[@id='FormDateFieldRQ-input']").get_attribute("value")
        if date_string==my_string[0:5]+str(int(my_string[5:7])+1).rjust(2,'0')+"-01":
            return True
        else:
            return False

    else:
        dateOfModificationOfVoucher(page,newData3,newData4)
        date_string=cscec.getVisible(page,"//input[@id='FormDateFieldRQ-input']").get_attribute("value")
        if date_string==my_string:
            return True
        else:
            return False

def threadFunction(cookies,cscecurl,projectDict,downloadDict,typeDict,lock,replaceDict):
    def handle_dialog(dialog):
        """监听后处理"""
        print(dialog.message)
        dialog.dismiss()
    newplaywright = sync_playwright().start()
    newbrowser = newplaywright.chromium.launch(channel="chrome",headless=False)
    newContext = newbrowser.new_context()
    newpage=newContext.new_page()
    newContext.add_cookies(cookies)
    newpage.goto(cscecurl,timeout=120000)
    cscec.toFunction(newpage,"报账系统","财务操作中心","制证及支付")
    tryCount=10
    while tryCount>0:
        try:
            recurringTable(newpage,projectDict,downloadDict,typeDict,lock,replaceDict)
            tryCount=-1
        except Exception as e:
            tryCount=tryCount-1
            print(e)
            newpage.reload()
            newpage.on("dialog", handle_dialog)

def voucherPreparation(useMode):
    def handle_dialog(dialog):
        """监听后处理"""
        print(dialog.message)
        dialog.dismiss()
    import src.utils.Browser.Browser as browser
    projectDict=configDB().excludedProfitCenters
    typeDict=configDB().documentTypes
    downloadDict={}
    import threading
    if useMode=="自动打开浏览器":
        B=browser.myBrowser("cscec")
        page=B.page
        default_context=B.Context
        cookies = default_context.cookies()
        default_context.close()
        cscecurl=f"https://iam.cscec.com/cas/login?service=https%3A%2F%2Ffip.cscec.com%2FOSPPortal%2Fcallback"
        lock= threading.Lock()
        threads = []
        replaceDict={}
        for i in range(5):
            print(i)
            t1 = threading.Thread(target=threadFunction,args=(cookies,cscecurl,projectDict,downloadDict,typeDict,lock,replaceDict))
            t1.setDaemon(True)
            t1.start()
            threads.append(t1) #线程保活，不然自动关闭啦
        for t in threads:
            t.join()
    else:
        with sync_playwright() as playwright:
            browser = playwright.chromium.connect_over_cdp("http://localhost:9222")
            default_context = browser.contexts[0]
            page = cscec.switch_to_page(default_context,"中国建筑司库一体化")
            cscec.toFunction(page,"报账系统","财务操作中心","制证及支付")
            recurringTable(page,projectDict,downloadDict,typeDict)

def main(inputMode):
    voucherPreparation(useMode=inputMode)
