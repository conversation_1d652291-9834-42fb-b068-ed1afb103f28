-- Active: 1745400258741@@127.0.0.1@3306
select * from 一体化合同台账


select 
any_value(WBS元素) as WBS元素,
any_value(WBS元素描述) as WBS元素描述,
any_value(供应商) as 供应商编码,
any_value(供应商名称) as 供应商名称,
any_value(总账科目长文本) as 总账科目长文本,
sum(case when 总账科目长文本 like '其他应付款%保证金%' then 期末余额 else 0 end) as 保证金金额
from 科目余额表第二期
where 总账科目长文本 like '其他应付款%保证金%'
group by WBS元素,供应商,总账科目长文本

select 
any_value(明细帐.利润中心) as 利润中心,
any_value(明细帐.利润中心描述) as 利润中心描述,
any_value(利润中心组描述) as 利润中心组描述,
count(DISTINCT 凭证编号) as 凭证数量
from 明细帐
left join 主数据 on 明细帐.利润中心 = 主数据.利润中心
where 过帐日期 BETWEEN '2025-01-01' and '2025-01-31' 
GROUP BY 明细帐.利润中心 



