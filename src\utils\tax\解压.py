import tkinter as tk
from tkinter import filedialog
import subprocess
import os
import re
import src.base.settings as settings
def extractFolder(file_path):
    zaPath=settings.PATH_THIRD_APP+'/7za.exe'
    print(zaPath)
    folder_path = os.path.dirname(file_path)+'/解压完税确认'
    command = [zaPath, 'x', '-y', file_path, '-o' + folder_path ]
    result = subprocess.run(command, capture_output=True, text=True)
    output = result.stdout.strip()
    file_names = os.listdir(folder_path)
    
    #os.rmdir(folder_path+"/临时文件")
    for file_name in file_names:
        command = [zaPath, 'x', '-y',folder_path+"/" +file_name, '-o' + folder_path+"/临时文件" ]
        result = subprocess.run(command, capture_output=True, text=True)
        output = result.stdout.strip()
        Code=re.findall("WSYJ\d+",file_name)[0]
        print(Code)
        file_names2 = os.listdir(folder_path+"/临时文件")
        count=0
        for file_name2 in file_names2:
            expandName=file_name2.split(".")[-1]
            os.rename(folder_path+"/临时文件/"+file_name2, folder_path+"/"+Code+"+"+str(count)+"."+expandName)
            count+=1
        os.remove(folder_path+"/"+file_name)
    os.rmdir(folder_path+"/临时文件")

def extractFile():
    file_path = filedialog.askopenfilename(title="选择文件夹", filetypes=[("All files", "*.rar")])
    extractFolder(file_path)

