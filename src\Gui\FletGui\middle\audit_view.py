import flet as ft
import src.Gui.callProcess as callF
import src.base.cache as cache
import src.base.settings as settings
import os
from datetime import datetime


# 智能稽核的子窗口
class auditTabView(ft.Column):
    def __init__(self):
        super().__init__()
        self.visible = True
        self.expand = True
        
        # 创建日期选择器
        self.startDate = ft.DatePicker(
            first_date=datetime(2012, 1, 1),
            last_date=datetime(2030, 12, 31),
            on_change=self.start_date_changed
        )
        self.endDate = ft.DatePicker(
            first_date=datetime(2012, 1, 1),
            last_date=datetime(2030, 12, 31),
            on_change=self.end_date_changed
        )
        
        # 创建日期显示按钮
        self.startDateButton = ft.ElevatedButton(
            text=cache.Lastdate,
            icon=ft.icons.CALENDAR_TODAY,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
            ),
            on_click=lambda _: self.startDate.pick_date()
        )
        
        self.endDateButton = ft.ElevatedButton(
            text=cache.Nowdate,
            icon=ft.icons.CALENDAR_TODAY,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
            ),
            on_click=lambda _: self.endDate.pick_date()
        )
        
        self.navigation_bar = ft.NavigationBar(
            destinations=[
                ft.NavigationBarDestination(icon=ft.icons.SWITCH_ACCESS_SHORTCUT_OUTLINED, label="快速稽核"),
            ],
            on_change=lambda e:self.changeMain(e)
        )
        
        self.mainRegional=ft.Column(
            controls=[],
            alignment=ft.MainAxisAlignment.CENTER,
            scroll=ft.ScrollMode.AUTO,
            expand=True,
        )
        
        self.controls=[
            self.navigation_bar,
            self.mainRegional,
            self.startDate,
            self.endDate
        ]
        
        # 设置默认显示科目余额表
        self.mainRegional.controls=[
            ft.Container(
                content=ft.Column(
                    controls=[
                        ft.Text("明细账检查", size=20, weight=ft.FontWeight.BOLD, color=ft.colors.PRIMARY),
                        ft.Row(
                            controls=[
                                ft.Container(
                                    content=ft.Row(
                                        controls=[
                                            ft.Text("开始日期", size=14, weight=ft.FontWeight.BOLD),
                                            self.startDateButton
                                        ],
                                        spacing=10,
                                        alignment=ft.MainAxisAlignment.START,
                                    ),
                                    padding=10,
                                    border_radius=10,
                                    bgcolor=ft.colors.SURFACE_VARIANT,
                                ),
                                ft.Container(
                                    content=ft.Row(
                                        controls=[
                                            ft.Text("结束日期", size=14, weight=ft.FontWeight.BOLD),
                                            self.endDateButton
                                        ],
                                        spacing=10,
                                        alignment=ft.MainAxisAlignment.START,
                                    ),
                                    padding=10,
                                    border_radius=10,
                                    bgcolor=ft.colors.SURFACE_VARIANT,
                                ),
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_AROUND,
                        ),
                        ft.Container(
                            content=ft.ElevatedButton(
                                text="科目余额表检查明细账",
                                icon=ft.icons.SEARCH,
                                style=ft.ButtonStyle(
                                    shape=ft.RoundedRectangleBorder(radius=10),
                                    padding=15,
                                ),
                                on_click=lambda e:callF.thisProcess.run({"功能":"科目余额表检查明细账","参数":[self.startDateButton.text, self.endDateButton.text]})
                            ),
                            padding=10,
                            alignment=ft.alignment.center,
                        ),
                    ],
                    spacing=15,
                ),
                padding=20,
                border_radius=15,
                bgcolor=ft.colors.SURFACE_VARIANT,
                margin=ft.margin.only(bottom=20),
            ),
            ft.Container(
                content=ft.Column(
                    controls=[
                        ft.Text("综合稽核", size=20, weight=ft.FontWeight.BOLD, color=ft.colors.PRIMARY),
                        ft.Container(
                            content=ft.ElevatedButton(
                                text="综合稽核",
                                icon=ft.icons.CLOUD_DOWNLOAD,
                                style=ft.ButtonStyle(
                                    shape=ft.RoundedRectangleBorder(radius=10),
                                    padding=15,
                                ),
                                on_click=lambda e:callF.thisProcess.run({"功能":"综合稽核"})
                            ),
                            padding=10,
                            alignment=ft.alignment.center,
                        ),
                    ],
                    spacing=15,
                ),
                padding=20,
                border_radius=15,
                bgcolor=ft.colors.SURFACE_VARIANT,
                margin=ft.margin.only(bottom=20),
            )
        ]
    
    def start_date_changed(self, e):
        if e.data:
            # 将日期时间字符串转换为datetime对象，然后格式化为YYYY-MM-DD
            date_obj = datetime.strptime(e.data, "%Y-%m-%dT%H:%M:%S.%f")
            self.startDateButton.text = date_obj.strftime("%Y-%m-%d")
            self.startDateButton.update()
    
    def end_date_changed(self, e):
        if e.data:
            # 将日期时间字符串转换为datetime对象，然后格式化为YYYY-MM-DD
            date_obj = datetime.strptime(e.data, "%Y-%m-%dT%H:%M:%S.%f")
            self.endDateButton.text = date_obj.strftime("%Y-%m-%d")
            self.endDateButton.update()
    
    def changeMain(self,e):
        self.mainRegional.update() 