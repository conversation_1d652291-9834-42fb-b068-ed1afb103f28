import src.utils.Browser.Browser as browser
import src.utils.Excel.excel as excel
import src.utils.cscec as cscec
import time
def autoReplace():
    wb=excel.myBook()
    ws=wb.sheet("合同资产调整")
    b=int(ws.Cells(1,5).Value)+4
    page=browser.myBrowser("cscec").page
    for i in range(b,ws.MaxRow+1):
        page=cscec.getCscecPage()
        table=cscec.cscecTable(page,"科目编号")
        table.appendRow()
        table.appendRow()
        time.sleep(0.1)
        table.reIndex()
        table.clickInputQuery(1,"*科目编号")
        cscec.dialogInput(page,"1125010000")
        table.clickInputQuery(1,"*借贷方向")
        cscec.getVisible(page,"//*[text()='借方']").click()

        table.clickInputQuery(2,"*科目编号")
        cscec.dialogInput(page,"2205010100")
        table.clickInputQuery(2,"*借贷方向")
        cscec.getVisible(page,"//*[text()='贷方']").click()


        table.click(1,"*科目名称")
        table2=cscec.cscecTable(page,"合同编号")
        table2.appendRow()
        time.sleep(0.1)
        table2.reIndex()
        table2.clickInputQuery(1,"*合同编号")
        cscec.dialogInput(page,ws.Cells(i,1).Value)

        table2.clickInputQuery(1,"*客商")
        cscec.dialogInput(page,ws.Cells(i,1).Value)

        table2.fillInput(1,"*金额",ws.Cells(i,4).Value)

        page.locator("//input[@placeholder='事由不能超过50字']").click()
        page.keyboard.type(ws.Cells(i,5).Value) 