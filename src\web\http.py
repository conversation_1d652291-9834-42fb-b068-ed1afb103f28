from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse
from pydantic import BaseModel
import subprocess
import threading
import time
import asyncio
import uvicorn
import sys
import logging
import traceback
import multiprocessing
import os
import src.base.settings as settings
import src.utils.closeAccountingPeriod.dataprocessing as dataprocessing

if not os.path.exists(settings.PATH_INTERNAL+"/web/assets"):
    os.makedirs(settings.PATH_INTERNAL+"/web/assets")
if not os.path.exists(settings.PATH_INTERNAL+"/web/static"):
    os.makedirs(settings.PATH_INTERNAL+"/web/static")

app = FastAPI()
# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载静态文件目录
app.mount("/assets", StaticFiles(directory=os.path.join(settings.PATH_INTERNAL,"web","assets")), name="assets")
app.mount("/static", StaticFiles(directory=os.path.join(settings.PATH_INTERNAL,"web","static")), name="static")


@app.get("/", response_class=HTMLResponse)
async def get_index():
    """
    返回 index.html 文件
    """
    try:
        index_path = os.path.join(settings.PATH_INTERNAL,"web","index.html")
        return FileResponse(index_path)
    except FileNotFoundError as e:
        raise HTTPException(status_code=404, detail="index.html not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/calculate")
async def root():
    return {"message": "Hello World"}

@app.post("/api/budget-data")
async def get_budget_data():
    try:
        from src.utils.DB.mainDB import mainDB
        try:
            rows = mainDB().getCloseDataByBalance()
            return rows
        except Exception as e:
            #打印所有出错位置
            import traceback
            traceback.print_exc()
            return [["错误"],[
                "错误"]]
    except Exception as e:
        print("发生错误", e)
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/budget-data-difference")
async def get_budget_data_difference():
    try:
        from src.utils.DB.midIntrSQLiteDB import excelDB
        conn=excelDB().conn
        rows=conn.execute("select * from 累计过账收入成本").fetchall()
        conn.close()
        resultMap={}
        for i in range(len(rows)):
            resultMap[f"{rows[i][0]}{rows[i][1]}"]={"收入":rows[i][2],"成本":rows[i][3]}
        return resultMap
    except Exception as e:
        print("发生错误", e)
        raise HTTPException(status_code=500, detail=str(e))
        

@app.post("/api/save-snapsheet")
async def save_snapsheet(request: Request):
    try:
        data = await request.json()
        result = dataprocessing.saveDataProcess(data)
        return {"status": "success", "result": result}
    except Exception as e:
        logging.error(f"Error in save_budget_data: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))
    
@app.post("/api/delete-snapsheet")
async def save_snapsheet(request: Request):
    try:
        data = await request.json()
        result = dataprocessing.deleteDataProcess(data)
        return {"status": "success", "result": result}
    except Exception as e:
        logging.error(f"Error in delete_budget_data: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))
    
@app.post("/api/save-snapsheet-new")
async def save_snapsheet_new(request: Request):
    try:
        data = await request.json()
        result = dataprocessing.saveDataProcess2(data)
        return {"status": "success", "result": result}
    except Exception as e:
        logging.error(f"Error in save_budget_data: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))
    
@app.post("/api/get-snapsheet")
async def get_snapsheet(request: Request):
    try:
        data = await request.json()
        result = dataprocessing.getDataProcess(data)
        return result
    except Exception as e:
        logging.error(f"Error in save_budget_data: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))
    
@app.post("/api/get-available-periods")
async def get_snapsheet_periods(request: Request):
    try:
        result = dataprocessing.queryDatalist()
        return result
    except Exception as e:
        logging.error(f"Error in save_budget_data: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/save-budget")
async def save_budget(request: Request):
    try:
        data = await request.json()
        result = dataprocessing.pushDataToColse(data)
        return result
    except Exception as e:
        logging.error(f"Error in save_budget_data: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))
    
@app.post("/api/save-config")
async def save_config(request: Request):
    try:
        data = await request.json()
        from src.utils.DB.configDB import configDB
        configDB().saveJson(data)
        return {"status": "success"}

    except Exception as e:
        logging.error(f"Error in save_budget_data: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/get-all-configs")
async def get_all_config(request: Request):
    try:
        from src.utils.DB.configDB import configDB
        return configDB().getJson()
    except Exception as e:
        logging.error(f"Error in get-all-configs: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/project/search")
async def get_project_list(request: Request):
    try:
        from src.utils.DB.mainDB import mainDB
        db=mainDB()
        value=db.conn.execute("select 利润中心 as id,利润中心描述 as name,项目编码 as code from 主数据 ").fetchall()
        title=[row[0] for row in db.conn.description]
        db.conn.close()
        #json化
        prjectList=[]
        for i in range(len(value)):
            prjectList.append({"id":value[i][0],"name":value[i][1],"code":value[i][2]})
        return prjectList
    except Exception as e:
        logging.error(f"Error in get-project-list: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/project/data")
async def get_project_data(request: Request):
    try:
        data = await request.json()
        projectId=data["projectId"]
        from src.utils.DB.mainDB import mainDB
        db=mainDB()
        return db.queryDataByProject(projectId)

    except Exception as e:
        logging.error(f"Error in get-project-data: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))



def run_server():
    uvicorn.run(app, host="127.0.0.1", port=8000,log_config=None) #注意没有cmd输出的情况下，必需将log_config设置为None,不然日志不输出服务器报错
    #,log_config=None

def start_server():
    # 创建服务器进程
    server_process = multiprocessing.Process(target=run_server)
    server_process.daemon = True  # 设置为守护进程，主进程退出时自动关闭
    server_process.start()
    return server_process
