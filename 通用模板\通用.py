import os
import sys

sys.path.append(".")
from playwright.sync_api import <PERSON><PERSON>, sync_playwright,Page
import src.utils.cscec as cscec
import src.utils.Excel.excel as excel
import time
import fitz
from tkinter import filedialog
def pdf_wirte(s,text):
    with fitz.open(s) as doc:
        for THEpage in doc:
                try:
                    font_size = 16
                    x = 20
                    y = 20
                    THEpage.insert_text(fitz.Point(x, y), text, fontname='helv', fontsize=font_size)
                except:
                    THEpage
        doc.saveIncr()


def label_question(s):
    s="//label[contains(text(),'"+s+"')]/parent::div/following-sibling::div[1]/div/div/div"
    return s


with sync_playwright() as playwright:
    browser = playwright.chromium.connect_over_cdp("http://localhost:9222")
    default_context = browser.contexts[0]
    page = cscec.switch_to_page(default_context,"中国建筑司库")
    ctable=cscec.cscecTable(page,"付款计算方式")
    ctable.reIndex()
    print(ctable.count)
    for i in range(ctable.count):
         #ctable.fillInput(i+1,"*本期确认金额","1000")
         print(ctable.getValue(i+1,"*本期确认金额"))
         ctable.fillInput(i+1,"*结算日期","2025-01-01")
         ctable.fillInput(i+1,"*付款期(天)","1000")




    








    
    







