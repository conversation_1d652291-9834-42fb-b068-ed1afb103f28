import os
import sys
sys.path.append(".")
import tkinter as tk
from tkinter import filedialog
import pandas as pd
import openpyxl
import src.utils.Excel.excel as excel
def select_files():
    # 打开文件选择对话框，允许选择多个文件
    file_paths = filedialog.askopenfilename(title='选择 Excel 文件', filetypes=[('Excel Files', '*.xlsx')])
    return file_paths


def complete1(wb:openpyxl.Workbook):    
    ws=wb["FZ2116 其他应付款"]
    d={}
    for row in ws.iter_rows():
        items=[row[1].value,row[2].value,row[4].value,row[5].value,row[6].value]
        filtered_items = [str(item) if item is not None else '' for item in items] #过滤None
        result = ''.join(map(str, filtered_items))
        try:
            d[result]=[items,row[7].value]
        except:
            pass
    thews=excel.myBook().sheet("FZ2116 其他应付款")
    k=thews.MaxRow+1
    for i in range(2,thews.MaxRow+1):
        try:
            items=[thews.Cells(i,2).Value,thews.Cells(i,3).Value,thews.Cells(i,5).Value,thews.Cells(i,6).Value,thews.Cells(i,7).Value]
            dindex=''.join(map(str, items))
            if d.get(dindex,False)!=False:
                thews.Cells(i,8).Value=d[dindex][1]
                thews.Cells(i,9).Value=d[dindex][1]
                d[dindex]=None
        except Exception as e:
            print(e)
    for key in d:
        if d[key]!=None:
            thews.Cells(k,8).Value=d[key][1]
            thews.Cells(k,9).Value=d[key][1]
            thews.Cells(k,2).Value=d[key][0][0]
            thews.Cells(k,3).Value=d[key][0][1]
            thews.Cells(k,5).Value=d[key][0][2]
            thews.Cells(k,6).Value=d[key][0][3]
            thews.Cells(k,7).Value=d[key][0][4]
            k=k+1
def complete2(wb:openpyxl.Workbook):    
    ws=wb["FZ2007 应付账款"]
    d={}
    for row in ws.iter_rows():
        items=[row[1].value,row[2].value,row[4].value,row[5].value,row[6].value]
        filtered_items = [str(item) if item is not None else '' for item in items] #过滤None
        result = ''.join(map(str, filtered_items))
        try:
            d[result]=[items,row[7].value]
        except:
            pass
    thews=excel.myBook().sheet("FZ2007 应付账款")
    k=thews.MaxRow+1
    for i in range(2,thews.MaxRow+1):
        try:
            items=[thews.Cells(i,2).Value,thews.Cells(i,3).Value,thews.Cells(i,5).Value,thews.Cells(i,6).Value,thews.Cells(i,7).Value]
            dindex=''.join(map(str, items))
            if d.get(dindex,False)!=False:
                thews.Cells(i,8).Value=d[dindex][1]
                thews.Cells(i,9).Value=d[dindex][1]
                d[dindex]=None
        except Exception as e:
            print(e)
    for key in d:
        if d[key]!=None:
            thews.Cells(k,8).Value=d[key][1]
            thews.Cells(k,9).Value=d[key][1]
            thews.Cells(k,2).Value=d[key][0][0]
            thews.Cells(k,3).Value=d[key][0][1]
            thews.Cells(k,5).Value=d[key][0][2]
            thews.Cells(k,6).Value=d[key][0][3]
            thews.Cells(k,7).Value=d[key][0][4]
            k=k+1

def complete3(wb:openpyxl.Workbook):    
    ws=wb["NBWL2108 内部往来-其他应收款"]
    d={}
    for row in ws.iter_rows():
        items=[row[1].value,row[2].value,row[4].value,row[5].value,row[6].value]
        filtered_items = [str(item) if item is not None else '' for item in items] #过滤None
        result = ''.join(map(str, filtered_items))
        try:
            d[result]=[items,row[7].value]
        except:
            pass
    thews=excel.myBook().sheet("NBWL2108 内部往来-其他应收款")
    k=thews.MaxRow+1
    for i in range(2,thews.MaxRow+1):
        try:
            items=[thews.Cells(i,2).Value,thews.Cells(i,3).Value,thews.Cells(i,5).Value,thews.Cells(i,6).Value,thews.Cells(i,7).Value]
            dindex=''.join(map(str, items))
            if d.get(dindex,False)!=False:
                thews.Cells(i,8).Value=d[dindex][1]
                thews.Cells(i,9).Value=d[dindex][1]
                d[dindex]=None
        except Exception as e:
            print(e)
    for key in d:
        if d[key]!=None:
            thews.Cells(k,8).Value=d[key][1]
            thews.Cells(k,9).Value=d[key][1]
            thews.Cells(k,2).Value=d[key][0][0]
            thews.Cells(k,3).Value=d[key][0][1]
            thews.Cells(k,5).Value=d[key][0][2]
            thews.Cells(k,6).Value=d[key][0][3]
            thews.Cells(k,7).Value=d[key][0][4]
            k=k+1

def complete():
    filePath=select_files()
    wb=openpyxl.load_workbook(filePath,data_only=True)
    complete1(wb)
    complete2(wb)
    complete3(wb)

complete()
