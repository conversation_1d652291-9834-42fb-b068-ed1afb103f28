import pandas as pd
import tkinter as tk
from tkinter import filedialog, messagebox
import duckdb
import os
import openpyxl
# 创建示例 DataFrame

ws=openpyxl.load_workbook(r"D:\Code\note\data\Excel文件\配置文件-fundCapital.xlsx",read_only=True)["单据分类"]
docCodeDict={}
for row in ws.iter_rows(min_row=2,values_only=True):
    docCodeDict[row[0]]=row[1]

def if_list_elements(lst2: list,s:str):
    d=s[:3]
    lst=str(lst2)
    if d in docCodeDict: 
        e=docCodeDict[d]
        if d == "CFK" and "劳务派遣" in lst:
            return "对外成本费用付款申请-薪酬"
        elif d == "SRL" and "待资金系统" in lst:
            return "收款确认单-退票"
        elif d == "JFK":
            if "劳务" in lst:
                return "支付劳务款"
            elif "购货" in lst:
                return "支付购货款"
            elif "分包" in lst:
                return "支付分包工程款"
            else :
                return "支付供应商其他"
        else:
            return e
    else:
        return ""

 

# 定义保存 Excel 的函数
def save_to_excel():
    # 打开文件选择对话框
    path=os.path.dirname(os.path.abspath(__file__))
    print(path)
    with open(path+"\凭证查询3.sql","r",encoding="UTF-8") as f:
        content=f.read()

    con=duckdb.connect(r"D:\Code\note\data\Duckdb\example.duckdb")
    con.create_function("if_classification", if_list_elements, [duckdb.list_type(type=str),str], str)

    df=con.sql(content).df()
    file_path = filedialog.asksaveasfilename(
        defaultextension='.xlsx',  # 默认扩展名
        filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],  # 文件类型
        title="保存文件为"
    )

    
    # 如果用户选择了文件路径，则保存 DataFrame
    if file_path:
        try:
            df.to_excel(file_path, index=False, engine='openpyxl')
            messagebox.showinfo("成功", f"数据已保存到 {file_path}")
        except Exception as e:
            messagebox.showerror("错误", f"保存文件时发生错误: {str(e)}")
    con.close()
if __name__ == "__main__":
    save_to_excel()
