import os
import sys

sys.path.append(".")
from playwright.sync_api import <PERSON><PERSON>, sync_playwright
import src.utils.cscec as cscec
import src.utils.Excel.excel as excel
wb=excel.myBook()
ws=wb.sheet("模板")

thePath=r"C:\Users\<USER>\Desktop\回单"

with sync_playwright() as playwright:
    browser = playwright.chromium.connect_over_cdp("http://localhost:9222")
    default_context = browser.contexts[0]
    page = cscec.switch_to_page(default_context,"中国建筑司库")

    for i in range(2,ws.MaxRow+1):
        flowCode=ws.Cells(i,2).Value
        cscec.fillLalbel_input(page,"付款单号",flowCode)
        cscec.getVisible(page,"//span[(text()='查询')]").click()
        cscec.getVisible(page,"//table//div[(text()='1')]").click()
        cscec.clickMouse(page,"//*[@title='联查单据']",97,50)
        #page.locator("//*[@title='联查单据']").click()
        cscec.getVisible(page,"//*[text()='联查资金单据']").click()
        cscec.getVisible(page,"//*[text()='电子回单']").click()

        frame=page.frame_locator("//*[@id='FormIFrame1']").frame_locator("//*[@id='pdf-box']") #两个frame
        with page.expect_download(timeout=80000) as download_info:
            frame.locator("//button[@id='download']").click()
            download = download_info.value
            path = download.path()
            download.save_as(path=thePath+f"/{flowCode}.pdf")
        page.locator("//div[text()='电子回单']/preceding-sibling::div[1]").click()
        cscec.closeTab(page)
