import flet as ft
import src.Gui.callProcess as callF
import src.base.settings as settings
import os
from datetime import datetime
import src.base.cache as cache

class RD(ft.Column):
    def __init__(self):
        super().__init__()
        self.controls=[
            ft.Container(
                content=ft.Column(
                    controls=[
                        ft.Text("研发费用管理", size=16, weight=ft.FontWeight.BOLD),
                        ft.Row(
                            controls=[
                                ft.ElevatedButton(
                                    text="打开模板文件",
                                    icon=ft.icons.FILE_OPEN,
                                    style=ft.ButtonStyle(
                                        shape=ft.RoundedRectangleBorder(radius=10),
                                        padding=15,
                                    ),
                                    on_click=lambda e:os.startfile(settings.PATH_EXCEL+"/研发费用.xlsx")
                                ),
                                ft.ElevatedButton(
                                    text="生成模板",
                                    icon=ft.icons.CREATE_NEW_FOLDER,
                                    style=ft.ButtonStyle(
                                        shape=ft.RoundedRectangleBorder(radius=10),
                                        padding=15,
                                    ),
                                    on_click=lambda e:callF.thisProcess.run({"功能":"研发生成模板"})
                                ),
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_AROUND,
                        ),
                    ],
                    spacing=10,
                ),
                padding=15,
                border_radius=10,
                bgcolor=ft.colors.SURFACE_VARIANT,
                margin=ft.margin.only(bottom=10),
            ),
            ft.Container(
                content=ft.Column(
                    controls=[
                        ft.Text("库存操作", size=16, weight=ft.FontWeight.BOLD),
                        ft.Row(
                            controls=[
                                ft.ElevatedButton(
                                    text="批量退库",
                                    icon=ft.icons.INVENTORY_2,
                                    style=ft.ButtonStyle(
                                        shape=ft.RoundedRectangleBorder(radius=10),
                                        padding=15,
                                    ),
                                    on_click=lambda e:callF.thisProcess.run({"功能":"研发批量退库"})
                                ),
                                ft.ElevatedButton(
                                    text="批量出库",
                                    icon=ft.icons.OUTBOX,
                                    style=ft.ButtonStyle(
                                        shape=ft.RoundedRectangleBorder(radius=10),
                                        padding=15,
                                    ),
                                    on_click=lambda e:callF.thisProcess.run({"功能":"研发批量出库"})
                                ),
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_AROUND,
                        ),
                    ],
                    spacing=10,
                ),
                padding=15,
                border_radius=10,
                bgcolor=ft.colors.SURFACE_VARIANT,
                margin=ft.margin.only(bottom=10),
            ),
        ]


class MDWidget(ft.Column):
    def __init__(self):
        super().__init__()
        self.controls=[
            ft.Container(
                content=ft.Column(
                    controls=[
                        ft.Text("项目人员管理", size=16, weight=ft.FontWeight.BOLD),
                        ft.Row(
                            controls=[
                                ft.ElevatedButton(
                                    text="快速增加项目人员",
                                    icon=ft.icons.PERSON_ADD,
                                    style=ft.ButtonStyle(
                                        shape=ft.RoundedRectangleBorder(radius=10),
                                        padding=15,
                                    ),
                                    on_click=lambda e:callF.thisProcess.run({"功能":"快速增加项目人员"})
                                ),
                            ],
                            alignment=ft.MainAxisAlignment.CENTER,
                        ),
                    ],
                    spacing=10,
                ),
                padding=15,
                border_radius=10,
                bgcolor=ft.colors.SURFACE_VARIANT,
                margin=ft.margin.only(bottom=10),
            ),
        ]

class VoucherWidget(ft.Column):
    def __init__(self):
        super().__init__()
        self.visible = True
        self.expand = True
        self.spacing = 10
        
        # 创建日期选择器
        self.startDate = ft.DatePicker(
            first_date=datetime(2018, 1, 1),
            last_date=datetime(2030, 12, 31),
            on_change=self.start_date_changed
        )
        self.endDate = ft.DatePicker(
            first_date=datetime(2018, 1, 1),
            last_date=datetime(2030, 12, 31),
            on_change=self.end_date_changed
        )
        
        # 创建日期显示按钮
        self.startDateButton = ft.ElevatedButton(
            text=cache.Lastdate,
            icon=ft.icons.CALENDAR_TODAY,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
                color=ft.colors.ON_SURFACE,
                bgcolor=ft.colors.SURFACE,
            ),
            on_click=lambda _: self.startDate.pick_date()
        )
        
        self.endDateButton = ft.ElevatedButton(
            text=cache.Nowdate,
            icon=ft.icons.CALENDAR_TODAY,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
                color=ft.colors.ON_SURFACE,
                bgcolor=ft.colors.SURFACE,
            ),
            on_click=lambda _: self.endDate.pick_date()
        )
        
        # 创建日期选择器容器
        self.startDateContainer = ft.Container(
            content=ft.Row(
                controls=[
                    ft.Text("开始日期", size=14, weight=ft.FontWeight.BOLD, color=ft.colors.PRIMARY),
                    self.startDateButton
                ],
                spacing=10,
                alignment=ft.MainAxisAlignment.START,
            ),
            padding=10,
            border_radius=12,
            bgcolor=ft.colors.SURFACE_VARIANT,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=3,
                color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                offset=ft.Offset(0, 1),
            ),
            width=220,
        )
        
        self.endDateContainer = ft.Container(
            content=ft.Row(
                controls=[
                    ft.Text("结束日期", size=14, weight=ft.FontWeight.BOLD, color=ft.colors.PRIMARY),
                    self.endDateButton
                ],
                spacing=10,
                alignment=ft.MainAxisAlignment.START,
            ),
            padding=10,
            border_radius=12,
            bgcolor=ft.colors.SURFACE_VARIANT,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=3,
                color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                offset=ft.Offset(0, 1),
            ),
            width=220,
        )
        
        # 创建浏览器选择单选按钮组
        self.browser_radio_group = ft.RadioGroup(
            content=ft.Row(
                controls=[
                    ft.Radio(value="使用已打开浏览器", label="使用已打开浏览器"),
                    ft.Radio(value="自动打开浏览器", label="自动打开浏览器"),
                ],
                spacing=10,
            ),
            value="使用已打开浏览器",
            on_change=self.on_browser_change
        )
        
        # 创建制证相关按钮
        self.auto_voucher_btn = ft.ElevatedButton(
            text="自动制证",
            icon=ft.icons.DESCRIPTION,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
            ),
            on_click=self.on_auto_voucher_clicked
        )
        
        self.pull_log_btn = ft.ElevatedButton(
            text="拉取制证日志",
            icon=ft.icons.DOWNLOAD,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
            ),
            on_click=self.on_pull_log_clicked
        )
        
        self.clear_log_btn = ft.ElevatedButton(
            text="清空制证日志",
            icon=ft.icons.DELETE,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
            ),
            on_click=self.on_clear_log_clicked
        )
        
        self.controls = [
            ft.Container(
                content=ft.Column(
                    controls=[
                        ft.Text("制证管理", size=16, weight=ft.FontWeight.BOLD),
                        ft.Row(
                            controls=[
                                self.browser_radio_group,
                                self.auto_voucher_btn,
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_AROUND,
                            vertical_alignment=ft.CrossAxisAlignment.CENTER,
                        ),
                        ft.Divider(height=1, color=ft.colors.OUTLINE),
                        ft.Row(
                            controls=[
                                self.startDateContainer,
                                self.endDateContainer,
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_AROUND,
                            vertical_alignment=ft.CrossAxisAlignment.CENTER,
                        ),
                        ft.Divider(height=1, color=ft.colors.OUTLINE),
                        ft.Row(
                            controls=[
                                self.pull_log_btn,
                                self.clear_log_btn,
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_AROUND,
                            vertical_alignment=ft.CrossAxisAlignment.CENTER,
                        ),
                    ],
                    spacing=10,
                ),
                padding=15,
                border_radius=10,
                bgcolor=ft.colors.SURFACE_VARIANT,
                margin=ft.margin.only(bottom=10),
            ),
            self.startDate,
            self.endDate
        ]
    
    def start_date_changed(self, e):
        if e.data:
            self.startDateButton.text = e.data[:10]
            self.startDateButton.update()
    
    def end_date_changed(self, e):
        if e.data:
            self.endDateButton.text = e.data[:10]
            self.endDateButton.update()
    
    def on_browser_change(self, e):
        # 保存选择的浏览器模式
        self.browser_mode = e.control.value
        self.browser_radio_group.value = e.control.value
    
    def on_auto_voucher_clicked(self, e):
        # 使用保存的浏览器模式作为参数
        callF.thisProcess.run({"功能":"自动制证", "参数": [self.browser_mode]})
    
    def on_pull_log_clicked(self, e):
        callF.thisProcess.run({"功能":"拉取制证日志", "参数": [self.startDateButton.text, self.endDateButton.text]})
    
    def on_clear_log_clicked(self, e):
        callF.thisProcess.run({"功能":"清空制证日志"})

class commonTabView(ft.Column):
    def __init__(self):
        super().__init__()
        self.visible = True
        self.expand = True
        # 添加年份和月份的存储变量
        self.selected_year = str(datetime.now().year)
        self.selected_month = str(datetime.now().month)
        
        self.navigation_bar = ft.NavigationBar(
            destinations=[
                ft.NavigationBarDestination(icon=ft.icons.COMMENT, label="常用模块"),
                ft.NavigationBarDestination(icon=ft.icons.PADDING, label="制证模块"),
                ft.NavigationBarDestination(icon=ft.icons.EMAIL, label="主数据模块"),
                ft.NavigationBarDestination(icon=ft.icons.WRAP_TEXT_SHARP, label="特殊模块"),
            ],
            on_change=lambda e:self.changeMain(e)
        )
        self.mainRegional=ft.Column(controls=[],alignment=ft.MainAxisAlignment.CENTER)
        self.mainRegional=ft.Column(
            controls=[],
            alignment=ft.MainAxisAlignment.CENTER,
            scroll=ft.ScrollMode.AUTO,
            expand=True,
        )
        self.controls=(self.navigation_bar,self.mainRegional)
        
        # 创建常用模块的按钮卡片
        self.default = [
            ft.Container(
                content=ft.Column(
                    controls=[
                        ft.Text("单据管理", size=16, weight=ft.FontWeight.BOLD),
                        ft.Row(
                            controls=[
                                ft.ElevatedButton(
                                    text="中台单据导出",
                                    icon=ft.icons.DOWNLOAD,
                                    style=ft.ButtonStyle(
                                        shape=ft.RoundedRectangleBorder(radius=10),
                                        padding=15,
                                    ),
                                    on_click=lambda e:callF.thisProcess.run({"功能":"中台单据导出"})
                                ),
                                ft.ElevatedButton(
                                    text="批量打印单据",
                                    icon=ft.icons.PRINT,
                                    style=ft.ButtonStyle(
                                        shape=ft.RoundedRectangleBorder(radius=10),
                                        padding=15,
                                    ),
                                    on_click=lambda e:callF.thisProcess.run({"功能":"批量打印单据"})
                                ),
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_AROUND,
                        ),
                    ],
                    spacing=10,
                ),
                padding=15,
                border_radius=10,
                bgcolor=ft.colors.SURFACE_VARIANT,
                margin=ft.margin.only(bottom=10),
            ),
            ft.Container(
                content=ft.Column(
                    controls=[
                        ft.Text("档案成册管理", size=16, weight=ft.FontWeight.BOLD),
                        ft.Row(
                            controls=[
                                ft.Dropdown(
                                    label="年份",
                                    width=200,
                                    options=[
                                        ft.dropdown.Option(str(year)) 
                                        for year in range(2016, datetime.now().year + 1)
                                    ],
                                    value=self.selected_year,
                                    on_change=lambda e: setattr(self, 'selected_year', e.control.value)
                                ),
                                ft.Dropdown(
                                    label="月份",
                                    width=200,
                                    options=[
                                        ft.dropdown.Option(str(month)) 
                                        for month in range(1, 13)
                                    ],
                                    value=self.selected_month,
                                    on_change=lambda e: setattr(self, 'selected_month', e.control.value)
                                ),
                                ft.ElevatedButton(
                                    text="查询凭证数量",
                                    icon=ft.icons.SEARCH,
                                    style=ft.ButtonStyle(
                                        shape=ft.RoundedRectangleBorder(radius=10),
                                        padding=15,
                                    ),
                                    on_click=lambda e: callF.thisProcess.run({
                                        "功能": "档案成册管理", 
                                        "参数": [self.selected_year, self.selected_month]
                                    })
                                ),
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_AROUND,
                        ),
                        ft.Divider(height=1, color=ft.colors.OUTLINE),
                        ft.Row(
                            controls=[
                                ft.ElevatedButton(
                                    text="查询档案模板",
                                    icon=ft.icons.DESCRIPTION,
                                    style=ft.ButtonStyle(
                                        shape=ft.RoundedRectangleBorder(radius=10),
                                        padding=15,
                                    ),
                                    on_click=lambda e: callF.thisProcess.run({"功能":"查询档案模板"})
                                ),
                                ft.ElevatedButton(
                                    text="更新档案模板",
                                    icon=ft.icons.UPDATE,
                                    style=ft.ButtonStyle(
                                        shape=ft.RoundedRectangleBorder(radius=10),
                                        padding=15,
                                    ),
                                    on_click=lambda e: callF.thisProcess.run({"功能":"更新档案模板"})
                                ),
                                ft.ElevatedButton(
                                    text="执行档案成册",
                                    icon=ft.icons.PLAY_ARROW,
                                    style=ft.ButtonStyle(
                                        shape=ft.RoundedRectangleBorder(radius=10),
                                        padding=15,
                                    ),
                                    on_click=lambda e: callF.thisProcess.run({"功能":"执行档案成册"})
                                ),
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_AROUND,
                        ),
                    ],
                    spacing=10,
                ),
                padding=15,
                border_radius=10,
                bgcolor=ft.colors.SURFACE_VARIANT,
                margin=ft.margin.only(bottom=10),
            ),
        ]
        self.mainRegional.controls=self.default

    def changeMain(self,e):
        index=e.control.selected_index
        if index==0:
            # 常用模块
            self.mainRegional.controls=self.default
        elif index==1:
            # 制证模块
            self.mainRegional.controls=[
                ft.Container(
                    content=VoucherWidget(),
                    padding=15,
                    border_radius=10,
                    bgcolor=ft.colors.SURFACE_VARIANT,
                    margin=ft.margin.only(bottom=10),
                )
            ]
        elif index==2:
            # 主数据模块
            self.mainRegional.controls=[MDWidget()]
        elif index==3:
            # 特殊模块
            self.mainRegional.controls=[RD()]
        
        self.mainRegional.update()