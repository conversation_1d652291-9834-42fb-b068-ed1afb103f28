# d:\infoTech\fip-rpa-robot-oldscriptref\src\Gui\FletGui2\views\home_view.py
import flet as ft
from . import ui_constants as C # Assuming ui_constants.py is in the same directory

# Data for function cards - module_name should correspond to view file names (without .py)
FUNCTION_CARDS_DATA = [
    {"name": "统计大师", "icon": ft.icons.DOCUMENT_SCANNER_OUTLINED, "description": "Scan and process data streams.", "color": "#FF8C00", "module_name": "data_scanner_view"},
    {"name": "自动制证", "icon": ft.icons.SETTINGS_APPLICATIONS_ROUNDED, "description": "Monitor and diagnose system health.", "color": "#39FF14", "module_name": "system_diagnostics_view"},
    {"name": "资金协管", "icon": ft.icons.SCHEDULE_ROUNDED, "description": "Manage and schedule automated tasks.", "color": "#00BFFF", "module_name": "task_scheduler_view"},
    {"name": "智能稽核", "icon": ft.icons.POLICY_ROUNDED, "description": "智能化审计与风险监控.", "color": "#FF6347", "module_name": "intelligent_audit_view"},
    {"name": "一键结账", "icon": ft.icons.POINT_OF_SALE_ROUNDED, "description": "快速完成财务结账流程.", "color": "#4682B4", "module_name": "one_click_checkout_view"},
    {"name": "智慧税务", "icon": ft.icons.ACCOUNT_BALANCE_WALLET_ROUNDED, "description": "智能化税务管理与申报.", "color": "#32CD32", "module_name": "smart_taxation_view"},
    {"name": "速填精灵", "icon": ft.icons.EDIT_NOTE_ROUNDED, "description": "辅助快速填写各类表单.", "color": "#FFD700", "module_name": "quick_fill_elf_view"},
    {"name": "报表助手", "icon": ft.icons.ASSESSMENT_ROUNDED, "description": "生成与分析财务报表.", "color": "#6A5ACD", "module_name": "report_assistant_view"},
    {"name": "电子归档", "icon": ft.icons.ARCHIVE_ROUNDED, "description": "安全管理电子会计档案.", "color": "#DAA520", "module_name": "electronic_archiving_view"},
    {"name": "系统协同", "icon": ft.icons.HUB_ROUNDED, "description": "多系统间数据与流程协同.", "color": "#8A2BE2", "module_name": "system_collaboration_view"},
    # Add more functions here as needed
]

def create_function_card(data, on_click_callback):
    """Creates a single function card container."""

    async def _handle_card_click(e):
        await on_click_callback(data["module_name"], data["name"], e)

    def on_hover(e):
        """Handle hover effects for the card."""
        if e.data == "true":
            e.control.scale = 1.05
            e.control.shadow = ft.BoxShadow(
                spread_radius=3,
                blur_radius=12,
                color=ft.colors.with_opacity(0.5, data["color"]),
                offset=ft.Offset(0, 4),
            )
        else:
            e.control.scale = 1.0
            e.control.shadow = ft.BoxShadow(
                spread_radius=2,
                blur_radius=8,
                color=ft.colors.with_opacity(0.3, data["color"]),
                offset=ft.Offset(2, 2),
            )
        e.control.update()

    return ft.Container(
        content=ft.Column(
            [
                ft.Container(
                    content=ft.Icon(data["icon"], size=40, color=data["color"]),
                    animate_scale=ft.animation.Animation(200, ft.AnimationCurve.EASE_OUT),
                ),
                ft.Text(data["name"], size=16, weight=ft.FontWeight.BOLD, color=C.TEXT_COLOR, text_align=ft.TextAlign.CENTER, font_family=C.FONT_ORBITRON),
                ft.Text(data["description"], size=11, color=C.TEXT_COLOR, text_align=ft.TextAlign.CENTER, font_family=C.FONT_CONSOLAS, expand=True, max_lines=3, overflow=ft.TextOverflow.ELLIPSIS),
            ],
            alignment=ft.MainAxisAlignment.SPACE_AROUND,
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            spacing=10,
        ),
        width=150,
        height=135,
        bgcolor=ft.colors.with_opacity(0.8, C.SECONDARY_COLOR),
        border_radius=15,
        padding=15,
        ink=True,
        on_click=_handle_card_click,
        on_hover=on_hover,
        shadow=ft.BoxShadow(
            spread_radius=2,
            blur_radius=8,
            color=ft.colors.with_opacity(0.3, data["color"]),
            offset=ft.Offset(2, 2),
        ),
        border=ft.border.all(1, ft.colors.with_opacity(0.5, data["color"])),
        animate_scale=ft.animation.Animation(200, ft.AnimationCurve.EASE_OUT),
        animate_opacity=ft.animation.Animation(200, ft.AnimationCurve.EASE_OUT),
    )

def get_function_grid(on_click_callback):
    """Returns the GridView containing all function cards."""
    return ft.Container(
        content=ft.GridView(
            controls=[create_function_card(data, on_click_callback) for data in FUNCTION_CARDS_DATA],
            expand=False,
            runs_count=3,  # Reduced to 3 columns to fit better with sidebar
            max_extent=150,
            child_aspect_ratio=1.11, # 150 / 135
            spacing=15,
            run_spacing=15,
            padding=20,
        ),
        # Add a subtle background and border
        bgcolor=ft.colors.with_opacity(0.02, C.ACCENT_COLOR),
        border_radius=12,
        border=ft.border.all(1, ft.colors.with_opacity(0.1, C.ACCENT_COLOR)),
        padding=10,
    )

def get_welcome_content():
    """Returns the initial content for the function display area."""
    return ft.Column([
        ft.Text("请选择功能模块", size=20, color=C.TEXT_COLOR, italic=True, text_align=ft.TextAlign.CENTER, font_family=C.FONT_ORBITRON),
        ft.Icon(ft.icons.TOUCH_APP_OUTLINED, size=50, color=C.ACCENT_COLOR)
        ], 
        horizontal_alignment=ft.CrossAxisAlignment.CENTER, 
        alignment=ft.MainAxisAlignment.CENTER, 
        expand=True
    )
