import sys
import os
script=os.path.abspath(__file__)
home=os.path.dirname(script)
os.chdir(home)
sys.path.append(".")
sys.path.append("..")
import flet as ft
import src.Gui.FletGui.components.call_component as call_component
from src.Gui.FletGui.components.Container import LabeledContainer
import src.Gui.callProcess #导入开始创建函数进程
import multiprocessing

def main(page: ft.Page):
    page.locale_configuration = ft.LocaleConfiguration(
    supported_locales=[
        ft.Locale("zh", "CN", "Hans"),
    ],
    current_locale=ft.Locale("zh", "CN", "Hans"),
)
    page.add(call_component.export())
    page.add(call_component.export_balance())
    page.add(LabeledContainer("导出主数据",ft.But<PERSON>("导出主数据"),bgcolor=ft.colors.GREEN_50,width=600))

if __name__ == "__main__":  
    multiprocessing.freeze_support()
    ft.app(main)