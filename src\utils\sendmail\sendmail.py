
import smtplib
import os
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.application import MIMEApplication
from email.header import Header
import src.utils.Excel.openpyxlExcel as openpyxlExcel

def send_mail_file(user,to_mail,v_subject,v_msg,attachment_name_d,smtpobj):
    msg = MIMEMultipart()
    msg['From'] = Header(user) #发送者
    msg['To'] = Header(to_mail)  # 接收者
    msg['Subject'] = Header(v_subject, 'utf-8')  # 邮件主题
    # 邮件正文内容
    msg.attach(MIMEText(v_msg, 'html', 'utf-8'))
    for attachment_name in attachment_name_d:
        if type(attachment_name)!=type(None):
            att1 = MIMEApplication(open(attachment_name, 'rb').read())
            att1.add_header('Content-Disposition', 'attachment',filename=attachment_name.split('\\')[-1])
            msg.attach(att1)
    try:
        list_mail = to_mail.split(",")
        smtpobj.sendmail(user, list_mail , msg.as_string())
        print(to_mail+",邮件发送成功")       
    except smtplib.SMTPException as e:
        print(e)
        print(to_mail+",无法发送邮件")

def main():
    
    configDict=openpyxlExcel.getDict("邮箱配置")
    user=configDict["用户名"]
    password=configDict["密码"]
    server=configDict["服务器"]
    mail_port=configDict["端口"]

    sendList=openpyxlExcel.getmailList()
    
    if mail_port=="465":
        smtpobj=smtplib.SMTP_SSL(server, port=mail_port) 
        smtpobj.login(user, password)   # 登录，发送者账号和口令
    else:
        smtpobj = smtplib.SMTP()  #smtpobj.starttls()    #ttls加密
        smtpobj.connect(server, mail_port)    # 建立连接，邮箱服务和端口号
        smtpobj.login(user, password)
        #smtpobj.starttls()
   
    for row in sendList:

        name=row[0]
        title=row[1]
        msg=row[2]
        mail=row[3]
        
        file=set()
        for i in range(4,len(row)):
            file.add(row[i]) 
        send_mail_file(
                    user, # 发送服务器用户名
                    mail, # 接收邮箱地址
                    title, # 标题
                    '<p>'+name+','+msg+'</p>', # 邮件正文，html格式
                    file, # 附件
                    smtpobj
                    )
    smtpobj.quit()




