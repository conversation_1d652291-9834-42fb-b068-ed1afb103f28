import flet as ft
import os
import src.base.settings as settings
import src.Gui.callProcess as callF


class settingTabView(ft.Column):
    def __init__(self):
        super().__init__()
        self.visible = True
        self.expand = True
        self.expireTime = callF.regInstance.expire_time
        
        # 创建主区域
        self.mainRegional = ft.Column(
            controls=[],
            alignment=ft.MainAxisAlignment.CENTER,
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            spacing=20,
        )
        
        # 创建机器码显示框
        self.machine_code_field = ft.TextField(
            value=callF.regInstance.machine_code,
            read_only=True,
            label="本机机器码",
            expand=True,
            border_radius=10,
            border_color=ft.colors.OUTLINE_VARIANT,
            focused_border_color=ft.colors.PRIMARY,
            text_style=ft.TextStyle(size=14),
        )
        
        # 创建注册码输入框
        self.inputText = ft.TextField(
            value="",
            min_lines=5,
            max_lines=5,
            text_size=14,
            label="输入注册码",
            expand=True,
            border_radius=10,
            border_color=ft.colors.OUTLINE_VARIANT,
            focused_border_color=ft.colors.PRIMARY,
        )
        
        # 创建按钮容器
        button_container = ft.Container(
            content=ft.Row(
                controls=[
                    ft.ElevatedButton(
                        "注册",
                        icon=ft.icons.APP_REGISTRATION,
                        on_click=lambda e: self.register(),
                        style=ft.ButtonStyle(
                            shape=ft.RoundedRectangleBorder(radius=10),
                            padding=15,
                            text_style=ft.TextStyle(size=14),
                        ),
                    ),
                    ft.ElevatedButton(
                        "更新",
                        icon=ft.icons.UPDATE,
                        on_click=lambda e: callF.thisProcess.run({"功能":"更新软件"}),
                        style=ft.ButtonStyle(
                            shape=ft.RoundedRectangleBorder(radius=10),
                            padding=15,
                            text_style=ft.TextStyle(size=14),
                        ),
                    ),
                    ft.ElevatedButton(
                        "软件维护",
                        icon=ft.icons.BUILD,
                        on_click=lambda e: callF.thisProcess.run({"模块":"base.installerCheck","函数":"main"}),
                        style=ft.ButtonStyle(
                            shape=ft.RoundedRectangleBorder(radius=10),
                            padding=15,
                            text_style=ft.TextStyle(size=14),
                        ),
                    ),
                    ft.ElevatedButton(
                        "打开配置文件",
                        icon=ft.icons.FILE_OPEN,
                        on_click=lambda e: os.startfile(settings.PATH_CONFIG+"\配置文件.xlsx"),
                        style=ft.ButtonStyle(
                            shape=ft.RoundedRectangleBorder(radius=10),
                            padding=15,
                            text_style=ft.TextStyle(size=14),
                        ),
                    ),
                ],
                alignment=ft.MainAxisAlignment.CENTER,
                spacing=10,
            ),
            padding=15,
            border_radius=10,
            bgcolor=ft.colors.SURFACE_VARIANT,
            margin=ft.margin.only(bottom=10),
        )
        
        # 创建到期时间显示
        self.expire_time_text = ft.Text(
            f"本机到期时间：{self.expireTime}", #问题在于这里作为基础类型是值传递
            size=16,
            weight=ft.FontWeight.BOLD,
        )
        
        # 添加所有控件到主区域
        self.mainRegional.controls = [
            ft.Container(
                content=self.machine_code_field,
                padding=15,
                border_radius=10,
                bgcolor=ft.colors.SURFACE_VARIANT,
                margin=ft.margin.only(bottom=10),
            ),
            ft.Container(
                content=self.inputText,
                padding=15,
                border_radius=10,
                bgcolor=ft.colors.SURFACE_VARIANT,
                margin=ft.margin.only(bottom=10),
            ),
            button_container,
            ft.Container(
                content=self.expire_time_text,
                padding=15,
                border_radius=10,
                bgcolor=ft.colors.SURFACE_VARIANT,
                margin=ft.margin.only(bottom=10),
            ),
        ]
        
        self.controls = [self.mainRegional]
    
    def register(self):
        callF.regInstance.register(self.inputText.value)
        self.expireTime = callF.regInstance.expire_time#刷新时间
        self.expire_time_text.value = f"本机到期时间：{self.expireTime}" #问题在于这里作为基础类型是值传递
        self.expire_time_text.update() #因为是值传递，所以需要重新创建
