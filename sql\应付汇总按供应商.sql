WITH a AS (
select 
any_value(利润中心描述) as 利润中心名称,
any_value(利润中心) as 利润中心,
any_value(供应商) as 供应商,
any_value(供应商描述) as 供应商描述,
any_value(CASE WHEN 总账科目长文本 LIKE '应付账款%' AND 总账科目长文本 NOT LIKE '%进项税%'  THEN 总账科目长文本 ELSE NULL END ) AS 业务类型,
0-sum(case when (((借贷标识 = 'H' and 反记帐 = '') or (借贷标识 = 'S' and 反记帐 = 'X') ) and 总账科目长文本 like '应付账款%' and 总账科目长文本 not like '暂估' and 文本 not like '自动清账剩余项目')then 带符号的本位币金额 else null end) as 累计结算,
sum(case when (((借贷标识 = 'S' and 反记帐 = '') or (借贷标识 = 'H' and 反记帐 = 'X') ) and 总账科目长文本 like '应付账款%' and 总账科目长文本 not like '暂估' and 文本 not like '自动清账剩余项目' )then 带符号的本位币金额 else null end) as 累计付款,
0-sum(case when (总账科目长文本 like '其他应付款%保证金%' ) then 带符号的本位币金额 else null end) as 保证金余额,
sum(case when (总账科目长文本 like '其他应收款%进项税%' or 总账科目长文本 like '应付账款%进项税%' ) then 带符号的本位币金额 else null end) as 进项税余额,
FROM 明细帐 WHERE (总账科目长文本 LIKE '%进项税%' OR 总账科目长文本 LIKE '应付账款%' OR 总账科目长文本 LIKE '其他应付款%') AND 总账科目长文本 NOT LIKE '%供应链融资%' AND 总账科目长文本 NOT LIKE '%暂估%'
GROUP by  利润中心,供应商),
b AS (
    select any_value(项目名称) as 项目名称,
    any_value(客商编号) as 客商编号,
    any_value(税率) as 税率,
    sum(结算金额) as 结算金额,
    sum(已付金额) as 已付金额,
    sum(发票金额) as 发票金额
    from 一体化合同台账
    GROUP BY 项目名称,客商名称
)
--
SELECT a.*,税率,结算金额,已付金额,发票金额 FROM  a left join b on 利润中心名称=项目名称 and 供应商=客商编号 WHERE 累计结算>0.001 OR 累计结算<-0.001