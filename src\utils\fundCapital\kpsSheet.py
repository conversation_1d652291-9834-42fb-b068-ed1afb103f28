
import requests
import sys
import os

def getDataFromWps(url,AirScriptToken):
    request_url = url
    headers = {'Content-Type': 'application/json',
        'AirScript-Token': AirScriptToken}
    payloads = {'Context': {'argv':{}}}
    response = requests.post(url=request_url, json=payloads,headers=headers).json()['data']['result']  #payloads不是用data而是json编码
    return response


def updateDatatoWithWps(arr,url,AirScriptToken):
    request_url = url
    headers = {'Content-Type': 'application/json',
        'AirScript-Token': AirScriptToken}
    payloads = {'Context': {'argv':{'js':arr}}}
    requests.post(url=request_url, json=payloads,headers=headers) #payloads不是用data而是json编码

def updateDatatoWithWps2(arr,url,AirScriptToken):
    request_url = url
    headers = {'Content-Type': 'application/json',
        'AirScript-Token': AirScriptToken}
    payloads = {'Context': {'argv':{'js':arr}}}
    requests.post(url=request_url, json=payloads,headers=headers)#payloads不是用data而是json编码



if __name__ == '__main__':
    arr=["测试1","测试2"]
    url="https://www.kdocs.cn/api/v3/ide/file/cazravtMi0Md/script/V2-1DYGtlTe3ybI3fec1O0oIg/sync_task"
    AirScriptToken="2CUotnkPkhJzWPhJRuYSoZ"
    headers = {'Content-Type': 'application/json',
        'AirScript-Token': AirScriptToken}
    payloads = {'Context': {'argv':{'js':arr}}}
    a=requests.post(url=url, json=payloads,headers=headers) #payloads不是用data而是json编码
    print(a.content.decode("utf-8"))