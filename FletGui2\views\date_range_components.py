import flet as ft
from datetime import datetime

class DateRangeComponent(ft.Column):
    """A reusable date range component with start date, end date, and action button."""
    
    def __init__(self, page: ft.Page, test_name: str, button_text: str = "Execute"):
        self.page = page
        self.test_name = test_name
        self.button_text = button_text
        
        # Create date picker dialogs first
        self.start_date_dialog = ft.DatePicker(
            on_change=self.update_start_date,
            first_date=datetime(2020, 1, 1),
            last_date=datetime(2030, 12, 31)
        )
        
        self.end_date_dialog = ft.DatePicker(
            on_change=self.update_end_date,
            first_date=datetime(2020, 1, 1),
            last_date=datetime(2030, 12, 31)
        )
        
        # Add date pickers to the page
        self.page.overlay.extend([self.start_date_dialog, self.end_date_dialog])
        
        # Create date input fields
        self.start_date = ft.TextField(
            label="Start Date",
            width=220,
            height=50,
            read_only=True,
            border_color=ft.colors.BLUE_400,
            text_style=ft.TextStyle(font_family="Consolas", size=14),
            label_style=ft.TextStyle(color=ft.colors.BLUE_600, weight=ft.FontWeight.W_500, size=14),
            content_padding=ft.padding.only(left=12, right=12, top=8, bottom=8),
            border_radius=6,
            dense=True
        )
        
        self.end_date = ft.TextField(
            label="End Date",
            width=220,
            height=50,
            read_only=True,
            border_color=ft.colors.BLUE_400,
            text_style=ft.TextStyle(font_family="Consolas", size=14),
            label_style=ft.TextStyle(color=ft.colors.BLUE_600, weight=ft.FontWeight.W_500, size=14),
            content_padding=ft.padding.only(left=12, right=12, top=8, bottom=8),
            border_radius=6,
            dense=True
        )
        
        # Buttons with consistent styling
        button_style = ft.ButtonStyle(
            color=ft.colors.WHITE,
            bgcolor=ft.colors.BLUE_400,
            padding=ft.padding.symmetric(horizontal=16, vertical=10),
            shape=ft.RoundedRectangleBorder(radius=6)
        )
        
        self.start_date_btn = ft.ElevatedButton(
            "Select Start",
            icon=ft.icons.CALENDAR_TODAY,
            on_click=lambda _: self.start_date_dialog.pick_date(),
            style=button_style,
            height=40
        )
        
        self.end_date_btn = ft.ElevatedButton(
            "Select End",
            icon=ft.icons.CALENDAR_TODAY,
            on_click=lambda _: self.end_date_dialog.pick_date(),
            style=button_style,
            height=40
        )
        
        self.execute_btn = ft.ElevatedButton(
            self.button_text,
            icon=ft.icons.PLAY_ARROW,
            on_click=self.execute_action,
            style=ft.ButtonStyle(
                color=ft.colors.WHITE,
                bgcolor=ft.colors.GREEN_500,
                padding=ft.padding.symmetric(horizontal=24, vertical=12),
                shape=ft.RoundedRectangleBorder(radius=6)
            ),
            width=220,
            height=44
        )
        
        # Create input rows with consistent spacing
        input_row_style = {
            "spacing": 12,
            "alignment": ft.MainAxisAlignment.CENTER,
            "vertical_alignment": ft.CrossAxisAlignment.CENTER,
            "height": 60
        }
        
        # Main container with proper constraints
        super().__init__(
            controls=[
                # Header
                ft.Container(
                    content=ft.Text(
                        test_name,
                        size=15,
                        weight=ft.FontWeight.W_600,
                        color=ft.colors.BLUE_GREY_800,
                        text_align=ft.TextAlign.CENTER
                    ),
                    padding=ft.padding.only(bottom=12, top=8),
                    alignment=ft.alignment.center,
                    width=480
                ),
                
                # Start date row
                ft.Container(
                    ft.Row(
                        [
                            self.start_date,
                            self.start_date_btn,
                        ],
                        spacing=12,
                        alignment=ft.MainAxisAlignment.CENTER,
                        vertical_alignment=ft.CrossAxisAlignment.CENTER,
                        height=60
                    ),
                    padding=ft.padding.symmetric(vertical=4)
                ),
                
                # End date row
                ft.Container(
                    ft.Row(
                        [
                            self.end_date,
                            self.end_date_btn,
                        ],
                        spacing=12,
                        alignment=ft.MainAxisAlignment.CENTER,
                        vertical_alignment=ft.CrossAxisAlignment.CENTER,
                        height=60
                    ),
                    padding=ft.padding.symmetric(vertical=4)
                ),
                
                # Execute button
                ft.Container(
                    self.execute_btn,
                    padding=ft.padding.only(top=12, bottom=12),
                    alignment=ft.alignment.center
                )
            ],
            spacing=0,
            horizontal_alignment=ft.CrossAxisAlignment.CENTER
            # width=480,  # Removed to allow parent to dictate width
            # expand=True # Removed to allow parent to dictate expansion
        )
    
    def update_start_date(self, e):
        if self.start_date_dialog.value:
            self.start_date.value = self.start_date_dialog.value.strftime("%Y-%m-%d")
            self.update()
    
    def update_end_date(self, e):
        if self.end_date_dialog.value:
            self.end_date.value = self.end_date_dialog.value.strftime("%Y-%m-%d")
            self.update()
    
    async def execute_action(self, e):
        if not self.start_date.value or not self.end_date.value:
            await self.page.show_snack_bar(
                ft.SnackBar(
                    ft.Text("Please select both start and end dates"),
                    open=True,
                    bgcolor=ft.colors.RED_400
                )
            )
            return
            
        print(f"Test: {self.test_name}")
        print(f"Start Date: {self.start_date.value}")
        print(f"End Date: {self.end_date.value}")
        print("-" * 40)
        
        # Show a snackbar confirmation
        await self.page.show_snack_bar(
            ft.SnackBar(
                ft.Text(f"{self.test_name} executed with dates: {self.start_date.value} to {self.end_date.value}"),
                open=True,
                bgcolor=ft.colors.GREEN_500,
                behavior=ft.SnackBarBehavior.FLOATING,
                margin=ft.margin.all(20),
                shape=ft.RoundedRectangleBorder(radius=8)
            )
        )
