class mylist():
    def __init__(self,list_name:list) -> None:
        self._list_=list_name
        self._rowDict_=self._returnRowDcit_()
        self._colDict_=self._returnColDcit_()
    def _returnRowDcit_(self):
        d={}
        for i in range(len(self._list_)):
            d[self._list_[i][0]]=i
        return d
    
    def _returnColDcit_(self):
        d={}
        for i in range(len(self._list_[0])):
            d[self._list_[0][i]]=i
        return d
    
    def getValueByName(self,rowName:str,colName:str):
        if rowName not in self._rowDict_ or colName not in self._colDict_:
            return None
        else:
            return self._list_[self._rowDict_[rowName]][self._colDict_[colName]]
        
    def getValueByRowCol(self,row:int,col:int):
        if row not in range(len(self._list_)) or col not in range(len(self._list_[0])):
            return None
        else:
            return self._list_[row][col]
    
    def append(self,row:list): #只能按行追加，且必须同大小
        self._list_.append(row)
        self._rowDict_[row[0]]=len(self._list_)-1
   