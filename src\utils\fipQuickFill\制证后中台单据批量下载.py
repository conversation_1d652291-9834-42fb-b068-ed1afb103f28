
from playwright.sync_api import Playwright, sync_playwright
import time
import src.utils.cscec as cscec
import src.utils.fipQuickFill.downloadAttachment as downloadAttachment
import src.base.settings as settings
thePath=settings.PATH_FIP_DOWNLOAD
def autodownload():
    with sync_playwright() as playwright:
        browser = playwright.chromium.connect_over_cdp("http://localhost:9222")
        default_context = browser.contexts[0]
        page = cscec.switch_to_page(default_context,"中国建筑")
        table=page.locator("//div[contains(text(),'填单日期')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table/tbody[2]//tr")
        from datetime import datetime # 获取当前日期
        current_date = datetime.now()
        formatted_date = current_date.strftime("%Y-%m-%d") # 格式化日期
        set_needdownload=set()
        for i in range(table.count()):
            element = table.nth(i).locator("//td[2]")
            background_color = element.evaluate('(element) => window.getComputedStyle(element).getPropertyValue("background-color")')
            if background_color=="rgb(193, 221, 241)":
                set_needdownload.add(table.nth(i).locator("//td[5]/div").text_content())          
        for i in range(table.count()):
            s=table.nth(i).locator("//td[5]/div").text_content()
            if s in set_needdownload:
                print(s+"开始")
                flowCode=table.nth(i).locator("//td[18]/div").text_content()
                reasons=table.nth(i).locator("//td[10]/div").text_content()
                table.nth(i).locator("//td[5]").click()
                time.sleep(0.2)
                path=thePath+"/单据附件/"+formatted_date+"/"
                path2=path+s+"a流程单.pdf"
                downloadAttachment.download(page,s,path,reasons)   
                downloadAttachment.pdf_wirte(path2,flowCode)
        print("下载完成")
  