import src.utils.cscec as cscec
import src.utils.Browser.Browser as browser
import src.utils.Excel.excel as excel
import src.utils.Excel.openpyxlExcel as openpyxlExcel

def autofill():
    ws=excel.myBook().sheet("收入成本表")
    b=int(ws.Cells(1,2).Value)+1
    table=ws.table("表1")
    #ws.UsedRange.Rows.Count+1
    browser = playwright.chromium.connect_over_cdp("http://localhost:9222")
    default_context = browser.contexts[0]
    page = default_context.pages[0]
    page = switch_to_page(default_context, title="中建三局", url=None)
    for i in range(b,ws.MaxRow):
        print("开始序号"+str(i-1)+"行")
        if table.getValue(i,"是否写入")=="是":
            page.click("//button[@class='ant-btn ant-btn-primary list-action-add']") #点击新增收入成本表
            frame=page.frame_locator("//*[@id='portal-form-list-container']/div[3]/iframe")
            
            
            frame.locator("//*[@id='quarter']/div[2]/div/div/div/div/div").click()
            frame.get_by_role("option", name=table.getValue(i,"季度")).click() #选择季度
            time.sleep(1)
            frame.locator("//*[@id='quarter']/div[2]/div/div/div/div/div").click()
            frame.get_by_role("option", name=table.getValue(i,"季度")).click() #选择季度
            time.sleep(1)
                                
            frame.locator("input[placeholder='请选择'][type='text']").click()
            frame.locator("//div[@id='projectName']//div[@class='field__control']//div//div[@class='text']//div//input[@placeholder='请输入']").fill(table.getValue(i,"财商名称"))
            frame.locator("//div[@class='ant-modal-body']//button[2]").click()
            frame.get_by_text(table.getValue(i,"编码")).click() #注意编码
            frame.locator("//div[@class='ant-modal-footer']//button[2]").click()
            frame.locator("//button[@class='ant-btn ant-btn-default getData']").click()
            frame.locator("//button[@class='ant-btn ant-btn-default btn3']").click() #商务取数
            time.sleep(3)

            try:
                frame.locator("//div[@title='指派人']/parent::div/following-sibling::div").click(timeout=5000)
                frame.locator("//input[@placeholder='搜索组织、姓名']").fill(table.getValue(i,"指派人"))
                frame.locator("//i[@aria-label='图标: search']").click()
                frame.get_by_text(table.getValue(i,"指派人")).first.click()
                cscec.getVisible(frame,"//div[contains(text(),'选择审批人')]/parent::div/parent::div//span[contains(text(),'确 定')]").click()
            except:
                pass
            if table.getValue(i,"附件地址")!=None:
                with page.expect_file_chooser() as fc_info:
                    frame.get_by_text("上传").click()
                    file_chooser = fc_info.Value
                    file_chooser.set_files(table.getValue(i,"附件地址"))
            frame.locator("//button[@class='ant-btn ant-btn-default btn4']").click()
            #frame.locator("//button[@class='ant-btn ant-btn-default btn1']").click()
            try:
                frame.get_by_text("校验结果")
                
                frame.locator("//div[@class='form-body']//button[3]").click(timeout=5000)
                #frame.locator("//button[@class='ant-btn ant-btn-primary']").click(timeout=5000)
            except:
                print("无误")
            time.sleep(1)
        ws.Cells(1,2).Value=ws.Cells(1,2).Value+1


