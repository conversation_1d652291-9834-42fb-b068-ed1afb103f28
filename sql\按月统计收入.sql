
SELECT 
    DATE_TRUNC('month', 过帐日期) AS month,        -- 提取月份
    SUM(case when 总账科目长文本 like '主营业务收入%' then 带符号的本位币金额 else 0 end) AS monthly_income,                     -- 当月收入
    SUM(SUM(case when 总账科目长文本 like '主营业务收入%' then 带符号的本位币金额 else 0 end)) OVER (ORDER BY DATE_TRUNC('month', 过帐日期)) AS 累计收入,
    CASE EXTRACT(MONTH FROM DATE_TRUNC('month', 过帐日期))
    WHEN 1 THEN '一月'
    WHEN 2 THEN '二月'
    WHEN 3 THEN '三月'
    WHEN 4 THEN '四月'
    WHEN 5 THEN '五月'
    WHEN 6 THEN '六月'
    WHEN 7 THEN '七月'
    WHEN 8 THEN '八月'
    WHEN 9 THEN '九月'
    WHEN 10 THEN '十月'
    WHEN 11 THEN '十一月'
    WHEN 12 THEN '十二月'
  END AS 中文月份
FROM 明细帐 where 过帐日期 > '2025-01-01'
GROUP BY DATE_TRUNC('month', 过帐日期)
ORDER BY month;


SELECT 
    DATE_TRUNC('month', 过帐日期) AS month,        -- 提取月份
    SUM(SUM(case when 总账科目长文本 like '合同资产%' then 带符号的本位币金额 else 0 end)) OVER (ORDER BY DATE_TRUNC('month', 过帐日期)) AS 累计合同资产
FROM 明细帐 
GROUP BY DATE_TRUNC('month', 过帐日期)
ORDER BY month;