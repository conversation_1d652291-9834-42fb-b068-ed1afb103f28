
from playwright.sync_api import Playwright, sync_playwright
import time
import src.utils.cscec as cscec
import src.utils.Browser.Browser as browser
import src.base.settings as settings
import duckdb
import pandas as pd
import threading


class cachePlan():
    def __init__(self):
        self.db=duckdb.connect(settings.PATH_DUCKDB+"\\fund.duckdb")
        self._jsText='''
        js_need=>{var spread = GC.Spread.Sheets.findControl(js_need);
        sheet=spread.getSheet(0);
        var rowCount = sheet.getRowCount(); // 获取最大行数
        var colCount = sheet.getColumnCount(); // 获取最大列数
        var arr = new Array(rowCount);


        for (var i = 0; i < rowCount; i++) { // 遍历行
        arr[i] = new Array(colCount);
        for (var j = 0; j < colCount; j++) { // 遍历列
            var cellValue = sheet.getValue(i, j); // 获取单元格的值
            arr[i][j]=(cellValue);// 将单元格的值存入数组
        }
        }
        return arr;}
        '''
        self._creatTable()

    def _creatTable(self):
        self.db.execute('''
            CREATE TABLE IF NOT EXISTS 资金计划流出(
            项目名称 varchar,
            序号 varchar,
            合同编码 varchar,
            合同名称 varchar,
            供应商编码 varchar,
            供应商名称 varchar,
            支出类型 varchar,
            合同金额 double,
            合同付款比例 double,
            累计结算金额 double,
            当月预计结算金额 double,
            累计已收发票金额 double,
            含预付款累计已付金额 double,
            预付款 double,
            当月新增计划付款金额 double,
            上月已占用未使用金额流程未彻底走完 double,
            备注 varchar,
            批次 varchar,  )           
            '''
            )
        self.db.execute('''
            CREATE TABLE IF NOT EXISTS 资金计划流入(
            项目名称 varchar,
            收款金额 double,
            批次 varchar,)            
            '''
            )
    
    def getAllproject(self):
        adict={}
        arow=self.db.execute("SELECT distinct 项目名称 FROM 资金计划流出").fetchall()
        for row in arow:
            adict[row[0]]="已下载"
        return adict
    def _clear(self):
        self.db.execute("TRUNCATE 资金计划流出")
        self.db.execute("TRUNCATE 资金计划流入")
        self.db.execute('VACUUM')
    def importToDBIn(self,arr,projectName,batch):
        collection=0
        for row in arr:
            if row[7]=="CNY":
                collection=row[28]+collection
        #向duckdb插入两个数据
        self.db.execute("INSERT INTO 资金计划流入 VALUES (?,?,?)",(projectName,collection,batch))
    def importToDBOut(self,arr,projectName,batch):
        newArr=[]
        k=1
        for row in arr:
            if row[7]=="CNY":
                newRow=[projectName,k,
                    row[1], # 合同编码
                    row[3], # 合同名称
                    row[4], # 供应商编码
                    row[5], # 供应商名称
                    row[6], # 支出类型
                    row[8], # 合同金额
                    row[9], # 合同付款比例
                    row[10], # 累计结算金额
                    row[11], # 当月预计结算金额
                    row[13], # 累计已收发票金额
                    row[15], # 含预付款累计已付金额
                    row[16], # 预付款
                    row[31], # 当月新增计划付款金额
                    row[37], # 上月已占用未使用金额流程未彻底走完
                    row[50], # 备注
                    batch #批次
                    ]
                k=k+1
                newArr.append(newRow)
        if len(newArr)>0:
            df=pd.DataFrame(newArr)
            self.db.execute("INSERT INTO 资金计划流出 SELECT * FROM df")

    def _downloadPlanA_(self,cookies,cscecurl,lock:threading.Lock,dictProject:dict,batchPara,statePara):
        playwright1 = sync_playwright().start()
        browser1 = playwright1.chromium.launch(channel="msedge",headless=False)
        newContext1 = browser1.new_context()
        page=newContext1.new_page()
        newContext1.add_cookies(cookies)
        page.goto(cscecurl)
        if  False: #保留上面的入口
            cscec.toFunction(page,"司库系统","资金预算","项目资金计划汇总")
            cscec.getVisible(page,"//div[text()='每页']/following-sibling::input[1]").fill("300")
            cscec.getVisible(page,"//div[text()='每页']/preceding-sibling::div[6]").click()
        else:
            cscec.toFunction(page,"司库系统","资金预算","编制计划情况查询")
            page.locator("//span[contains(text(),'-')]/parent::div/parent::div/parent::td/div[1]").first.click()#起等待作用
            tdNext=page.locator("//span[contains(text(),'-')]/parent::div/parent::div/parent::td/div")
            loopCount=tdNext.count()
            print("顶点单位数量"+str(loopCount))
        for orgI in range(loopCount):
            tdNext.nth(orgI).click()
            time.sleep(1)
            cscec.getVisible(page,"//span[text()='查询']").click()
            time.sleep(4)
            #这一块保留没有管理节点的人使用，他这个style直接使用base64编码图片在属性里面，所以这样定位
            #orgEle=page.locator("//img[contains(@style,'M30KpaMQxcwAAAAAElFTkSuQmCC')]")
            #for orgI in range(0,orgEle.count()):
            #time.sleep(4)
            #orgEle.nth(orgI).click()
            table=cscec.cscecTable(page,"项目编号")
            table.reIndex()
            print(f"本区域数量{table.count}")
            for i in range(1,table.count+1):
                projectName=table.getValue(i,"项目名称")
                batch=table.getValue(i,"上报批次")
                state=table.getValue(i,"上报状态")

                #下面是条件
                if batchPara=="所有批次":
                    next1=True
                elif batch==batchPara:
                    next1=True
                else:
                    next1=False
                if statePara=="所有状态":
                    next2=True
                elif state==statePara:
                    next2=True
                elif state!="结束" and statePara=="流程中":
                    next2=True
                else:
                    next2=False

                with lock:
                    if projectName+batch not in dictProject:
                        ifnext=True
                        dictProject[projectName+batch]="已处理"
                    else:
                        ifnext=False
                if ("机关" not in projectName) and ifnext and next1 and next2:
                    print(f"剩余序号{table.count-i}")
                    table.doubleClick(i,"项目名称")
                    js_need=cscec.getVisible(page,"//div[@gcuielement='gcSpread']") #获取spreadjs表格
                    systemArr=js_need.evaluate(self._jsText)
                    page.locator("//span[contains(text(),'项目流出表')]/parent::li").click()
                    with lock:
                        self.importToDBIn(systemArr,projectName,batch)
                        dictProject[projectName+batch]="已下账"
                    js_need=cscec.getVisible(page,"//div[@gcuielement='gcSpread']") #
                    systemArr=js_need.evaluate(self._jsText)
                    with lock:
                        self.importToDBOut(systemArr,projectName,batch)
                        dictProject[projectName+batch]="已下账"
        cscec.closeTab(page)
    def dPlanThreeThread(self,batchPara,statePara,ifresume):
        B=browser.myBrowser("cscec")
        page0=B.page
        default_context=B.Context
        cookies = default_context.cookies()
        default_context.close()
        cscecurl=f"https://iam.cscec.com/cas/login?service=https%3A%2F%2Ffip.cscec.com%2FOSPPortal%2Fcallback"
        lock= threading.Lock()
        threads = []
        if ifresume:
            self._clear()
            dictProject={}
        else:
            dictProject=self.getAllproject()
        for i in range(2):
            print("持续运行")
            t1 = threading.Thread(target=self._downloadPlanA_,args=(cookies,cscecurl,lock,dictProject,batchPara,statePara))
            t1.setDaemon(True)
            t1.start()
            threads.append(t1) #线程保活，不然自动关闭啦
        for t in threads:
            t.join()
    def ToExcel(self):
        df2=self.db.execute("select * from 资金计划流出").df()
        df1=self.db.execute("select * from 资金计划流入").df()
        with pd.ExcelWriter(settings.PATH_EXCEL+"/资金计划.xlsx", engine='openpyxl') as writer:
            df1.to_excel(writer, sheet_name='资金计划流入', index=False)
            df2.to_excel(writer, sheet_name='资金计划流出', index=False)
