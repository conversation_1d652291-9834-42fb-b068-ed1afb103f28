import sys
import os
from playwright.sync_api import Page
from playwright.sync_api import ChromiumBrowserContext
import time
import cscec

def autoOne(page:Page,d: dict):                
    page.locator("//span[text()='税务系统']/parent::span/parent::li").click()
    page.locator("//span[contains(text(),'发票管理')]").click()
    page.locator("//span[contains(@class,'txt')][text()='发票收票登记单']").click() 
    page.locator("//span[text()='新建蓝票收票单']/parent::div").click()

    page.locator("//input[@id='FormTextInput5-input']").fill(d["事由"])
    s="//label[contains(text(),'合同编号')]/parent::div/following-sibling::div[1]/div/div/div"
    page.locator(s).first.click()
    page.get_by_placeholder("请输入查询合同编号关键字").fill(d["合同编号"].replace(";",""))
    page.locator("//*[text()='合同帮助']/parent::div/parent::div/parent::div//span[contains(text(),'查询')]/parent::div/parent::div").click()
    page.locator("//*[text()='合同帮助']/parent::div/parent::div/parent::div//*[text()='"+d["合同编号"].replace(";","")+"']").first.dblclick()
    page.locator("//span[text()='影像']/parent::div/parent::div/parent::div/parent::div").click()
    page.locator("//span[text()='发票标签']/parent::li").click()
    with page.expect_file_chooser() as fc_info:
        page.locator("//img[contains(@src,'https://fip.cscec.com/OSPPortal/GWTStandard/images/scan/localimage.png')]").click()
        file_chooser = fc_info.value
        file_chooser.set_files(d["发票路径合并"].split(";"))
    page.locator("//*[text()='识别查验日志信息']/parent::div/parent::div/parent::div//div[text()='退出']").click(timeout=70000)
    page.locator("//*[text()='票据影像']/preceding-sibling::div[1]//td[1]").click() #点击关闭

    page.locator("//span[text()='保存']/parent::div/parent::div/parent::div/parent::div").click()
    time.sleep(0.5)
    page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='确定']").click()
    page.locator("//span[text()='保存']/parent::div/parent::div/parent::div/parent::div/parent::div//span[text()='提交']/parent::div/parent::div/parent::div/parent::div").click() #通过保存来确定提交按钮
    page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='提交']").click()
    page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='确定']").click()
    page.locator("//div[@class='ant-tabs-tab ant-tabs-tab-with-remove ant-tabs-tab-active']//span[@aria-label='close']//*[name()='svg']//*[name()='path' and contains(@d,'M563.8 512')]").click() #关闭结算单
    page.locator("//button[@aria-label='remove']//span[@aria-label='close']//*[name()='svg']//*[name()='path' and contains(@d,'M563.8 512')]").click()



def autoOperate():
    import excelto
    context=cscec.getContext("9222")
    page=cscec.switch_to_page(context,title="中国建筑财务一体化")
    print(page.title)
    ws=excelto.toWorksheet("发票推送系统","发票上报")
    start_number=int(ws.Cells(1,2).Value)
    max_number=excelto.toRowCount(ws,"表2")
    project_judge=""
    for i in range(start_number+1,max_number+1):  #跳过标题行+1
        d=excelto.toDict(ws,"表2",i)
        if project_judge!=d["项目名称"]:
            cscec.changeProjectCscec(page,d["组织机构"],d["项目名称"])
        autoOne(page,d)
        project_judge=d["项目名称"]
        ws.Cells(1,2).Value=ws.Cells(1,2).Value+1
    
if __name__=='__main__':
    ui.runAuto(autoOperate)
