import time
import sys
import os
import pythoncom
import ctypes
import threading
import multiprocessing
from multiprocessing import  Queue

import src.Gui.processClass 
import src.Gui.allFuntion as thisFuntion
import src.Gui.register
import src.base.settings as settings
import src.base.cache as cache


shared_queue = Queue()
thisProcess=src.Gui.processClass.myProcess(thisFuntion.allfunction,shared_queue)
regInstance=src.Gui.register.RegisterDialog()

