import os
import re

def sanitize_filename(filename):
    # 移除文件扩展名
    base_name = os.path.splitext(filename)[0]
    # 替换非法字符为下划线
    sanitized_name = re.sub(r'\W+', '_', base_name)
    return sanitized_name

def convert_sql_to_py(directory, output_file):
    # 打开输出文件
    with open(output_file, 'w',encoding='utf-8') as py_file:
        # 遍历目录中的所有文件
        for filename in os.listdir(directory):
            if filename.endswith('.sql'):
                # 构建完整的文件路径
                file_path = os.path.join(directory, filename)
                
                # 读取SQL文件内容
                with open(file_path, 'r',encoding='utf-8') as sql_file:
                    sql_content = sql_file.read()
                
                # 生成变量名，使用文件名作为变量名
                variable_name = sanitize_filename(filename)
                
                # 将内容写入到py文件中
                py_file.write(f"{variable_name}='''{sql_content}'''\n\n")

if __name__ == "__main__":
    # 指定目录和输出文件
    directory = './sql'
    output_file = './src/utils/DB/outputSQL.py'
    
    # 调用函数进行转换
    convert_sql_to_py(directory, output_file)
    print(f"转换完成，结果已保存到 {output_file}")

#打开一个目录，读取所有的sql文件，将其内容写入到一个py文件中，变量名使用文件名作为变量名
#使用tkinter获取文件
