
import os
import sys

sys.path.append(".")
import src.utils.Excel.excel as excel
from python_calamine import CalamineWorkbook
from pathlib import Path

def get_title_col(inputList,title):
    for i in range(3):
        for j in range(len(inputList[i])):
            if inputList[i][j]==title:
                return j

def get_excel_data_main(folder_path, sheet_name,column):
    # 获取文件夹下所有Excel文件
    excel_files = [f for f in os.listdir(folder_path) if f.endswith('.xlsx') or f.endswith('.xls')]
    
    resultOrginal = []
    
    for file in excel_files:
        file_path = os.path.join(folder_path, file)
        workbook = CalamineWorkbook.from_path(file_path)
        sheet = workbook.get_sheet_by_name(sheet_name)
        
        if sheet:
            # 将单元格引用转换为行和列的索引
            data = sheet.to_python()
            dataCol=[data[i][column] for i in range(len(data))]
            file_name = Path(file).stem  # 获取不含后缀的文件名
            dataCol.insert(0,file_name)
            resultOrginal.append(dataCol)
    return list(zip(*resultOrginal))

def get_excel_data_Balance(folder_path, sheet_name,titleList,amountTitle,d):
    # 获取文件夹下所有Excel文件
    excel_files = [f for f in os.listdir(folder_path) if f.endswith('.xlsx') or f.endswith('.xls')]
    
    resultOrginal = []
    
    for file in excel_files:
        file_path = os.path.join(folder_path, file)
        workbook = CalamineWorkbook.from_path(file_path)
        theList = workbook.get_sheet_by_name(sheet_name).to_python()
        titleCol=[get_title_col(theList,title) for title in titleList]
        if type(amountTitle)==int:
            amountCol=amountTitle
        else:
            amountCol=get_title_col(theList,amountTitle)
        for i in range(len(theList)):
            #判断是否为文本整数
            if theList[i][0].lstrip('-').isdigit() and theList[i][0] != "" :
                key=tuple([theList[i][col] for col in titleCol].append(sheet_name))
                d[key]=theList[i][amountCol]+d.get(key,0)
    return d
def getBalance():
    d={}
    folder_path = "D:\数字公司总部\报表部分\集团账套23全\子表"
    get_excel_data_Balance(folder_path, "NBWL2004 内部往来-应收账款",["对方单位名称","科目","填报单位"],"期末余额",d)
    outputList1=[]
    outputList2=[]
    for key in d:
        outputList1.append(key)
        outputList2.append([d[key]])
    theOutputSheet=excel.myBook("输出表").sheet("差额表重构")
    theOutputSheet.Range("a2").resize(len(outputList1),len(outputList1[0])).Value=outputList1
    theOutputSheet.Range(2,len(outputList1[0])+1,2,len(outputList1[0])+1).resize(len(outputList2),len(outputList2[0])).Value=outputList2


def fillmainTable():
    folder_path = "D:\数字公司总部\报表部分\财务中报--年报0218"# 指定文件夹路径
    getdata = get_excel_data_main(folder_path, "ZB2007 资产负债表账面_管理",2)
    theOutputSheet=excel.myBook("资产查询表").sheet("资产表账面管理")
    theOutputSheet.Range("b1").resize(len(getdata),len(getdata[0])).Value=getdata

    getdata = get_excel_data_main(folder_path, "ZB2007 资产负债表账面_管理",7)
    theOutputSheet=excel.myBook("资产查询表").sheet("负债表账面管理")
    theOutputSheet.Range("b1").resize(len(getdata),len(getdata[0])).Value=getdata

#fillmainTable()

