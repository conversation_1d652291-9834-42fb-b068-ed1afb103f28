import duckdb 
import src.base.settings as settings
import src.utils.Excel.excel as excel
import src.utils.sapPublic.sapExport
import src.utils.DB.readtxttolist
import src.utils.DB.outputSQL as Sql
import pandas as pd
import pywintypes
import datetime

# 遍历dfList并确保所有时间戳都是datetime对象


def downloadFromDb():
    #将df转为一个二维list
    wb=excel.myBook()
    ws=wb.sheet("查询结果")
    needReplace=wb.sheet("导入模板清单").Cells(1,7).Value
    sql2=Sql.账期查询.replace("替换日期",needReplace)
    con = duckdb.connect(database=settings.PATH_DUCKDB+'/example.duckdb', read_only=True)

    df= con.execute(sql2).df(date_as_object=False)
    df['起算日期'] = df['起算日期'].dt.strftime('%Y-%m-%d')  # 指定您需要的日期格式
    df['到期日'] = df['到期日'].dt.strftime('%Y-%m-%d')  # 指定您需要的日期格式
    dfList=[df.columns.to_list()]+df.values.tolist()
    ws.deleteUsedRange(2)
    ws.Range("a1").resize(len(dfList),len(dfList[0])).Value=dfList

def dateChange():
    import os
    import src.utils.Excel.excel as excel
    import src.base.settings as settings
    import src.utils.sapPublic.GetSAPSession as GetSAPSession

    wb=excel.myBook()
    ws=wb.sheet("导入模板清单")
    session = GetSAPSession.creatSAP()
    session.StartTransaction("ZGL0033")
    session.findById("wnd[0]/usr/cmbP_GZLX").Key = "B02"
    for i in range(2, ws.MaxRow):
        if ws.Cells(i,2).Value=="是":
                try:
                    session.findById("wnd[0]/usr/ctxtP_FILE").SetFocus()
                    session.findById("wnd[0]/usr/ctxtP_FILE").caretPosition = 0
                    session.findById("wnd[0]").sendVKey(4)
                    session.findById("wnd[1]/usr/ctxtDY_PATH").SetFocus()
                    session.findById("wnd[1]/usr/ctxtDY_PATH").caretPosition = 0
                    session.findById("wnd[1]").sendVKey(4)
                    session.findById("wnd[2]/usr/ctxtDY_PATH").Text = wb.path + "\模板"
                    session.findById("wnd[2]/usr/ctxtDY_FILENAME").Text = ws.Cells(i,1).Value +".xlsx"
                    session.findById("wnd[2]/usr/ctxtDY_FILENAME").caretPosition = 74
                    
                    try:
                        session.findById("wnd[2]/tbar[0]/btn[0]").press()
                        session.findById("wnd[1]/tbar[0]/btn[0]").press()
                        session.findById("wnd[0]/tbar[1]/btn[8]").press()
                    except:
                        pass
                    
                    session.findById("wnd[0]/usr/cntl9000_CONT/shellcont/shell").setCurrentCell(-1, "")
                    session.findById("wnd[0]/usr/cntl9000_CONT/shellcont/shell").SelectAll()
                    session.findById("wnd[0]/usr/cntl9000_CONT/shellcont/shell").pressToolbarButton("POST")
                    session.findById("wnd[0]/tbar[0]/btn[3]").press()
                    ws.Cells(i,2).Value= "成功"
                except Exception as e:
                    session.findById("wnd[0]/usr/cntl9000_CONT/shellcont/shell").selectColumn("ZMSG")
                    errorResult = session.findById("wnd[0]/usr/cntl9000_CONT/shellcont/shell").getCellValue(1, "ZMSG") #注意这一块未修正
                    ws.Cells(i,2).Value= errorResult #写入失败原因
                    session.StartTransaction("ZGL0033")
                    session.findById("wnd[0]/usr/cmbP_GZLX").Key = "B02"

    print("完成任务")