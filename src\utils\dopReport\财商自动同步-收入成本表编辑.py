import os
import sys

def initRuntimeEnvironment(startup_script):
    """初始化运行环境。startup_script: 启动脚本路径"""

    import site
    # 定义一个最简单的消息弹窗
    # 初始化工作目录和Python搜索路径
    script = os.path.abspath(startup_script)  # 启动脚本.py的路径
    home = os.path.dirname(script)  # 工作目录
    os.chdir(home)  # 重新设定工作目录（不在最顶层，而在UmiOCR-data文件夹下）
    for n in ['.', '.site-packages']:  # 将模块目录添加到 Python 搜索路径中
        path = os.path.abspath(os.path.join(home, n))
        if os.path.exists(path):
            site.addsitedir(path)


initRuntimeEnvironment(__file__)  # 初始化运行环境
sys.path.append("..") 
from playwright.sync_api import Playwright, sync_playwright
import win32com.client
import time
import pythoncom


def switch_to_page(context, title=None, url=None):
    """切换到指定title 名称 或 url 的 标签页"""
    for item_page in context.pages:
        if title:
            if title in item_page.title():
                # 激活当前选项卡
                item_page.bring_to_front()
                return item_page
        elif url:
            if url in item_page.url:
                # 激活当前选项卡
                item_page.bring_to_front()
                return item_page
    else:
        print("not found title or url")
    return context.pages[0]

def autofill():
    try:
        et = win32com.client.Dispatch("Excel.Application")
        wb=et.ActiveWorkbook
        ws=wb.Worksheets("收入成本表")
    except:
        et = win32com.client.Dispatch("Ket.Application")
        wb=et.ActiveWorkbook
        ws=wb.Worksheets("收入成本表")
    b=int(ws.Cells(1,2).Value)+4
    #ws.UsedRange.Rows.Count+1
    with sync_playwright() as playwright:
        browser = playwright.chromium.connect_over_cdp("http://localhost:9222")
        default_context = browser.contexts[0]
        page = default_context.pages[0]
        page = switch_to_page(default_context, title="中建三局", url=None)
        for i in range(b,ws.UsedRange.Rows.Count+1):
                print("开始序号"+str(i-4)+"行")
                if ws.Cells(i,2).Value=="是":
                    page.locator("//*[@id='ApplicationList']/div[1]/div[2]/div[1]/div[1]/span/i").click()
                    time.sleep(1)
                    page.locator("//*[@id='ApplicationList']/div[1]/div[2]/div[1]/div[1]/i").click()
                    page.locator("//*[@id='project']/div[2]/div/div/div/input").fill(ws.Cells(i,4).Value)
                    page.locator("//span[text()='查 询']/parent::button").click()
                    page.locator("//div[@class='table-container row-selectable']//div[1]//div[5]//div[1]").click()
                    frame=page.frame_locator("//*[@id='portal-form-list-container']/div[3]/iframe")             
                    frame.locator("//span[text()='编 辑']/parent::button").click()
                    frame.locator("//div[@id='totalPaymentOfThisPeriod']//div[@class='field__control']//div//input[@placeholder='财务人员填写']").click()
                    frame.locator("//div[@id='totalPaymentOfThisPeriod']//div[@class='field__control']//div//input[@placeholder='财务人员填写']").fill(str(ws.Cells(i,29).Value))  #本期收款总额
                    
                    frame.locator("//div[@id='totalPayOfThisPeriod']//div[@class='field__control']//div//input[@placeholder='财务人员填写']").click()
                    frame.locator("//div[@id='totalPayOfThisPeriod']//div[@class='field__control']//div//input[@placeholder='财务人员填写']").fill(str(ws.Cells(i,30).Value))  #本期付款总额
                    
                    #frame.locator("//div[@id='promissoryBalanceOfPeriod']//div[@class='field__control']//div//input[@placeholder='财务人员填写']").click()
                    #frame.locator("//div[@id='promissoryBalanceOfPeriod']//div[@class='field__control']//div//input[@placeholder='财务人员填写']").fill(str(ws.Cells(i,6).Value))  #承兑票据余额
                    
                    frame.locator("//div[@id='internalFinanceOfThisPeriod']//div[@class='field__control']//div//input[@placeholder='财务人员填写']").click()
                    frame.locator("//div[@id='internalFinanceOfThisPeriod']//div[@class='field__control']//div//input[@placeholder='财务人员填写']").fill(str(ws.Cells(i,25).Value))  #内部借款余额
                    
                    frame.locator("//div[@id='externalFinanceOfThisPeriod']//div[@class='field__control']//div//input[@placeholder='财务人员填写']").click()
                    frame.locator("//div[@id='externalFinanceOfThisPeriod']//div[@class='field__control']//div//input[@placeholder='财务人员填写']").fill(str(ws.Cells(i,26).Value))  #供应链融资余额
                    
                    #frame.locator("//div[@id='factoringBalanceOfPeriod']//div[@class='field__control']//div//input[@placeholder='财务人员填写']").click()
                    #frame.locator("//div[@id='factoringBalanceOfPeriod']//div[@class='field__control']//div//input[@placeholder='财务人员填写']").fill(str(ws.Cells(i,5).Value))  #保理融资余额
                    frame.locator("button").filter(has_text="提 交").first.click()
                    try:
                        frame.get_by_text("校验结果")
                        frame.locator("//button[@class='ant-btn ant-btn-primary']").click()
                    except:
                        print("无误")
                    time.sleep(1)
                ws.Cells(1,2).Value=ws.Cells(1,2).Value+1
    print("完成任务")

import Gui.ui as ui
if __name__=='__main__':
    ui.runAuto(autofill) 