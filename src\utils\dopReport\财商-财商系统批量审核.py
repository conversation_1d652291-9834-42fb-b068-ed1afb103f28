
from playwright.sync_api import <PERSON><PERSON>, sync_playwright,Page
import src.utils.cscec as cscec
import src.utils.Browser.Browser as browser
import src.utils.Excel.excel as excel
import time
import re
def autoSubmit(page:Page,determinationValue):
    n=page.locator("//*[@id='MyUnfinishedWorkItem']/div[3]/div[3]/ul/li[1]").text_content()
    n=re.findall("\d+",n)[0]
    print(n)
    for i in range(1,int(n)+1):
        try:
            page.locator("//*[@id='MyUnfinishedWorkItem']/div[3]/div[2]/div[1]/div/div[2]/div/div[1]/div[2]/div/span").click()
            time.sleep(1)
            if determinationValue=="同意":
                page.frame_locator("//*[@id='app']/div/div[2]/div[2]/div/iframe").locator("//span[contains(text(),'同 意')]/parent::button").click()
            else:
                page.frame_locator("//*[@id='app']/div/div[2]/div[2]/div/iframe").locator("//span[contains(text(),'驳 回')]/parent::button").click()
                frame1=page.frame_locator("//*[@id='app']/div/div[2]/div[2]/div/iframe")
                frame1.locator("//span[contains(text(),'填写申请单')]").click()
                cscec.getVisible(frame1,frame1.get_by_text("确 定")).click()
        except:
            if determinationValue=="同意":
                page.frame_locator("//*[@id='app']/div/div[2]/div[2]/div/iframe").locator("//span[contains(text(),'同 意')]/parent::button").click()
            else:
                page.frame_locator("//*[@id='app']/div/div[2]/div[2]/div/iframe").locator("//span[contains(text(),'驳 回')]/parent::button").click()
                frame1=page.frame_locator("//*[@id='app']/div/div[2]/div[2]/div/iframe")
                frame1.locator("//span[contains(text(),'填写申请单')]").click()
                cscec.getVisible(frame1,frame1.get_by_text("确 定")).click()
            page.frame_locator("//*[@id='app']/div/div[2]/div[2]/div/iframe").locator("//*[@id='app']/div/div[1]/div[1]/span[1]/i").click()
def autoYes():
    wb=excel.myBook()
    ws=wb.sheet("特殊支付导入")
    Id=ws.Cells(1,6).Value
    password=ws.Cells(2,6).Value
    page=browser.myBrowser("3b",id,password).page
    p=1
    determinationValue="同意"
    while p>0:
        try:
            autoSubmit(page,determinationValue)
            p=-1
        except Exception as e:
            print(e)
            page.reload()
            page.locator("//*[@id='app']/div/div[2]/div[1]/aside/div/div/div[2]/ul/li[1]").click()
            p=1

    

