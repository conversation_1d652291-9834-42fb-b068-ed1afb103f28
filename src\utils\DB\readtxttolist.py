from datetime import datetime
import src.utils.Excel.openpyxlExcel as openpyxlExcel
def readTxt(path,title2):
    replaceTitleDict=openpyxlExcel.getDict("明细账标题映射清单")
    with open(path, 'r') as file:
        data = file.read()
    # Split the content into a list by line breaks
    lines = data.split('\n')

    arr=[]

    if lines[6][0]=="-":
        pair = lines[4] + lines[5] #第五行和第六行是标题
        elements = [elem.strip() for elem in pair.split('|')]

        title1={}
        arr.append(title2)
        for i in range(len(elements)):
            if replaceTitleDict.get(elements[i],0)!=0: #修正标题不一致
                elements[i]=replaceTitleDict[elements[i]]
            title1[elements[i]]=i

        determiningWhetherToContinue=1
        for key in title2[1:]:
            if title1.get(key,0)==0:
                print(f"缺少标题列{key}，需要修改代码完善标题或者导出数据有问题")
                print(path)
                determiningWhetherToContinue=-1

        if determiningWhetherToContinue>0:
            lines = lines[7:-1]
            # Check if the length of the list is even
            if len(lines) % 2 != 0:
                lines = lines[:-1]  # Remove the last element to make the list even

            # Divide the list into pairs and process each pair
            for i in range(0, len(lines), 2):
                pair = lines[i] + lines[i+1]
                elements = [elem.strip() for elem in pair.split('|')]

                #问题在于修改标题
                row=[]
                row.append(elements[title1["财年"]]+elements[title1["项目行数"]]+elements[title1["利润中心"]]+elements[title1["凭证编号"]])
                elements[title1["带符号的本位币金额"]]=elements[title1["带符号的本位币金额"]].replace(",","")
                if elements[title1["带符号的本位币金额"]][-1]=="-":
                    elements[title1["带符号的本位币金额"]]="-"+elements[title1["带符号的本位币金额"]][:-1]
                elements[title1["带符号的本位币金额"]]=float(elements[title1["带符号的本位币金额"]])
                elements[title1["过帐日期"]] = datetime.strptime(elements[title1["过帐日期"]], '%Y-%m-%d')
                elements[title1["输入日期"]] = datetime.strptime(elements[title1["输入日期"]], '%Y-%m-%d')
                for key in title2[1:]:
                    row.append(elements[title1[key]])
                arr.append(row)
            return arr[1:] #插入数据库不用标题行
            #excelto.myWrokSheet("测试2").rangeResize("a1",len(arr),len(arr[0])).Value=arr
    else:
        pair = lines[4] + lines[5] + lines[6] #第五行和第六行是标题
        elements = [elem.strip() for elem in pair.split('|')]

        title1={}
        arr.append(title2)
        for i in range(len(elements)):
            if replaceTitleDict.get(elements[i],0)!=0:
                elements[i]=replaceTitleDict[elements[i]]
            #if elements[i] in title2:
            title1[elements[i]]=i

        determiningWhetherToContinue=1
        for key in title2[1:]:
            if title1.get(key,0)==0:
                print(f"缺少标题列{key}，需要修改代码完善标题或者导出数据有问题")
                print(path)
                determiningWhetherToContinue=-1

        if determiningWhetherToContinue>0:
            lines = lines[8:-1]
            # Check if the length of the list is even
            if len(lines) % 3 != 0:
                lines = lines[:-1]  # Remove the last element to make the list even

            # Divide the list into pairs and process each pair
            for i in range(0, len(lines), 3):
                pair = lines[i] + lines[i+1]+lines[i+2]
                elements = [elem.strip() for elem in pair.split('|')]

                #问题在于修改标题
                row=[]
                row.append(elements[title1["财年"]]+elements[title1["项目行数"]]+elements[title1["利润中心"]]+elements[title1["凭证编号"]])
                elements[title1["带符号的本位币金额"]]=elements[title1["带符号的本位币金额"]].replace(",","")
                if elements[title1["带符号的本位币金额"]][-1]=="-":
                    elements[title1["带符号的本位币金额"]]="-"+elements[title1["带符号的本位币金额"]][:-1]
                elements[title1["带符号的本位币金额"]]=float(elements[title1["带符号的本位币金额"]])
                elements[title1["过帐日期"]] = datetime.strptime(elements[title1["过帐日期"]], '%Y-%m-%d')
                elements[title1["输入日期"]] = datetime.strptime(elements[title1["输入日期"]], '%Y-%m-%d')
                for key in title2[1:]:
                    row.append(elements[title1[key]])
                arr.append(row)
            return arr[1:] #插入数据库不用标题行
            #excelto.myWrokSheet("测试2").rangeResize("a1",len(arr),len(arr[0])).Value=arr

def readTxtForAccountBalTable(path):
    titleList=["公司","公司名称","总账科目","总账科目长文本","利润中心组",
               "利润中心组名称","利润中心名称","利润中心","WBS元素","WBS元素描述",
               "客户","客户名称",
               "供应商","供应商名称",
               "税","税码描述",
               "合同",
               "认定类型","认定类型文本描述",
               "收支项目","收支项目 文本描述",
               "本期借方金额","本期贷方金额","本年累计借方金额","本年累计贷方金额","期初金额","期末余额","期末方向"]
    needModifyTitle=["本期借方金额","本期贷方金额","本年累计借方金额","本年累计贷方金额","期初金额","期末余额"]
    with open(path, 'r') as file:
        data = file.read()
    # Split the content into a list by line breaks
    lines = data.split('\n')
    arr=[]
    if lines[9][0]=="-":
        title=[elem.strip() for elem in lines[8].split('|')]
        for i in range(len(title)):
            if title[i]=="长文本":
                title[i]="总账科目长文本"
            if title[i]=="PC组":
                title[i]="利润中心组"
        titleIndex=[title.index(ele) for ele in needModifyTitle]
        titleIndex2=[title.index(ele) for ele in titleList]
        title2=[title[ele] for ele in titleIndex2]
        arr.append(title2)
        for rowI in range(9,len(lines)): #从第九行开始，第一行是标题:
            row=lines[rowI]
            try:
                if row[0]=="|":
                    data_row= [elem.strip() for elem in row.split('|')]
                    for titleI in titleIndex:
                        data_row[titleI]=data_row[titleI].replace(",","")
                        if data_row[titleI][-1]=="-":
                            data_row[titleI]="-"+data_row[titleI][:-1]
                        data_row[titleI]=float(data_row[titleI])
                    data_row2 = [data_row[ele] for ele in titleIndex2]
                    arr.append(data_row2)
            except Exception as e:
                pass
    else:
        pair = lines[8] + lines[9] #因为个别导出科目余额表也有两行
        title = [elem.strip() for elem in pair.split('|')]
        for i in range(len(title)):
            if title[i]=="长文本":
                title[i]="总账科目长文本"
            if title[i]=="PC组":
                title[i]="利润中心组"
        titleIndex=[title.index(ele) for ele in needModifyTitle]
        titleIndex2=[title.index(ele) for ele in titleList]
        title2=[title[ele] for ele in titleIndex2]
        arr.append(title2)
        lines = lines[11:-1]
        if len(lines) % 2 != 0:
            lines = lines[:-1]  
        for i in range(0, len(lines), 2):
            pair = lines[i] + lines[i+1]
            elements = [elem.strip() for elem in pair.split('|')]
            try:
                data_row= elements
                for titleI in titleIndex:
                    data_row[titleI]=data_row[titleI].replace(",","")
                    if data_row[titleI][-1]=="-":
                        data_row[titleI]="-"+data_row[titleI][:-1]
                    data_row[titleI]=float(data_row[titleI])
                data_row2 = [data_row[ele] for ele in titleIndex2]
                arr.append(data_row2)
            except Exception as e:
                pass
    return arr #插入数据库不用标题行

def readTxtForMDdata(path):  #用于主数据修正
    with open(path, 'r') as file:
        data = file.read()
    # Split the content into a list by line breaks
    lines = data.split('\n')
    if lines[5][0]!="-" and "列表中不包含任何数据" not in lines[5]:
        arr=[]
        title=[elem.strip() for elem in lines[3].split('|')]
        titleIndex=[title.index("项目编码"),title.index("利润中心"),title.index("项目所属的核算组织"),title.index("利润中心描述"),title.index("利润中心组描述")]
        title2=[title[ele] for ele in titleIndex]
        arr.append(title2)
        for rowI in range(4,len(lines)): #从第九行开始，第一行是标题:
            row=lines[rowI]
            try:
                if row[0]=="|":
                    data_row= [elem.strip() for elem in row.split('|')]
                    data_row2 = [data_row[ele] for ele in titleIndex]
                    arr.append(data_row2)
            except:
                pass
        return arr #插入数据库不用标题行
    
    elif "列表中不包含任何数据" not in lines[5]:
         pair = lines[3] + lines[4] #因为个别导出科目余额表也有两行
         title=[elem.strip() for elem in pair.split('|')]
         titleIndex=[title.index("项目编码"),title.index("利润中心"),title.index("项目所属的核算组织"),title.index("利润中心描述"),title.index("利润中心组描述")]
         title2=[title[ele] for ele in titleIndex]
         arr=[]
         arr.append(title2)
         lines = lines[6:-1]
         if len(lines) % 2 != 0:
             lines = lines[:-1]  # Remove the last element to make the list even
         for i in range(0, len(lines), 2):
             pair = lines[i] + lines[i+1]
             elements = [elem.strip() for elem in pair.split('|')]
             elements2 = [elements[ele] for ele in titleIndex]
             arr.append(elements2)
         return arr

def readTxtInerTransaction(path):
    titleList=["利润中心",
               "客商编码",
               "客商名称",
               "会计科目",
               "科目描述",
               "合同",
               "合同名称","WBS元素","WBS描述","本币金额",
               "行项目文本",
               "借贷标识","过帐日期","凭证编号","行项目"]
    
    needModifyTitle=["本币金额"]
    with open(path, 'r') as file:
        data = file.read()
    # Split the content into a list by line breaks
    if "列表中不包含任何数据 " not in data:
        lines = data.split('\n')
        arr=[]
        if lines[5][0]=="-":
            title=[elem.strip() for elem in lines[4].split('|')]
            for i in range(len(title)):
                if title[i]=="行项":
                    title[i]="行项目"
                if title[i]=="凭证号码":
                    title[i]="凭证编号"
            titleIndex=[title.index(ele) for ele in needModifyTitle]
            titleIndex2=[title.index(ele) for ele in titleList]
            title2=[title[ele] for ele in titleIndex2]
            arr.append(title2)
            for rowI in range(6,len(lines)): #从第九行开始，第一行是标题:
                row=lines[rowI]
                try:
                    if row[0]=="|":
                        data_row= [elem.strip() for elem in row.split('|')]
                        for titleI in titleIndex:
                            data_row[titleI]=data_row[titleI].replace(",","")
                            if data_row[titleI][-1]=="-":
                                data_row[titleI]="-"+data_row[titleI][:-1]
                            data_row[titleI]=float(data_row[titleI])
                        data_row2 = [data_row[ele] for ele in titleIndex2]
                        arr.append(data_row2)
                except Exception as e:
                    pass
        else:
            pair = lines[4] + lines[5] #因为个别导出科目余额表也有两行
            title = [elem.strip() for elem in pair.split('|')]
            for i in range(len(title)):
                if title[i]=="行项":
                    title[i]="行项目"
                if title[i]=="凭证号码":
                    title[i]="凭证编号"
            titleIndex=[title.index(ele) for ele in needModifyTitle]
            titleIndex2=[title.index(ele) for ele in titleList]
            title2=[title[ele] for ele in titleIndex2]
            arr.append(title2)
            lines = lines[7:-1]
            if len(lines) % 2 != 0:
                lines = lines[:-1]  
            for i in range(0, len(lines), 2):
                pair = lines[i] + lines[i+1]
                elements = [elem.strip() for elem in pair.split('|')]
                try:
                    data_row= elements
                    for titleI in titleIndex:
                        data_row[titleI]=data_row[titleI].replace(",","")
                        if data_row[titleI][-1]=="-":
                            data_row[titleI]="-"+data_row[titleI][:-1]
                        data_row[titleI]=float(data_row[titleI])
                    data_row2 = [data_row[ele] for ele in titleIndex2]
                    arr.append(data_row2)
                except Exception as e:
                    pass
        return arr #插入数据库不用标题行
