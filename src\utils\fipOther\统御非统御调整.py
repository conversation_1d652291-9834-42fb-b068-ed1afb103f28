import os
import sys
def initRuntimeEnvironment(startup_script):
    """初始化运行环境。startup_script: 启动脚本路径"""

    import site
    # 定义一个最简单的消息弹窗
    # 初始化工作目录和Python搜索路径
    script = os.path.abspath(startup_script)  # 启动脚本.py的路径
    home = os.path.dirname(script)  # 工作目录
    os.chdir(home)  # 重新设定工作目录（不在最顶层，而在UmiOCR-data文件夹下）
    for n in ['.', '.site-packages']:  # 将模块目录添加到 Python 搜索路径中
        path = os.path.abspath(os.path.join(home, n))
        if os.path.exists(path):
            site.addsitedir(path)


initRuntimeEnvironment(__file__)  # 初始化运行环境
sys.path.append("..") 

import cscec
import excelto

from playwright.sync_api import Playwright, sync_playwright
import win32com.client
import time
import pythoncom
def label_question(s):
    s="//label[contains(text(),'"+s+"')]/parent::div/following-sibling::div[1]/div/div/div"
    return s
def autofill():
    pythoncom.CoInitialize()
    try:
        et = win32com.client.Dispatch("Excel.Application")
        wb=et.ActiveWorkbook
        ws=wb.Worksheets("调整表")
    except:
        et = win32com.client.Dispatch("Ket.Application")
        wb=et.ActiveWorkbook
        ws=wb.Worksheets("调整表")
    ws=wb.Worksheets("调整表")
    table=excelto.table(wb,"调整表","表1")
    start_number=int(ws.Cells(1,2).Value)
    with sync_playwright() as playwright:
        browser = playwright.chromium.connect_over_cdp("http://localhost:9222")
        default_context = browser.contexts[0]
        page = cscec.switch_to_page(default_context,"中国建筑司库一体化")
        for i in range(start_number+1,table.max_row+1):
                print("开始第"+str(i-1)+"行")
                cscec.changeProjectCscec(page,table.value(i,"组织机构"),table.value(i,"项目名称"))
                cscec.toFunction(page,"报账系统","总账业务","统驭非统驭调整单")
                page.get_by_placeholder("事由不能超过").fill(table.value(i,"事由")) 

                s="业务大类："
                s=f"//label[contains(text(),'{s}')]/parent::div/following-sibling::div[1]/div/div/div"
                page.locator(s).click()
                page.get_by_placeholder("请输入查询关键字").fill("60200010")
                page.locator("//*[text()='帮助字典']/parent::div/parent::div/parent::div//span[contains(text(),'查询')]/parent::div/parent::div").click()
                page.get_by_text("60200010",exact=True).first.dblclick() 
                
                tr=cscec.getTr(page,"业务明细名称")
                
                a=tr.nth(0).locator("//td[2]")
                a.click()
                cscec.clickMouse(page,a,95,50) #点击问号
                page.get_by_placeholder("请输入查询关键字").fill("6401")
                page.locator("//*[text()='帮助字典']/parent::div/parent::div/parent::div//span[contains(text(),'查询')]/parent::div/parent::div").click()
                page.get_by_text("6401",exact=True).first.dblclick() 

                a=tr.nth(0).locator("//td[6]")
                a.click()
                time.sleep(0.3)
                cscec.clickMouse(page,a,95,50) #点击问号
                cscec.clickMouse(page,a,95,50) #点击问号
                cscec.getVisible(page,"//input[@placeholder='请输入客商编号或客商名称']").fill("100037178")
                page.locator("//img[contains(@src,'query')]/parent::div").click()
                page.locator("//img[contains(@src,'query')]/parent::div").click()
                page.get_by_text("100037178",exact=True).first.dblclick() #建设银行


                cscec.getVisible(page,"//div[contains(text(),'自定义选项')]").locator("//parent::span/parent::div/parent::td/preceding-sibling::td[4]").click()
                page.get_by_text("增加").click()
                tr=cscec.getTr(page,"项目")
                a=tr.nth(0).locator("//td[2]")
                a.click()
                time.sleep(0.3)
                cscec.getInputAbove(page,a).fill(table.value(i,"金额"))

                s="//div[contains(text(),'业务明细名称')]//parent::span/parent::div/parent::td/preceding-sibling::td[2]" #通过科目编号找到增加按钮
                page.locator(s).click()
                page.get_by_text("增加").click()
                
                tr=cscec.getTr(page,"业务明细名称")
                a=tr.nth(1).locator("//td[2]")
                a.click()
                cscec.clickMouse(page,a,95,50) #点击问号
                page.get_by_placeholder("请输入查询关键字").fill("6432")
                page.locator("//*[text()='帮助字典']/parent::div/parent::div/parent::div//span[contains(text(),'查询')]/parent::div/parent::div").click()
                page.get_by_text("6432",exact=True).first.dblclick() 

                a=tr.nth(1).locator("//td[4]")
                a.click()
                time.sleep(0.3)
                cscec.clickMouse(page,a,97,50) #点击问号
                cscec.clickMouse(page,a,97,50) #点击问号
                page.get_by_placeholder("请输入查询合同编号关键字").fill(table.value(i,"合同"))
                page.locator("//*[text()='合同帮助']/parent::div/parent::div/parent::div//span[contains(text(),'查询')]/parent::div/parent::div").click()
                page.get_by_text(table.value(i,"合同")).first.dblclick()

                cscec.getVisible(page,"//div[contains(text(),'到期日')]").locator("//parent::span/parent::div/parent::td/preceding-sibling::td[2]").click()
                page.get_by_text("增加").click()
                tr=cscec.getTr(page,"到期日")
                a=tr.nth(0).locator("//td[2]")
                a.click()
                time.sleep(0.3)
                cscec.getInputAbove(page,a).fill(table.value(i,"金额"))

                a=tr.nth(0).locator("//td[4]")
                a.dblclick()
                time.sleep(0.3)
                cscec.getInputAbove(page,a).fill(table.value(i,"日期"))
                page.locator("//span[text()='保存']/parent::div/parent::div/parent::div/parent::div").click()
                page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='确定']").click()
                page.locator("//span[text()='保存']/parent::div/parent::div/parent::div/parent::div/parent::div//span[text()='提交']/parent::div/parent::div/parent::div/parent::div").click() #通过保存来确定提交按钮
                page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='提交']").click()
                page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='确定']").click()

                cscec.closeTab(page)
                ws.Cells(1,2).Value=ws.Cells(1,2).Value+1
        print("完成任务")


import Gui.ui as ui
if __name__=='__main__':
    ui.runAuto(autofill)