import openpyxl
import src.base.settings as settings
import openpyxl
thePath=settings.PATH_CONFIG
def getDict(sheetName:str):
    wb = openpyxl.load_workbook(thePath+"/配置文件.xlsx")
    sheet = wb[sheetName]
    useDict = {}
    for row in sheet.iter_rows(min_row=1, values_only=True):
        if row[0] != None:
            key = row[0]  # 假设第一列作为键
            value = row[1]  # 假设第二列作为值
            useDict[key] = value
    return useDict

def getUsedList(sheetName:str):
    wb = openpyxl.load_workbook(thePath+"/配置文件.xlsx")
    sheet = wb[sheetName]
    useList = []
    for row in sheet.iter_rows(min_row=1, values_only=True):
        useList.append(list(row))
    return useList

def getmailList():
    path=thePath+"/邮件批量发送配置表.xlsx"
    wb = openpyxl.load_workbook(path,data_only=False)
    sheet = wb["配置表"]
    useList = []
    for row in sheet.iter_rows(min_row=1, values_only=True):
        useList.append(list(row))
    return useList

def getUsedListByPath(path:str):
    wb = openpyxl.load_workbook(path)
    sheet = wb.active
    useList = []
    for row in sheet.iter_rows(min_row=1, values_only=True):
        useList.append(list(row))
    return useList

def getUsedCenterGroup():
    wb = openpyxl.load_workbook(thePath+"/配置文件.xlsx")
    sheet = wb["sap配置"]
    useList = []
    startRow=sheet.max_row
    for i,row in enumerate(sheet.iter_rows(min_row=1, values_only=True)):
        startRow=i if row[0]=="所使用的利润中心组" else startRow
        if row[0] != None and i>startRow :
            useList.append(list(row))
    print(useList)
    return useList

def getUnitConditionsText():
    L=getUsedList("上级机关单位")
# 使用列表推导式生成条件字符串
    textUnion = ' or '.join([f"客户描述 = '{ele[0]}'" for ele in L])
    return textUnion



def createExcelTamplate():
    """
    创建Excel模板
    """

    from openpyxl import Workbook
    from openpyxl.worksheet.table import Table, TableStyleInfo
    wb = Workbook()
    ws = wb.active
    headers = ['序号', '项目编号', '项目名称','合同编号','金额','季度',"期间","资金主管","财务经理","上一级财务经理","上一级总会","是否历史项目","累计结算","累计应付","累计已付","累计欠付","比例","结果"]
    datarows=[1,"123456789","测试项目","中建三局12345678",1000,"2025年2季度","2025年03月","杨主管","杨经理","周经理","周总会","否",1000,800,600,200,0.8]
    # 插入一些示例数据
    data = [
        ['当前序号:', 25 ],
        [''],
        headers,
        datarows
    ]
    for row in data:
        ws.append(row)

    table_range = f'a3:q4'
    tab = Table(displayName="表特殊支付", ref=table_range)
    style = TableStyleInfo(name="TableStyleMedium8", showFirstColumn=False,
                        showLastColumn=False, showRowStripes=True, showColumnStripes=False)
    tab.tableStyleInfo = style
    ws.add_table(tab)
    wb.save(settings.PATH_EXCEL+"/财商系统特殊支付模板.xlsx")
    print("模板位于"+settings.PATH_EXCEL+"/财商系统特殊支付模板.xlsx"+"/n使用时需保持模板打开")