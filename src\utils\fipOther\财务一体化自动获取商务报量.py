import os
import sys

def initRuntimeEnvironment(startup_script):
    """初始化运行环境。startup_script: 启动脚本路径"""

    import site
    # 定义一个最简单的消息弹窗
    # 初始化工作目录和Python搜索路径
    script = os.path.abspath(startup_script)  # 启动脚本.py的路径
    home = os.path.dirname(script)  # 工作目录
    os.chdir(home)  # 重新设定工作目录（不在最顶层，而在UmiOCR-data文件夹下）
    for n in ['.', '.site-packages']:  # 将模块目录添加到 Python 搜索路径中
        path = os.path.abspath(os.path.join(home, n))
        if os.path.exists(path):
            site.addsitedir(path)


initRuntimeEnvironment(__file__)  # 初始化运行环境

from playwright.sync_api import Playwright, sync_playwright
import win32com.client
import time
import pythoncom
def label_question(s):
    s="//label[contains(text(),'"+s+"')]/parent::div/following-sibling::div[1]/div/div/div"
    return s
def autofill():
    pythoncom.CoInitialize()
    try:
        et = win32com.client.Dispatch("Excel.Application")
    except:
        et = win32com.client.Dispatch("Ket.Application")
    wb=et.Workbooks("自动导入.xlsm")
    ws=wb.Worksheets("分包录入")
    ws.Cells(1,1).Value=ws.UsedRange.Rows.Count
    b=int(ws.Cells(1,5).Value)+4
    #ws.UsedRange.Rows.Count+1
    for i in range(b,ws.UsedRange.Rows.Count+1):
        if stopSignal > 0 :
             with sync_playwright() as playwright:
                print("开始序号"+str(i-4)+"行")
                if ws.Cells(i,12).Value=="是":
                    browser = playwright.chromium.connect_over_cdp("http://localhost:9522")
                    default_context = browser.contexts[0]
                    page = default_context.pages[0]
                    page.locator("//*[@id='scroll-container']/div/div[1]/div/form/div/div[1]/div/div[4]/div/div[2]/div/div/span/input").click()
                    page.locator("//span[@class='g3-scm-input-affix-wrapper g3-scm-input-affix-wrapper-focused']//input[@placeholder='请输入']").fill(ws.Cells(i,16).Value)
                    page.locator("//span[contains(text(),'查 询')]").click()
                    s="//div[contains(text(),'最新结算类型')]/parent::span/parent::span/parent::div/parent::th/parent::tr/parent::thead/parent::table/parent::div/following-sibling::div[1]//table//tr[2]/td[2]"
                    page.locator(s).click()
                    time.sleep(3)
                    page = default_context.pages[1]
                    s="//div[contains(text(),'结算编号')]/parent::span/parent::span/parent::div/parent::th/parent::tr/parent::thead/parent::table/parent::div/following-sibling::div[1]//table//tr[2]/td[2]"
                    page.locator(s).click()
                    page = default_context.pages[2]
                    time.sleep(3)
                    a1=page.locator("//td[text()='申请单号：']/following-sibling::td[1]").text_content()
                    ws.Cells(i,17).Value=a1
                    with page.expect_download() as download_info:
                        page.locator("//*[@id='stepContainer']/div[2]/div[3]/div/div[2]/div/div/div[2]/div/div/div/span[1]/a/div/div[2]/div").click()
                    download = download_info.value
                    path = download.path()
                    if not os.path.exists(os.path.dirname(os.path.dirname(__file__))+"/"+"商务系统附件"):
                        os.mkdir(os.path.dirname(os.path.dirname(__file__))+"/"+"商务系统附件")
                    download.save_as(path=os.path.dirname(os.path.dirname(__file__))+"/"+"商务系统附件/"+a1+".pdf")
                    page.close()
                    page = default_context.pages[1]
                    page.close()
                ws.Cells(1,5).Value=ws.Cells(1,5).Value+1
        else:
            break
    print("完成任务")
import PySimpleGUI as sg
import threading

stopSignal = 1
t1 = threading.Thread(target=autofill)
t1.start()


# 定义界面布局
layout = [[sg.Button('重新开始')],
          [sg.Button('停止')],
          ]

# 创建窗口
window = sg.Window('自动填充', layout)

while True:
    event, values = window.read()
    
    if event == sg.WINDOW_CLOSED:
        stopSignal = -1
        break
    if event == '重新开始':
        stopSignal = 1
        t1 = threading.Thread(target=autofill)
        t1.start()
        #download(stopSignal)
    if event == '停止':
        stopSignal = -2
window.close()