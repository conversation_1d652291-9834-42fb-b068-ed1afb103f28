import flet as ft
import src.Gui.callProcess as callF
import src.base.settings as settings
from datetime import datetime


class moneyLedagerWidget(ft.Column):
    def __init__(self):
        super().__init__()
        self.startDate=ft.DatePicker(
            first_date=datetime(2020, 1, 1),
            last_date=datetime(2030, 12, 31),
            on_change=self.start_date_changed
        )
        self.endDate=ft.DatePicker(
            first_date=datetime(2020, 1, 1),
            last_date=datetime(2030, 12, 31),
            on_change=self.end_date_changed
        )
        self.startDateButton = ft.ElevatedButton(
            text=settings.LAST_MONTH_FIRST_DAY,
            icon=ft.icons.CALENDAR_TODAY,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
            ),
            on_click=lambda _: self.startDate.pick_date()
        )
        self.endDateButton = ft.ElevatedButton(
            text=settings.LAST_MONTH_LAST_DAY,
            icon=ft.icons.CALENDAR_TODAY,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
            ),
            on_click=lambda _: self.endDate.pick_date()
        )
        self.button1= ft.ElevatedButton(
            text="初始化收支台账模板",
            icon=ft.icons.FILE_DOWNLOAD,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
            ),
            on_click=lambda e:callF.thisProcess.run({"功能":"初始化收支台账模板","参数":[self.startDateButton.text,self.endDateButton.text]})
        )
        self.button2= ft.ElevatedButton(
            text="更新收支台账模板",
            icon=ft.icons.FILE_DOWNLOAD,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
            ),
            on_click=lambda e:callF.thisProcess.run({"功能":"更新收支台账模板"})
        )
        self.button3=ft.ElevatedButton(
            text="查询收支台账模板",
            icon=ft.icons.UPLOAD_FILE,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
            ),
            on_click=lambda e:callF.thisProcess.run({"功能":"查询收支台账模板"})
        )
        self.button4=ft.ElevatedButton(
            text="上传收支台账",
            icon=ft.icons.UPLOAD_FILE,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
            ),
            on_click=lambda e:callF.thisProcess.run({"功能":"上传收支台账"})
        )
        self.controls=[
             ft.Container(
                content=ft.Column([
            ft.Container(
                content=ft.Row([
                    ft.Text("开始日期", size=14, weight=ft.FontWeight.BOLD),
                    self.startDateButton,
                    ft.Text("结束日期", size=14, weight=ft.FontWeight.BOLD),
                    self.endDateButton,                     
                    self.startDate,
                    self.endDate,
                    self.button1,]),
                border=ft.border.all(1, ft.colors.OUTLINE),
                border_radius=10,
                padding=10,
                bgcolor=ft.colors.SURFACE_VARIANT,
            ),
                self.button2
        ]), bgcolor=ft.colors.SURFACE_VARIANT,padding=10,
                            alignment=ft.alignment.center,)]
    
    def start_date_changed(self, e):
        if e.data:
            self.startDateButton.text = e.data[:10]
            self.startDateButton.update()
    
    def end_date_changed(self, e):
        if e.data:
            self.endDateButton.text = e.data[:10]
            self.endDateButton.update()


class stupidPlanWidget(ft.Row):
    def __init__(self):
        super().__init__()
        self.button1= ft.ElevatedButton(
            text="初始化财商计划模板",
            icon=ft.icons.FILE_DOWNLOAD,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=5),
                padding=5,
            ),
            on_click=lambda e:callF.thisProcess.run({"功能":"初始化财商计划模板"})
        )
        self.button2= ft.ElevatedButton(
            text="更新财商计划模板",
            icon=ft.icons.FILE_DOWNLOAD,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=5),
                padding=5,
            ),
            on_click=lambda e:callF.thisProcess.run({"功能":"更新财商计划模板"})
        )
        self.button3=ft.ElevatedButton(
            text="查询财商计划数量",
            icon=ft.icons.UPLOAD_FILE,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=5),
                padding=5,
            ),
            on_click=lambda e:callF.thisProcess.run({"功能":"查询财商计划数量"})
        )
        self.button4=ft.ElevatedButton(
            text="上传财商计划",
            icon=ft.icons.UPLOAD_FILE,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=5),
                padding=5,
            ),
            on_click=lambda e:callF.thisProcess.run({"功能":"上传财商计划"})
        )
        self.controls=[
            ft.Container(
                content=ft.Row(
                    controls=[
                        ft.Container(
                            content=self.button1,
                            padding=5,
                            alignment=ft.alignment.center,
                        ),
                        ft.Container(
                            content=self.button2,
                            padding=5,
                            alignment=ft.alignment.center,
                        ),
                    ],
                    alignment=ft.MainAxisAlignment.SPACE_AROUND,
                ),
                padding=5,
                border_radius=5,
                bgcolor=ft.colors.SURFACE_VARIANT,
                margin=ft.margin.only(bottom=5),
            ),
        ]

class stupidSystemWidget1(ft.Row):
    def __init__(self):
        super().__init__()
        self.controls=[
            ft.Container(
                content=ft.Row(
                    controls=[],
                    alignment=ft.MainAxisAlignment.SPACE_AROUND,
                ),
                padding=5,
                border_radius=5,
                bgcolor=ft.colors.SURFACE_VARIANT,
                margin=ft.margin.only(bottom=5),
            ),
        ]

class stupidSystemWidget2(ft.Row):
    def __init__(self):
        super().__init__()
        self.button1= ft.ElevatedButton(
            text="获取特殊支付模板",
            icon=ft.icons.FILE_DOWNLOAD,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=5),
                padding=5,
            ),
            on_click=lambda e:callF.thisProcess.run({"功能":"获取特殊支付模板"})
        )
        self.button2= ft.ElevatedButton(
            text="更新特殊支付模板",
            icon=ft.icons.FILE_DOWNLOAD,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=5),
                padding=5,
            ),
            on_click=lambda e:callF.thisProcess.run({"功能":"更新特殊支付模板"})
        )
        self.button23=ft.ElevatedButton(
            text="批量特殊支付",
            icon=ft.icons.PAYMENT,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=5),
                padding=5,
            ),
            on_click=lambda e:callF.thisProcess.run({"功能":"批量特殊支付"})
        )
        self.controls=[
            ft.Container(
                content=ft.Row(
                    controls=[
                        ft.Container(
                            content=self.button1,
                            padding=5,
                            alignment=ft.alignment.center,
                        ),
                        ft.Container(
                            content=self.button2,
                            padding=5,
                            alignment=ft.alignment.center,
                        ),
                    ],
                    alignment=ft.MainAxisAlignment.SPACE_AROUND,
                ),
                padding=5,
                border_radius=5,
                bgcolor=ft.colors.SURFACE_VARIANT,
                margin=ft.margin.only(bottom=5),
            ),
        ]

class stupidSystemWidget3(ft.Row):
    def __init__(self):
        super().__init__()
        self.button1= ft.ElevatedButton(
            text="获取商务推送模板",
            icon=ft.icons.FILE_OPEN,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=5),
                padding=5,
            ),
            on_click=lambda e:callF.thisProcess.run({"功能":"获取商务推送模板"})
        )        
        self.button2= ft.ElevatedButton(
            text="更新商务推送模板",
            icon=ft.icons.FILE_OPEN,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=5),
                padding=5,
            ),
            on_click=lambda e:callF.thisProcess.run({"功能":"更新商务推送模板"})
        )
        self.button3=ft.ElevatedButton(
            text="批量商务推送",
            icon=ft.icons.SEND,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=5),
                padding=5,
            ),
            on_click=lambda e:callF.thisProcess.run({"功能":"批量商务推送"})
        )
        self.controls=[
            ft.Container(
                content=ft.Row(
                    controls=[
                        ft.Container(
                            content=self.button1,
                            padding=5,
                            alignment=ft.alignment.center,
                        ),
                        ft.Container(
                            content=self.button2,
                            padding=5,
                            alignment=ft.alignment.center,
                        ),
                    ],
                    alignment=ft.MainAxisAlignment.SPACE_AROUND,
                ),
                padding=5,
                border_radius=5,
                bgcolor=ft.colors.SURFACE_VARIANT,
                margin=ft.margin.only(bottom=5),
            ),
        ]
   

class collaborationTabView(ft.Column):
    def __init__(self):
        super().__init__()
        self.visible = True
        self.expand = True
        self.navigation_bar = ft.NavigationBar(
            destinations=[
                ft.NavigationBarDestination(icon=ft.icons.MULTILINE_CHART, label="财商板块"),
            ],
            on_change=lambda e:self.changeMain(e)
        )

        # 创建可滚动的内容区域
        self.scrollable_content = ft.Column(
            scroll=ft.ScrollMode.AUTO,
            expand=True,
            spacing=10,
        )

        # 创建收支台账模块
        self.money_ledger = ft.Container(
            content=ft.Column(
                controls=[
                    ft.Text("收支台账管理", size=16, weight=ft.FontWeight.BOLD, color=ft.colors.PRIMARY),
                    ft.Container(
                        content=ft.Row(
                            controls=[
                                ft.Container(
                                    content=ft.Column(
                                        controls=[
                                            ft.Text("开始日期", size=14, weight=ft.FontWeight.BOLD),
                                            moneyLedagerWidget().startDateButton,
                                        ],
                                        spacing=5,
                                    ),
                                    padding=10,
                                ),
                                ft.Container(
                                    content=ft.Column(
                                        controls=[
                                            ft.Text("结束日期", size=14, weight=ft.FontWeight.BOLD),
                                            moneyLedagerWidget().endDateButton,
                                        ],
                                        spacing=5,
                                    ),
                                    padding=10,
                                ),
                                ft.ElevatedButton(
                                    text="初始化收支台账模板",
                                    icon=ft.icons.FILE_DOWNLOAD,
                                    style=ft.ButtonStyle(
                                        shape=ft.RoundedRectangleBorder(radius=10),
                                        padding=15,
                                    ),
                                    on_click=lambda e:callF.thisProcess.run({"功能":"初始化收支台账模板","参数":[moneyLedagerWidget().startDateButton.text,moneyLedagerWidget().endDateButton.text]})
                                ),
                            ],
                            alignment=ft.MainAxisAlignment.CENTER,
                        ),
                        border=ft.border.all(1, ft.colors.OUTLINE),
                        border_radius=10,
                        padding=10,
                        bgcolor=ft.colors.SURFACE_VARIANT,
                    ),
                    ft.Row(
                        controls=[
                            ft.ElevatedButton(
                                text="更新收支台账模板",
                                icon=ft.icons.FILE_DOWNLOAD,
                                style=ft.ButtonStyle(
                                    shape=ft.RoundedRectangleBorder(radius=10),
                                    padding=15,
                                ),
                                on_click=lambda e:callF.thisProcess.run({"功能":"更新收支台账模板"})
                            ),
                            ft.ElevatedButton(
                                text="查询收支台账模板",
                                icon=ft.icons.UPLOAD_FILE,
                                style=ft.ButtonStyle(
                                    shape=ft.RoundedRectangleBorder(radius=10),
                                    padding=15,
                                ),
                                on_click=lambda e:callF.thisProcess.run({"功能":"查询收支台账模板"})
                            ),
                            ft.ElevatedButton(
                                text="上传收支台账",
                                icon=ft.icons.UPLOAD_FILE,
                                style=ft.ButtonStyle(
                                    shape=ft.RoundedRectangleBorder(radius=10),
                                    padding=15,
                                ),
                                on_click=lambda e:callF.thisProcess.run({"功能":"上传收支台账"})
                            ),
                        ],
                        alignment=ft.MainAxisAlignment.SPACE_AROUND,
                    ),
                ],
                spacing=15,
            ),
            padding=20,
            border_radius=15,
            bgcolor=ft.colors.SURFACE_VARIANT,
            margin=ft.margin.only(bottom=20),
        )

        # 创建财商计划模块
        self.plan_section = ft.Container(
            content=ft.Column(
                controls=[
                    ft.Text("财商计划管理", size=16, weight=ft.FontWeight.BOLD, color=ft.colors.PRIMARY),
                    ft.Row(
                        controls=[
                            ft.ElevatedButton(
                                text="初始化财商计划模板",
                                icon=ft.icons.FILE_DOWNLOAD,
                                style=ft.ButtonStyle(
                                    shape=ft.RoundedRectangleBorder(radius=10),
                                    padding=10,
                                ),
                                on_click=lambda e:callF.thisProcess.run({"功能":"初始化财商计划模板"})
                            ),                            
                            ft.ElevatedButton(
                                text="更新财商计划模板",
                                icon=ft.icons.FILE_DOWNLOAD,
                                style=ft.ButtonStyle(
                                    shape=ft.RoundedRectangleBorder(radius=10),
                                    padding=10,
                                ),
                                on_click=lambda e:callF.thisProcess.run({"功能":"更新财商计划模板"})
                            ),
                            ft.ElevatedButton(
                                text="查询财商计划数量",
                                icon=ft.icons.UPLOAD_FILE,
                                style=ft.ButtonStyle(
                                    shape=ft.RoundedRectangleBorder(radius=10),
                                    padding=10,
                                ),
                                on_click=lambda e:callF.thisProcess.run({"功能":"查询财商计划数量"})
                            ),
                            ft.ElevatedButton(
                                text="上传财商计划",
                                icon=ft.icons.UPLOAD_FILE,
                                style=ft.ButtonStyle(
                                    shape=ft.RoundedRectangleBorder(radius=10),
                                    padding=10,
                                ),
                                on_click=lambda e:callF.thisProcess.run({"功能":"上传财商计划"})
                            ),
                        ],
                        alignment=ft.MainAxisAlignment.SPACE_AROUND,
                    ),
                ],
                spacing=15,
            ),
            padding=20,
            border_radius=15,
            bgcolor=ft.colors.SURFACE_VARIANT,
            margin=ft.margin.only(bottom=20),
        )

        # 创建特殊支付模块
        self.special_payment = ft.Container(
            content=ft.Column(
                controls=[
                    ft.Text("特殊支付管理", size=16, weight=ft.FontWeight.BOLD, color=ft.colors.PRIMARY),
                    ft.Row(
                        controls=[
                            ft.ElevatedButton(
                                text="获取特殊支付模板",
                                icon=ft.icons.FILE_DOWNLOAD,
                                style=ft.ButtonStyle(
                                    shape=ft.RoundedRectangleBorder(radius=10),
                                    padding=15,
                                ),
                                on_click=lambda e:callF.thisProcess.run({"功能":"获取特殊支付模板"})
                            ),
                            ft.ElevatedButton(
                                text="更新特殊支付模板",
                                icon=ft.icons.FILE_DOWNLOAD,
                                style=ft.ButtonStyle(
                                    shape=ft.RoundedRectangleBorder(radius=10),
                                    padding=15,
                                ),
                                on_click=lambda e:callF.thisProcess.run({"功能":"更新特殊支付模板"})
                            ),
                            ft.ElevatedButton(
                                text="批量特殊支付",
                                icon=ft.icons.PAYMENT,
                                style=ft.ButtonStyle(
                                    shape=ft.RoundedRectangleBorder(radius=10),
                                    padding=15,
                                ),
                                on_click=lambda e:callF.thisProcess.run({"功能":"批量特殊支付"})
                            ),
                        ],
                        alignment=ft.MainAxisAlignment.SPACE_AROUND,
                    ),
                ],
                spacing=15,
            ),
            padding=20,
            border_radius=15,
            bgcolor=ft.colors.SURFACE_VARIANT,
            margin=ft.margin.only(bottom=20),
        )

        # 创建商务推送模块
        self.business_push = ft.Container(
            content=ft.Column(
                controls=[
                    ft.Text("商务推送管理", size=16, weight=ft.FontWeight.BOLD, color=ft.colors.PRIMARY),
                    ft.Row(
                        controls=[
                            ft.ElevatedButton(
                                text="获取商务推送模板",
                                icon=ft.icons.FILE_OPEN,
                                style=ft.ButtonStyle(
                                    shape=ft.RoundedRectangleBorder(radius=10),
                                    padding=15,
                                ),
                                on_click=lambda e:callF.thisProcess.run({"功能":"获取商务推送模板"})
                            ),
                            ft.ElevatedButton(
                                text="更新商务推送模板",
                                icon=ft.icons.FILE_OPEN,
                                style=ft.ButtonStyle(
                                    shape=ft.RoundedRectangleBorder(radius=10),
                                    padding=15,
                                ),
                                on_click=lambda e:callF.thisProcess.run({"功能":"更新商务推送模板"})
                            ),
                            ft.ElevatedButton(
                                text="批量商务推送",
                                icon=ft.icons.SEND,
                                style=ft.ButtonStyle(
                                    shape=ft.RoundedRectangleBorder(radius=10),
                                    padding=15,
                                ),
                                on_click=lambda e:callF.thisProcess.run({"功能":"批量商务推送"})
                            ),
                        ],
                        alignment=ft.MainAxisAlignment.SPACE_AROUND,
                    ),
                ],
                spacing=15,
            ),
            padding=20,
            border_radius=15,
            bgcolor=ft.colors.SURFACE_VARIANT,
            margin=ft.margin.only(bottom=20),
        )

        # 将所有模块添加到可滚动区域
        self.scrollable_content.controls = [
            self.money_ledger,
            self.plan_section,
            self.special_payment,
            self.business_push,
        ]

        # 创建主布局
        self.mainRegional = ft.Column(
            controls=[self.scrollable_content],
            expand=True,
        )

        self.controls = (self.navigation_bar, self.mainRegional)
        self.default = [self.scrollable_content]

    def changeMain(self,e):
        index=e.control.selected_index
        if index==0:
            self.mainRegional.controls = self.default
        self.mainRegional.update()