with a as(
SELECT  
any_value(内行查询.财年) as 财年,
any_value(内行查询.过帐日期) as 过帐日期,
any_value(内行查询.利润中心) as 利润中心,
any_value(内行查询.利润中心描述) as 利润中心描述,
any_value(内行查询.凭证编号) as 凭证编号,
any_value(内行查询.中台单据号) as 中台单据号2,
array_agg(DISTINCT(内行查询.总账科目长文本)) as 总账科目,
array_agg(DISTINCT(内行查询.供应商描述)) as 供应商描述,
any_value(事由) as 事由,
sum(内行查询.内行金额) as 内行金额,
any_value(CASE WHEN 项目所属的核算组织 is NULL THEN  '外部'  ELSE  '内部' END) as 内外部流水,
if_classification(总账科目,中台单据号2) as 单据分类,
any_value(内行客商) as 内行客商标记,
any_value(内行客商描述) as 内行客商标记2
from 内行查询 left join 主数据  on 内行查询.内行客商=主数据.项目所属的核算组织
GROUP BY 凭证编号,财年,内行查询.利润中心,内行客商)
--
INSERT OR REPLACE INTO 资金整理 SELECT * FROM a