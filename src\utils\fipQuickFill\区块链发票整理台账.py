import os
from playwright.sync_api import Play<PERSON>, sync_playwright,<PERSON>,BrowserContext
import src.utils.cscec as cscec
import src.utils.Excel.excel as excel
import fitz
import cv2
import numpy as np
from pyzbar.pyzbar import decode
from PIL import Image
import io
import fitz  # PyMuPDF
import time
def detect_qr_from_image(image):
    """识别图片中的二维码"""
    # 将图片转换为PIL Image对象
    if isinstance(image, bytes):
        image = Image.open(io.BytesIO(image))
    
    # 转换为OpenCV格式
    if isinstance(image, Image.Image):
        image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
    
    # 解码二维码
    decoded_objects = decode(image)
    
    if decoded_objects:
        for obj in decoded_objects:
            # 获取二维码内容
            data = obj.data.decode('utf-8')
            return data
    return None

def extract_qr_from_pdf(pdf_path):
    """从PDF中提取并识别二维码"""
    # 打开PDF文件
    doc = fitz.open(pdf_path)
    
    for page_num in range(len(doc)):
        page = doc[page_num]
        
        # 获取页面上的图片
        image_list = page.get_images()
        
        # 遍历页面上的所有图片
        for img_index, img in enumerate(image_list):
            xref = img[0]  # 图片的xref号
            base_image = doc.extract_image(xref)
            image_bytes = base_image["image"]
            
            # 识别图片中的二维码
            qr_data = detect_qr_from_image(image_bytes)
            if qr_data:
                print(f"在第{page_num + 1}页找到二维码，内容为: {qr_data}")
                return qr_data
        
        # 如果没有找到图片或图片中没有二维码，尝试截取左上角区域
        # 假设二维码在左上角300x300像素的区域内
        zoom = 2  # 增加分辨率
        mat = fitz.Matrix(zoom, zoom)
        clip = fitz.Rect(0, 0, 300, 300)  # 左上角300x300像素区域
        pix = page.get_pixmap(matrix=mat, clip=clip)
        
        # 将pixmap转换为PIL Image
        img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
        
        # 识别二维码
        qr_data = detect_qr_from_image(img)
        if qr_data:
            print(f"在第{page_num + 1}页左上角区域找到二维码，内容为: {qr_data}")
            return qr_data
    
    print("未找到二维码")
    return None

def shenzhenInvoice(default_context:BrowserContext,url):
    newPage=default_context.new_page()
    newPage.goto(url)
    invoiceCode=newPage.locator("//div[text()='发票代码']/following-sibling::div[1]").text_content()
    invoiceNum=newPage.locator("//div[text()='发票号码']/following-sibling::div[1]").text_content()
    invoiceSale=newPage.locator("//div[text()='销货方名称']/following-sibling::div[1]").text_content()
    invoiceBuy=newPage.locator("//div[text()='购买方名称']/following-sibling::div[1]").text_content()
    invoiceAmount=newPage.locator("//div[text()='价税合计']/following-sibling::div[1]").text_content()
    newPage.close()
    return [invoiceCode,invoiceNum,invoiceSale,invoiceBuy,invoiceAmount]
def main():
    with sync_playwright() as playwright:
        browser = playwright.chromium.connect_over_cdp("http://localhost:9222")
        default_context = browser.contexts[0]
        page = cscec.switch_to_page(default_context,"中国建筑司库一体化")
        table=cscec.cscecTable(page,"组织机构")
        wb=excel.myBook()
        ws=wb.sheet("Sheet1")
        k=ws.MaxRow+1
        for i in range(1,table.count+1):
            table.click(i,"单据")
            cscec.getVisible(page,"//label[text()='报销事由：']")
            
            try:
                page.locator("//div[text()='审批提醒']/preceding::div[1]").click()
            except Exception as e:
                print(e)

            flowCode=cscec.getLalbel_inputText(page,"单据编号：")
            flowReason=cscec.getLalbel_inputValue(page,"*报销事由：")
            table2=cscec.cscecTable(page,"实际报销金额")
            flowMoney=table2.getValue(1,"实际报销金额")


            page.locator("//span[text()='影像']/parent::div/parent::div/parent::div/parent::div").click()
            time.sleep(1)
            cscec.getVisible(page,"//div[@title='下载' and @role='button']").click()
            cscec.getVisible(page,"//*[text()='影像下载']/parent::div/parent::div/parent::div//span[@class='x-editable']")
            xc=page.locator("//*[text()='影像下载']/parent::div/parent::div/parent::div//span[@class='x-editable']")
            for j in range(xc.count()):
                xc.nth(j).click()
                name, ext = os.path.splitext(xc.nth(j).locator("//preceding-sibling::div[1]//img").get_attribute("title"))
                if ext==".pdf": #只识别pdf,放弃图片格式
                    with page.expect_download() as download_info:
                        page.locator("//*[text()='影像下载']/parent::div/parent::div/parent::div//span[contains(text(),'下载')]").dblclick()
                        download = download_info.value
                        path = download.path()
                        print(wb.path+"/临时文件"+ext)
                        download.save_as(path=wb.path+"/临时文件"+ext)
                        ocrText=extract_qr_from_pdf(wb.path+"/临时文件"+ext) #删除临时文件
                        os.remove(wb.path+"/临时文件"+ext)
                        if ocrText!=None:
                            ws.Cells(k,1).Value=flowCode
                            ws.Cells(k,2).Value=flowReason
                            ws.Cells(k,3).Value=flowMoney
                            ws.Cells(k,4).Value=ocrText
                            if "https://bcfp.shenzhen.chinatax.gov.cn" in ocrText:
                                rList=shenzhenInvoice(default_context,ocrText)
                                for cl in range(5):
                                    ws.Cells(k,cl+5).Value=rList[cl]
                            k=k+1
            page.locator("//*[text()='影像下载']/preceding-sibling::div[1]//td[1]").click() #点击关闭
            cscec.closeTab(page)
            cscec.closeTab(page)
if __name__ == '__main__':
    main()


