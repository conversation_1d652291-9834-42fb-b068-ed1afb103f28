WITH a AS (
    SELECT  ANY_VALUE(凭证编号) as 凭证编号, ANY_VALUE(利润中心) as 利润中心, ANY_VALUE(财年) as 财年,any_value(客户描述) as 客户描述,
    any_value(客户) as 客户,
    count(distinct (CASE WHEN 总账科目长文本 like '%可用存款%' THEN 客户 ELSE NULL END)) as 内行客商数量,
    any_value(CASE WHEN 总账科目长文本 like '银行存款%' THEN 1 ELSE 0 END) as 是否含银行存款,
    any_value(CASE WHEN 总账科目长文本 like '可用存款%' THEN 1 ELSE 0 END) as 是否含内行存款,
    FROM 明细帐
    WHERE (总账科目长文本 LIKE '%存款%') 
    GROUP BY 利润中心, 凭证编号, 财年
),
b AS (
select 明细帐.*,
(case when a.内行客商数量 > 1  then 明细帐.客户 else a.客户 end) as 内行客商,
(case when a.内行客商数量 > 1  then 明细帐.客户描述 else a.客户描述 end) as 内行客商描述,
a.内行客商数量
from 明细帐 LEFT join a on a.利润中心 = 明细帐.利润中心 and a.凭证编号 = 明细帐.凭证编号 and a.财年 = 明细帐.财年
where ((a.内行客商数量 > 1 or (是否含银行存款>0 and 是否含内行存款>0) and 总账科目长文本  like '%存款%') or (a.内行客商数量 == 1 and 总账科目长文本 not like '%存款%' and 是否含银行存款==0)
or (是否含银行存款==1 and 是否含内行存款==0 and 总账科目长文本 not like '%存款%')
)
),
c as (
select 
any_value(财年) as 财年,
any_value(过帐日期) as 过帐日期,
any_value(输入日期) as 输入日期,
any_value(凭证编号) as 凭证编号, 
any_value(利润中心) as 利润中心, 
any_value(利润中心描述) as 利润中心描述,
any_value(总账科目长文本) as 总账科目长文本,
sum(case when 内行客商数量 > 1  then 带符号的本位币金额 else 0-带符号的本位币金额 end) as 内行金额,
any_value(内行客商) as 内行客商,
any_value(内行客商描述) as 内行客商描述,
any_value(内行客商数量) as 内行客商数量,
any_value(供应商) as 供应商,
any_value(供应商描述) as 供应商描述,
any_value(中台单据号) as 中台单据号,
any_value(文本) as 事由
from b
GROUP by 凭证编号, 利润中心, 财年,总账科目长文本,内行客商)
--
INSERT OR REPLACE INTO 内行查询 SELECT * FROM c
