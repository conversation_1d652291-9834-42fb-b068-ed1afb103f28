revenueAndExpenditureLedger='''
select 
any_value(WBS元素) as 项目编号,
any_value(利润中心描述) as 项目名称,
any_value(利润中心) as 利润中心,
sum(case when (((借贷标识 = 'S' and 反记帐 = '') or (借贷标识 = 'H' and 反记帐 = 'X') ) and ((过帐日期 >= '期初日期留 00:00:00') and (过帐日期<='期末日期留 00:00:00')) and 总账科目长文本 like '应交税费\增值税\预交增值税\建筑服务%' )then 带符号的本位币金额 else null end) as 预缴税金,
sum(case when (((借贷标识 = 'S' and 反记帐 = '') or (借贷标识 = 'H' and 反记帐 = 'X') ) and ((过帐日期 >= '期初日期留 00:00:00') and (过帐日期<='期末日期留 00:00:00')) and 总账科目长文本 like '税金及附加\%' )then 带符号的本位币金额 else null end) as 其他税金,
sum(case when (((借贷标识 = 'S' and 反记帐 = '') or (借贷标识 = 'H' and 反记帐 = 'X') ) and ((过帐日期 >= '期初日期留 00:00:00') and (过帐日期<='期末日期留 00:00:00')) and 总账科目长文本 like '合同履约成本%职工薪酬%' )then 带符号的本位币金额 else null end) as 薪酬,
sum(case when (((借贷标识 = 'S' and 反记帐 = '') or (借贷标识 = 'H' and 反记帐 = 'X') ) and ((过帐日期 >= '期初日期留 00:00:00') and (过帐日期<='期末日期留 00:00:00')) and 总账科目长文本 like '合同履约成本%办公%' )then 带符号的本位币金额 else null end) as 办公费,
sum(case when (((借贷标识 = 'S' and 反记帐 = '') or (借贷标识 = 'H' and 反记帐 = 'X') ) and ((过帐日期 >= '期初日期留 00:00:00') and (过帐日期<='期末日期留 00:00:00')) and 总账科目长文本 like '合同履约成本%差旅%' )then 带符号的本位币金额 else null end) as 差旅费,
sum(case when (((借贷标识 = 'S' and 反记帐 = '') or (借贷标识 = 'H' and 反记帐 = 'X') ) and ((过帐日期 >= '期初日期留 00:00:00') and (过帐日期<='期末日期留 00:00:00')) and 总账科目长文本 like '合同履约成本%业务招待%' )then 带符号的本位币金额 else null end) as 业务招待费,
0 as 其他收入,
0 as "非包-其他专项支出",
0 as 一体化余额,
'' as 上级机关,
'' as 第几季度,
'' as 几月,
'否' as 是否跨年,
'是' as 是否填入
FROM 明细帐
GROUP by  利润中心
'''

accountBalanceTable='''
select 
any_value(总账科目) as 总账科目,
any_value(总账科目长文本) as 总账科目长文本,
any_value(利润中心) as 利润中心,
any_value(利润中心描述) as 利润中心描述,
any_value(合同) as 合同,
any_value(合同文本描述) as 合同文本描述,
any_value(供应商) as 供应商,
any_value(供应商描述) as 供应商描述,
any_value(客户) as 客户,
any_value(客户描述) as 客户描述,
sum(case when (过帐日期<'期初日期留 00:00:00')  then 带符号的本位币金额 else null end) as 期初余额,
sum(带符号的本位币金额) as 期末余额,
sum(case when (((借贷标识 = 'H' and 反记帐 = '') or (借贷标识 = 'S' and 反记帐 = 'X')) and ((过帐日期 >= '期初日期留 00:00:00') and (过帐日期<='期末日期留 00:00:00')) ) then 带符号的本位币金额 else null end) as 本期借方发生额,
sum(case when (((借贷标识 = 'S' and 反记帐 = '') or (借贷标识 = 'H' and 反记帐 = 'X')) and ((过帐日期 >= '期初日期留 00:00:00') and (过帐日期<='期末日期留 00:00:00'))) then 带符号的本位币金额 else null end) as 本期贷方发生额,
sum(case when (((借贷标识 = 'H' and 反记帐 = '') or (借贷标识 = 'S' and 反记帐 = 'X')) and ((过帐日期<='期末日期留 00:00:00'))) then 带符号的本位币金额 else null end) as 累计借方发生额,
sum(case when (((借贷标识 = 'S' and 反记帐 = '') or (借贷标识 = 'H' and 反记帐 = 'X')) and ((过帐日期<='期末日期留 00:00:00'))) then 带符号的本位币金额 else null end) as 累计贷方发生额
from 明细帐 where 文本 != '自动清账剩余项目'  GROUP BY 总账科目长文本,利润中心,合同,供应商,客户'''


付款台账='''
with 
a as (
SELECT any_value(凭证编号) as 凭证编号, any_value(财年) as 财年,any_value(利润中心) as 利润中心,
       0-SUM(case when (总账科目长文本 LIKE '%保证金%') then 带符号的本位币金额 ELSE NULL END) AS 扣履约保证金,
       0-SUM(case when (总账科目长文本 LIKE '%供应链融资%' or 总账科目长文本 LIKE '%保理%') then 带符号的本位币金额 ELSE NULL END) AS 供应链保理,
       0-SUM(case when (总账科目长文本 LIKE '%非货币交易%' ) then 带符号的本位币金额 ELSE NULL END) AS 本利润中心,
       0-SUM(case when (总账科目长文本 LIKE '%直接%费%') then 带符号的本位币金额 ELSE NULL END) AS 冲成本,
       0-SUM(CASE WHEN (总账科目长文本 LIKE '%可用存款%' or 总账科目长文本 LIKE '%银行存款%') THEN 带符号的本位币金额 ELSE NULL END) AS 内行或存款,
       any_value(case when 总账科目长文本  like '%可用存款%' then 客户描述 else null end) as 内行客商
FROM 明细帐 GROUP BY 利润中心, 凭证编号, 财年),
--
b as (SELECT  any_value(凭证编号) as 凭证编号, any_value(利润中心) as 利润中心, any_value(财年) as 财年,
any_value(总账科目长文本) as 供应商类型,
any_value(合同) as 合同,
any_value(输入日期) as 输入日期,
any_value(供应商) as 供应商,any_value(供应商描述) as 供应商描述,any_value(文本) as 文本,
any_value(利润中心描述) as 利润中心描述,any_value(过帐日期) as 过帐日期,any_value(中台单据号) as 中台单据号,
sum(带符号的本位币金额) as '总付款金额'
FROM 明细帐
WHERE (总账科目长文本 LIKE '应付账款%款%' and 总账科目长文本 not LIKE '%暂估%') and (文本!='自动清账剩余项目')
AND ((借贷标识 = 'S' AND 反记帐 = '') OR (借贷标识 = 'H' AND 反记帐 = 'X'))  GROUP BY 利润中心, 凭证编号, 财年,供应商,合同
)
select  过帐日期,输入日期,b.凭证编号,b.利润中心,利润中心描述,供应商,供应商描述,合同,文本,中台单据号,总付款金额,扣履约保证金,供应链保理,冲成本,本利润中心,内行或存款,内行客商
from b LEFT  JOIN a ON b.利润中心 = a.利润中心 AND b.凭证编号 = a.凭证编号 AND b.财年 = a.财年'''

all='''
select 
any_value(利润中心描述) as 利润中心描述,
any_value(利润中心) as 利润中心,
sum(case when 总账科目长文本 like '主营业务收入%'  then 0-带符号的本位币金额 else 0 end) as 收入,
sum(case when 总账科目长文本 like '主营业务成本%'  then 带符号的本位币金额 else 0 end) as 成本,
sum(case when 总账科目长文本 like '专项储备%'  then 带符号的本位币金额 else 0 end) as 专项储备余额,
sum(case when 总账科目长文本 like '%合同结算%' or 总账科目长文本 like '合同资产\工程款（已完工未结算）' or 总账科目长文本 like '%已结算未完工%' then 带符号的本位币金额 else 0 end) as 合同余额,
sum(case when 总账科目长文本 like '合同履约成本%' and 总账科目长文本 not like '合同履约成本%结转%' then 带符号的本位币金额 else 0 end) as 合同履约成本余额,
sum(case when 总账科目长文本 like '%预计负债\亏损合同%'  then 带符号的本位币金额 else 0 end) as 预计负债亏损合同,
sum(case when 总账科目长文本 like '原材料%'  then 带符号的本位币金额 else 0 end) as 原材料,
sum(case when 总账科目长文本 like '合同履约成本%'  then 带符号的本位币金额 else 0 end) as 成本余额,
sum(case when 总账科目长文本 like '应付账款%暂估%'  then 带符号的本位币金额 else 0 end) as 暂估应付余额,
sum(case when 总账科目长文本 like '研发支出%'  then 带符号的本位币金额 else 0 end) as 研发支出,
sum(case when 总账科目长文本 like '内部存款\非货币交易'  then 带符号的本位币金额 else 0 end) as 本利润非货币交易未平,
sum(case when 总账科目长文本 like '%可用存款%' or 总账科目长文本 like '银行存款%'  then 带符号的本位币金额 else 0 end) as 原始存量,
sum(case when 总账科目长文本 like '%内部往来\内部借贷%'  then 带符号的本位币金额 else 0 end) as 内部借款,
sum(case when 总账科目长文本 like '应付账款\应付供应链融资款'  then 带符号的本位币金额 else 0 end) as 保理借款,
sum(case when 总账科目长文本 like '%内部往来\其他' and (客户描述='中建三局安装工程有限公司' or 客户描述 = '中建三局集团有限公司安装事业部') then 带符号的本位币金额 else 0 end) as 内部往来挂总部,
sum(case when 总账科目长文本 like '%内部往来\其他' and (客户描述='中建三局安装工程有限公司南方分公司' or 客户描述 = '中建三局集团有限公司安装事业部南方分公司') then 带符号的本位币金额 else 0 end) as 内部往来挂经理部,
sum(case when 总账科目长文本 like '%现场维护费'then 带符号的本位币金额 else 0 end) as 现场维护费,
内部往来挂经理部+现场维护费 as 内部往来需调整,
sum(case when ((总账科目长文本 like '应收账款%进度%' or 总账科目长文本 like '合同资产&质保金%') and 文本 not like '自动清账' and  ((借贷标识 = 'S' AND 反记帐 = '') OR (借贷标识 = 'H' AND 反记帐 = 'X'))) then 带符号的本位币金额 else 0 end) as 累计确权,
0-sum(case when (总账科目长文本 like '应收账款%进度%' and 文本 not like '自动清账' and  ((借贷标识 = 'H' AND 反记帐 = '') OR (借贷标识 = 'S' AND 反记帐 = 'X'))) then 带符号的本位币金额 else 0 end) as 累计收款
FROM 明细帐
GROUP by  利润中心'''

成本查询='''
with 
a as (
SELECT any_value(凭证编号) as 凭证编号, 
       any_value(财年) as 财年,
       any_value(利润中心) as 利润中心,
       any_value(利润中心描述) as 利润中心描述,
       any_value(文本) as 文本,
       any_value(过帐日期) as 过帐日期,
       any_value(输入日期) as 输入日期,
       any_value(总账科目长文本) as 成本科目,
       SUM(带符号的本位币金额) AS 入账成本
FROM 明细帐 where (总账科目长文本 LIKE '%合同履约成本%' 
or 总账科目长文本 LIKE '管理费用%'
or 总账科目长文本 LIKE '信用减值损失%'
or 总账科目长文本 LIKE '资产减值损失%'
or 总账科目长文本 LIKE '研发费用%'
or 总账科目长文本 LIKE '税金及附加%'
or 总账科目长文本 LIKE '资产处置损益%'
or 总账科目长文本 LIKE '财务费用%'
or 总账科目长文本 LIKE '营业外支出%') 
and 总账科目长文本 not like '%结转%'
GROUP BY 利润中心,凭证编号,财年,总账科目长文本),
b as (SELECT any_value(凭证编号) as 凭证编号, 
any_value(利润中心) as 利润中心, 
any_value(财年) as 财年, 
any_value(case when (供应商 != '') then 供应商 ELSE NULL END) as 供应商,
any_value(case when (供应商描述 != '') then 供应商描述 ELSE NULL END) as 供应商描述,
any_value(case when (总账科目长文本 LIKE '%暂估%') then '暂估成本' when (总账科目长文本 LIKE '%内部往来%' ) then '内部往来划转' ELSE NULL END) AS 科目分类
FROM 明细帐
GROUP BY 利润中心,凭证编号,财年
)
--
select  a.财年,a.过帐日期,a.输入日期,a.凭证编号,a.利润中心,a.利润中心描述, a.成本科目,b.供应商,b.供应商描述,a.文本,a.入账成本,b.科目分类
from a LEFT JOIN b ON a.利润中心 = b.利润中心 AND a.凭证编号 = b.凭证编号 AND a.财年 = b.财年 where a.入账成本 > 0.001 or a.入账成本 < -0.001'''

收款台账='''
with 
a as (
SELECT any_value(凭证编号) as 凭证编号, any_value(财年) as 财年,any_value(利润中心) as 利润中心,
       SUM(case when (总账科目长文本 LIKE '%非货币交易%' ) then 带符号的本位币金额 ELSE NULL END) AS 本利润中心,
       SUM(CASE WHEN (总账科目长文本 LIKE '%可用存款%' or 总账科目长文本 LIKE '%银行存款%') THEN 带符号的本位币金额 ELSE NULL END) AS 内行或存款,
       any_value(case when 总账科目长文本  like '%可用存款%' then 客户描述 else null end) as 内行客商
FROM 明细帐 GROUP BY 利润中心, 凭证编号, 财年),
--
b as (SELECT  any_value(凭证编号) as 凭证编号, any_value(利润中心) as 利润中心, any_value(财年) as 财年,
any_value(合同) as 合同,
any_value(输入日期) as 输入日期,
any_value(客户) as 客户,any_value(客户描述) as 客户描述,any_value(文本) as 文本,
any_value(利润中心描述) as 利润中心描述,any_value(过帐日期) as 过帐日期,any_value(中台单据号) as 中台单据号,
0-sum(带符号的本位币金额) as '总收款金额'
FROM 明细帐
WHERE (总账科目长文本 LIKE '应收账款%款%') and (文本!='自动清账剩余项目')
AND ((借贷标识 = 'H' AND 反记帐 = '') OR (借贷标识 = 'S' AND 反记帐 = 'X'))  GROUP BY 利润中心, 凭证编号, 财年,客户,合同
)
select  过帐日期,输入日期,b.凭证编号,b.利润中心,利润中心描述,客户,客户描述,合同,文本,中台单据号,总收款金额,本利润中心,内行或存款,内行客商
from b LEFT  JOIN a ON b.利润中心 = a.利润中心 AND b.凭证编号 = a.凭证编号 AND b.财年 = a.财年
'''
内行查询='''
WITH a AS (
    SELECT  ANY_VALUE(凭证编号) as 凭证编号, ANY_VALUE(利润中心) as 利润中心, ANY_VALUE(财年) as 财年,any_value(客户描述) as 客户描述,
    any_value(客户) as 客户,
    count(distinct 客户) as 内行客商数量
    FROM 明细帐
    WHERE (总账科目长文本 LIKE '%可用存款%') 
    GROUP BY 利润中心, 凭证编号, 财年
)
--
select 
any_value(过帐日期) as 过帐日期,
any_value(输入日期) as 输入日期,
any_value(明细帐.凭证编号) as 凭证编号, 
any_value(明细帐.利润中心) as 利润中心, 
any_value(利润中心描述) as 利润中心描述,
any_value(总账科目长文本) as 总账科目长文本,
sum(case when a.内行客商数量 > 1  then 带符号的本位币金额 else 0-带符号的本位币金额 end) as 内行金额,
any_value(case when a.内行客商数量 > 1  then 明细帐.客户 else a.客户 end) as 内行客商,
any_value(case when a.内行客商数量 > 1  then 明细帐.客户描述 else a.客户描述 end) as 内行客商描述,
any_value(a.内行客商数量) as 内行客商数量,
any_value(供应商) as 供应商,
any_value(供应商描述) as 供应商描述
from 明细帐 LEFT join a on a.利润中心 = 明细帐.利润中心 and a.凭证编号 = 明细帐.凭证编号 and a.财年 = 明细帐.财年
where (a.内行客商数量 > 1 and 总账科目长文本  like '%可用存款%') or (a.内行客商数量 = 1 and 总账科目长文本 not like '%可用存款%')
GROUP by 明细帐.凭证编号, 明细帐.利润中心, 明细帐.财年,明细帐.总账科目长文本
'''

结算台账='''

with 
a as (
SELECT any_value(凭证编号) as 凭证编号, any_value(财年) as 财年,any_value(利润中心) as 利润中心,
       SUM(case when (总账科目长文本 LIKE '%进项税%') then 带符号的本位币金额 ELSE NULL END) AS 进项税
FROM 明细帐 GROUP BY 利润中心, 凭证编号, 财年),
--
b as (SELECT  any_value(凭证编号) as 凭证编号, any_value(利润中心) as 利润中心, any_value(财年) as 财年,
any_value(总账科目长文本) as 供应商类型,
any_value(合同) as 合同,
any_value(输入日期) as 输入日期,
any_value(供应商) as 供应商,any_value(供应商描述) as 供应商描述,any_value(文本) as 文本,
any_value(利润中心描述) as 利润中心描述,any_value(过帐日期) as 过帐日期,any_value(中台单据号) as 中台单据号,
0-sum(带符号的本位币金额) as 含税结算金额
FROM 明细帐
WHERE (总账科目长文本 LIKE '应付账款%款%' and 总账科目长文本 not LIKE '%暂估%') and (文本!='自动清账剩余项目')
AND ((借贷标识 = 'H' AND 反记帐 = '') OR (借贷标识 = 'S' AND 反记帐 = 'X'))  GROUP BY 利润中心, 凭证编号, 财年,供应商,合同
)
select  过帐日期,输入日期,b.凭证编号,b.利润中心,利润中心描述,供应商,供应商描述,合同,文本,中台单据号,含税结算金额,进项税
from b LEFT  JOIN a ON b.利润中心 = a.利润中心 AND b.凭证编号 = a.凭证编号 AND b.财年 = a.财年'''

应付汇总供应商='''
WITH a AS (
select 
any_value(利润中心描述) as 利润中心名称,
any_value(利润中心) as 利润中心,
any_value(供应商) as 供应商,
any_value(供应商描述) as 供应商描述,
any_value(CASE WHEN 总账科目长文本 LIKE '应付账款%' AND 总账科目长文本 NOT LIKE '%进项税%'  THEN 总账科目长文本 ELSE NULL END ) AS 业务类型,
0-sum(case when (((借贷标识 = 'H' and 反记帐 = '') or (借贷标识 = 'S' and 反记帐 = 'X') ) and 总账科目长文本 like '应付账款%' and 总账科目长文本 not like '暂估' and 文本 not like '自动清账剩余项目')then 带符号的本位币金额 else null end) as 累计结算,
sum(case when (((借贷标识 = 'S' and 反记帐 = '') or (借贷标识 = 'H' and 反记帐 = 'X') ) and 总账科目长文本 like '应付账款%' and 总账科目长文本 not like '暂估' and 文本 not like '自动清账剩余项目' )then 带符号的本位币金额 else null end) as 累计付款,
0-sum(case when (总账科目长文本 like '其他应付款%保证金%' ) then 带符号的本位币金额 else null end) as 保证金余额,
sum(case when (总账科目长文本 like '其他应收款%进项税%' or 总账科目长文本 like '应付账款%进项税%' ) then 带符号的本位币金额 else null end) as 进项税余额,
FROM 明细帐 WHERE (总账科目长文本 LIKE '%进项税%' OR 总账科目长文本 LIKE '应付账款%' OR 总账科目长文本 LIKE '其他应付款%') AND 总账科目长文本 NOT LIKE '%供应链融资%'
GROUP by  利润中心,供应商)
--
SELECT * FROM  a WHERE 累计结算>0.001 OR 累计结算<-0.001'''

应付汇总合同='''
select 
any_value(利润中心描述) as 项目名称,
any_value(利润中心) as 利润中心,
any_value(供应商) as 供应商,
any_value(供应商描述) as 供应商描述,
any_value(总账科目长文本) AS 业务类型,
any_value(合同) as 合同编号,
any_value(合同文本描述) as 合同文本描述,
0-sum(case when (((借贷标识 = 'H' and 反记帐 = '') or (借贷标识 = 'S' and 反记帐 = 'X') ) and 总账科目长文本 like '应付账款%' and 总账科目长文本 not like '暂估' and 文本 not like '自动清账剩余项目')then 带符号的本位币金额 else null end) as 累计结算,
sum(case when (((借贷标识 = 'S' and 反记帐 = '') or (借贷标识 = 'H' and 反记帐 = 'X') ) and 总账科目长文本 like '应付账款%' and 总账科目长文本 not like '暂估' and 文本 not like '自动清账剩余项目' )then 带符号的本位币金额 else null end) as 累计付款
FROM 明细帐 WHERE 总账科目长文本 LIKE '应付账款%' AND 总账科目长文本 NOT LIKE '%供应链%' AND 总账科目长文本 NOT LIKE '%进项%'
GROUP by  利润中心,供应商,合同'''

专项储备='''
with 
a as (
SELECT any_value(凭证编号) as 凭证编号, 
       any_value(财年) as 财年,
       any_value(利润中心) as 利润中心,
       any_value(利润中心描述) as 利润中心描述,
       any_value(文本) as 文本,
       any_value(过帐日期) as 过帐日期,
       any_value(输入日期) as 输入日期,
       SUM(case when 总账科目长文本 like '专项储备\安全生产费\发生数' then 带符号的本位币金额 else null end) AS 安全生产费,
       any_value(case when 总账科目长文本 like '应付账款%劳务%' or 总账科目长文本 like '应付账款%分包%' then '分包结算安全费' else null end ) AS 类型
FROM 明细帐 
GROUP BY 利润中心,凭证编号,财年)
--
SELECT * FROM a where 安全生产费 > 0.001 or 安全生产费 < -0.001'''

一体化分供='''
select 
any_value(项目名称||合同编号) as 查找主键,
any_value(组织机构名称) as 组织机构名称,
any_value(项目名称) as 项目名称,
any_value(项目编号) as 项目编号,
any_value(合同名称) as 合同名称,
any_value(合同编号) as 合同编号,
any_value(原合同编号) as 原合同编号,
any_value(合同业务内容) as 合同业务内容,
any_value(客商名称) as 客商名称,
any_value(客商编号) as 客商编号,
any_value(合同类型) as 合同类型,
sum("合同金额(含税)") as 合同金额,
any_value(税率) as 税率,
sum("累计已结算金额(含税)") as 结算金额,
sum("累计已预收/预付金额") as 预付金额,
sum("累计已收/付金额(含税)") as 已付金额,
sum("累计发票金额(含税)") as 发票金额,
max("约定付款比例")as 付款比例,
(结算金额-预付金额-已付金额) as 应付余额,
(结算金额*付款比例-已付金额) as 拖欠款
from df
GROUP by  项目名称,合同编号'''