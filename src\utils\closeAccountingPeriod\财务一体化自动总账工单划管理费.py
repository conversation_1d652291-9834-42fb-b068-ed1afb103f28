
import time
import src.utils.Browser.Browser as browser
import src.utils.Excel.excel as excel
import src.utils.cscec as cscec
def label_question(s):
    s="//label[contains(text(),'"+s+"')]/parent::div/following-sibling::div[1]/div/div/div"
    return s
def autofill():
    wb=excel.myBook()
    ws=wb.sheet("管理费划转")
    b=int(ws.Cells(1,5).Value)+4
    page=browser.myBrowser("cscec").page
    for i in range(b,ws.MaxRow+1):
        cscec.changeProjectCscec(page,ws.Cells(i,2).Value,ws.Cells(i,4).Value)  
        cscec.toFunction(page,"报账系统","总账业务","总账通用工单")
        page.get_by_placeholder("事由不能超过").fill(ws.Cells(i,5).Value) 
        s="//div[contains(text(),'科目编号')]//parent::span/parent::div/parent::td/preceding-sibling::td[2]" #通过科目编号找到增加按钮
        page.locator(s).click()
        cscec.getVisible(page,"//*[text()='增加']").click()
        
        s="//div[contains(text(),'科目编号')]//parent::span/parent::div/parent::td/preceding-sibling::td[2]" #通过科目编号找到增加按钮
        page.locator(s).click()
        cscec.getVisible(page,"//*[text()='增加']").click()

        s="//div[contains(text(),'科目编号')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tr[1]/td[3]"
        page.locator(s).dblclick()
        time.sleep(0.05)
        cscec.clickMouse(page,s,95,50) #点击问号
        page.get_by_placeholder("请输入查询关键字").fill("7001990000")
        page.locator("//*[text()='帮助字典']/parent::div/parent::div/parent::div//span[contains(text(),'查询')]/parent::div/parent::div").click()
        page.get_by_text("7001990000",exact=True).first.dblclick() #第一个内部往来其他
        s="//div[contains(text(),'科目编号')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tr[1]/td[5]"
        page.locator(s).dblclick()
        cscec.clickMouse(page,s,95,50) #点击下拉按钮
        page.get_by_text("贷方",exact=True).click()
    

        s="//div[contains(text(),'科目编号')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tr[2]/td[3]"
        page.locator(s).dblclick()
        time.sleep(0.05)
        cscec.clickMouse(page,s,95,50) #点击问号
        page.get_by_placeholder("请输入查询关键字").fill("5400010641")
        page.locator("//*[text()='帮助字典']/parent::div/parent::div/parent::div//span[contains(text(),'查询')]/parent::div/parent::div").click()
        page.get_by_text("5400010641",exact=True).first.dblclick() #第二现场维护费
        s="//div[contains(text(),'科目编号')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tr[2]/td[5]"
        page.locator(s).click()
        cscec.clickMouse(page,s,95,50) #点击下拉按钮
        page.get_by_text("借方",exact=True).dblclick()

        #完成科目选择分界线
        s="//div[contains(text(),'科目编号')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tr[2]/td[4]"
        page.locator(s).dblclick()
        s="//div[contains(text(),'科目编号')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tr[1]/td[4]"
        page.locator(s).dblclick()
        s="//div[contains(text(),'客商')]//parent::span/parent::div/parent::td/preceding-sibling::td[2]" #通过科目编号找到增加按钮
        page.locator(s).click()
        cscec.getVisible(page,"//*[text()='增加']").click()
        
        s="//div[contains(text(),'客商')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tbody[2]/tr[1]/td[8]"
        page.locator(s).dblclick()
        cscec.clickMouse(page,s,95,50) #点击问号
        page.get_by_role("textbox", name="请输入客商编号或客商名称").fill(ws.Cells(i,6).Value)
        time.sleep(0.5)
        page.locator("//*[text()='客商帮助']/parent::div/parent::div/parent::div//img[contains(@src,'query_up')]/parent::div").click()
        page.locator("//*[text()='客商帮助']/parent::div/parent::div/parent::div//img[contains(@src,'query_up')]/parent::div").click()
        page.get_by_text(ws.Cells(i,6).Value,exact=True).first.dblclick() #选择客商

        s="//div[contains(text(),'客商')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tbody[2]/tr[1]/td[38]"
        page.locator(s).dblclick()
        cscec.getInputAbove(page,s).fill(str(ws.Cells(i,8).Value))

        #分界线完成客商选择及内部往来金额

        s="//div[contains(text(),'科目编号')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tr[1]/td[4]"
        page.locator(s).dblclick()
        s="//div[contains(text(),'科目编号')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tr[2]/td[4]"
        page.locator(s).dblclick()
        time.sleep(1)
        s="//div[contains(text(),'项目')]//parent::span/parent::div/parent::td/preceding-sibling::td[2]" #通过科目编号找到增加按钮
        page.locator(s).click()
        cscec.getVisible(page,"//*[text()='增加']").click()

        s="//div[contains(text(),'项目')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tbody[2]/tr[1]/td[38]"
        page.locator(s).dblclick()
        cscec.getInputAbove(page,s).fill(str(ws.Cells(i,8).Value))
        s="//div[contains(text(),'项目')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tbody[2]/tr[1]/td[1]"
        page.locator(s).dblclick() #此处为回点一下，保存金额
        #完成项目现场维护费金额填写

        page.locator("//input[@placeholder='事由不能超过50字']").click()
        page.keyboard.type(ws.Cells(i,5).Value) 
        page.get_by_text("业务部门：").first.click()

        page.locator("//span[text()='保存']/parent::div/parent::div/parent::div/parent::div").click()
        page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='确定']").click()
        page.locator("//span[text()='保存']/parent::div/parent::div/parent::div/parent::div/parent::div//span[text()='提交']/parent::div/parent::div/parent::div/parent::div").click() #通过保存来确定提交按钮
        #page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='提交']").click()
        page.locator("//*[text()='处理意见']/parent::div/parent::div/parent::div//div[text()='确定']").click()
        page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='确定']").click()
        
        page.locator("//button[@aria-label='remove']//span[@aria-label='close']//*[name()='svg']//*[name()='path' and contains(@d,'M563.8 512')]").click()
        ws.Cells(1,5).Value=ws.Cells(1,5).Value+1
