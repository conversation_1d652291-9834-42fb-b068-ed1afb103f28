-- Active: 1745400258741@@127.0.0.1@3306
with 
a as (
select 
any_value(利润中心组名称) as 利润中心组名称,
any_value(利润中心名称) as 利润中心名称,
any_value(WBS元素描述) as 项目名称,
any_value(WBS元素) as WBS元素,
any_value(利润中心) as 利润中心,
round(sum(case when 科目余额表第二期.总账科目长文本 like '主营业务收入%'  then 0-期末余额 else 0 end),4) as 收入,
round(sum(case when 科目余额表第二期.总账科目长文本 like '主营业务成本%'  then 期末余额 else 0 end),4) as 成本,
round(sum(case when 科目余额表第二期.总账科目长文本 like '主营业务收入%'  then 0-期初金额 else 0 end),4) as 年初收入,
round(sum(case when 科目余额表第二期.总账科目长文本 like '主营业务成本%'  then 期初金额 else 0 end),4) as 年初成本,
round(sum(case when 科目余额表第二期.总账科目长文本 like '专项储备%'  then 期末余额 else 0 end),4) as 专项储备余额,
round(sum(case when 科目余额表第二期.总账科目长文本 like '%合同结算%' or 科目余额表第二期.总账科目长文本 like '合同资产\工程款（已完工未结算）' or 科目余额表第二期.总账科目长文本 like '%已结算未完工%' then 期末余额 else 0 end),4) as 合同余额,
round(sum(case when 科目余额表第二期.总账科目长文本 like '合同履约成本%' and 科目余额表第二期.总账科目长文本 not like '合同履约成本%结转%' then 期末余额 else 0 end),4) as 合同履约成本余额,
round(sum(case when 科目余额表第二期.总账科目长文本 like '%预计负债\亏损合同%'  then 期末余额 else 0 end),4) as 预计负债亏损合同,
round(sum(case when 科目余额表第二期.总账科目长文本 like '原材料%'  then 期末余额 else 0 end),4) as 原材料,
round(sum(case when 科目余额表第二期.总账科目长文本 like '合同履约成本%'  then 期末余额 else 0 end),4) as 成本余额,
round(sum(case when 科目余额表第二期.总账科目长文本 like '应付账款%暂估%'  then 期末余额 else 0 end),4) as 暂估应付余额,
round(sum(case when 科目余额表第二期.总账科目长文本 like '研发支出%'  then 期末余额 else 0 end),4) as 研发支出,
round(sum(case when 科目余额表第二期.总账科目长文本 like '内部存款\非货币交易'  then 期末余额 else 0 end),4) as 本利润非货币交易,
round(sum(case when 科目余额表第二期.总账科目长文本 like '%可用存款%' or 科目余额表第二期.总账科目长文本 like '银行存款%'  then 期末余额 else 0 end),4) as 原始存量,
round(sum(case when 科目余额表第二期.总账科目长文本 like '%内部往来\内部借贷%'  then 期末余额 else 0 end),4) as 内部借款,
round(sum(case when 科目余额表第二期.总账科目长文本 like '应付账款\应付供应链融资款%'  then 期末余额 else 0 end),4) as 保理借款,
round(sum(case when 科目余额表第二期.总账科目长文本 like '%内部往来\其他' and 客户名称 in ('总部客商名称') then 期末余额 else 0 end),4) as 内部往来挂总部,
round(sum(case when 科目余额表第二期.总账科目长文本 like '%内部往来\其他' and 客户名称 in ('总部客商名称') then 期初金额 else 0 end),4) as 期初内部往来挂总部,
round(sum(case when 科目余额表第二期.总账科目长文本 like '%现场维护费'then 期末余额 else 0 end),4) as 现场维护费,
round(sum(case when 科目余额表第二期.总账科目长文本 like '%内部往来\其他' then 期末余额 else 0 end),4) - 现场维护费 as 内部往来挂经理部,
内部往来挂经理部+现场维护费 as 内部往来需调整,
round(sum(case when (科目余额表第二期.总账科目长文本 like '应收账款%' or 科目余额表第二期.总账科目长文本 like '合同资产%质保金') and 科目余额表第二期.总账科目长文本 not like '%税%'  then 本年累计借方金额 else 0 end),4) as 本年确权额,
round(sum(case when (科目余额表第二期.总账科目长文本 like '应收账款%' or 科目余额表第二期.总账科目长文本 like '合同资产%质保金') and 科目余额表第二期.总账科目长文本 not like '%税%'  then 本年累计贷方金额 else 0 end),4) as 本年收款额,
round(sum(case when 科目余额表第二期.总账科目长文本 like '应付账款%' and 科目余额表第二期.总账科目长文本 like '应付账款%供应链%'  and 科目余额表第二期.总账科目长文本 not like '%税%'  then 本年累计贷方金额 else 0 end),4) as 本年分供结算,
round(sum(case when 科目余额表第二期.总账科目长文本 like '应付账款%' and 科目余额表第二期.总账科目长文本 like '应付账款%供应链%'  and 科目余额表第二期.总账科目长文本 not like '%税%'  then 本年累计借方金额 else 0 end),4) as 本年分供付款,
sum(case when 科目余额表第二期.总账科目长文本 like '%分包工程支出%' then 期末余额 else 0 end) as 累计分包工程支出,
sum(case when 科目余额表第二期.总账科目长文本 like '%直接人工费%' then 期末余额 else 0 end) as 累计直接人工费,
sum(case when 科目余额表第二期.总账科目长文本 like '%直接材料费%' then 期末余额 else 0 end) as 累计直接材料费,
sum(case when 科目余额表第二期.总账科目长文本 like '%机械使用费%' then 期末余额 else 0 end) as 累计机械使用费,
sum(case when 科目余额表第二期.总账科目长文本 like '%其他直接费用%' then 期末余额 else 0 end) as 累计其他直接费用,
round(sum(case when 科目余额表第二期.总账科目长文本 like '%可用存款%' or 科目余额表第二期.总账科目长文本 like '银行存款%'  then 期初金额 else 0 end),4) as 期初原始存量,
round(sum(case when 科目余额表第二期.总账科目长文本 like '%内部往来\内部借贷%'  then 期初金额 else 0 end),4) as 期初内部借款,
round(sum(case when 科目余额表第二期.总账科目长文本 like '应付账款\应付供应链融资款%'  then 期初金额 else 0 end),4) as 期初保理借款,
0-sum(case when 科目分类2 like '%利润%' then 期末余额 else 0 end) as 未含本期毛利累计利润,
0-sum(case when 科目分类2 like '%利润%'  then 期初金额 else 0 end) as 年初累计利润
from 科目余额表第二期
LEFT JOIN 科目对照 on 科目余额表第二期.总账科目长文本=科目对照.总账科目长文本
WHERE WBS元素 != '' and WBS元素 not like 'QCQH%'
GROUP by  利润中心,WBS元素),
b as (
select
any_value(WBS元素) as WBS元素,
any_value(利润中心) as 利润中心,
round(sum(case when (总账科目长文本 like '应收账款%' or 总账科目长文本 like '合同资产%质保金') and 总账科目长文本 not like '%税%'  then 本年累计借方金额 else 0 end),4) as 本年确权额,
round(sum(case when (总账科目长文本 like '应收账款%' or 总账科目长文本 like '合同资产%质保金') and 总账科目长文本 not like '%税%'  then 本年累计贷方金额 else 0 end),4) as 本年收款额,
round(sum(case when 总账科目长文本 like '应付账款%' and 总账科目长文本 like '应付账款%供应链%'  and 总账科目长文本 not like '%税%'  then 本年累计贷方金额 else 0 end),4) as 本年分供结算,
round(sum(case when 总账科目长文本 like '应付账款%' and 总账科目长文本 like '应付账款%供应链%'  and 总账科目长文本 not like '%税%'  then 本年累计借方金额 else 0 end),4) as 本年分供付款
from 科目余额表第一期
WHERE WBS元素 != '' and WBS元素 not like 'QCQH%'
GROUP by  利润中心,WBS元素
),
c as (
select 
concat(a.利润中心,a.WBS元素) as 定位符,
利润中心组名称,利润中心名称,项目名称,a.WBS元素,a.利润中心,收入,成本,年初收入,年初成本,专项储备余额,合同余额,合同履约成本余额,预计负债亏损合同,原材料,成本余额,暂估应付余额,研发支出,本利润非货币交易,原始存量,内部借款,保理借款,
内部往来挂总部,
内部往来挂经理部,
现场维护费,
内部往来需调整,
a.本年确权额+b.本年确权额 as 累计确权,
a.本年收款额+b.本年收款额 as 累计收款,
a.本年分供结算+b.本年分供结算 as 累计分供结算,
a.本年分供付款+b.本年分供付款 as 累计分供付款,
累计分包工程支出,
累计直接人工费,
累计直接材料费,
累计机械使用费,
累计其他直接费用,
期初原始存量,
期初内部借款,期初保理借款,
期初原始存量+期初内部借款+期初保理借款+期初内部往来挂总部 as 期初快速资金存量,
原始存量+内部借款+保理借款+内部往来挂总部 as 快速资金存量,
累计收款 as 开累资金流入,
累计收款-快速资金存量 as 开累资金流出,
a.本年收款额 as 本期收款,
期初快速资金存量+本期收款-快速资金存量 as 本期流出,
未含本期毛利累计利润,
年初累计利润
from a left JOIN b on a.WBS元素 = b.WBS元素 and a.利润中心 = b.利润中心
)
select * from c