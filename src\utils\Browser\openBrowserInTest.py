
import sys
import os
from playwright.sync_api import Playwright, sync_playwright
import json
import src.utils.cscec as cscec
import time
import src.base.settings as settings
thePath=settings.PATH_INTERNAL

def openDebugModeBrowser(s0=None,s1=None,s2=None,s3=None):
    try:
        with sync_playwright() as playwright:
            browser = playwright.chromium.connect_over_cdp("http://localhost:9222")
            default_context = browser.contexts[0]
            page = cscec.switch_to_page(default_context,"中国建筑司库一体化")
            
    except:
        import subprocess
        chromeScript="start Chrome --remote-debugging-port=9222 --remote-allow-origins=*  --user-data-dir=C:\chromeDataForUser "
        subprocess.call(chromeScript,shell=True)
        if s0=="edge":
            subprocess.call(thePath+"/cache/RunEdge9222.bat",shell=True)
        else:
            subprocess.call(chromeScript,shell=True)
        time.sleep(5)
        with sync_playwright() as playwright:
            browser = playwright.chromium.connect_over_cdp("http://localhost:9222")
            default_context = browser.contexts[0]
            page=default_context.new_page()
            page.goto(f"https://iam.cscec.com/cas/login?service=https%3A%2F%2Ffip.cscec.com%2FOSPPortal%2Fcallback")
            page.locator("//label[text()='我的单据']").click(timeout=360000) #通过六分钟等待
    return page

