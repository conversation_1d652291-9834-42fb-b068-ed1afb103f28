import os
import sys

sys.path.append(".")
import src.utils.Excel.excel as excel

import os
from pathlib import Path
from python_calamine import CalamineWorkbook

def getIndexByTitle(inputList,title):

    if type(title)==str:
        for i in range(3):
            for j in range(len(inputList[i])):
                if inputList[i][j]==title:
                    return j
    else:
        for i in range(len(title)):
            if i==0:
                for j in range(len(inputList[i])):
                    if inputList[i][j]==title[i]:
                        nextLoop = j
            else:
                for j in range(nextLoop,len(inputList[i])):
                    if inputList[i][j]==title[i]:
                        nextLoop = j
                        break
        return nextLoop
def getDataByName(inputList,title,rowName):
    col=getIndexByTitle(inputList,title)
    for i in range(len(inputList)):
        if inputList[i][0]==rowName:
            row=i
    return inputList[row][col]
            
def get_cell_reference(data,cell_reference):
    # 将类似"A1"的单元格引用转换为行和列的索引
    col_part = ''.join(filter(str.isalpha, cell_reference))
    row_part = ''.join(filter(str.isdigit, cell_reference))
    
    col = 0
    for i, char in enumerate(col_part[::-1]):
        col += (ord(char.upper()) - ord('A') + 1) * (26 ** i)
    
    row = int(row_part) - 1
    col = col - 1
    if data[row][col] != "":
        return data[row][col]
    else:
        return 0
def get_excel_data(folder_path, sheet_name):
    # 获取文件夹下所有Excel文件
    excel_files = [f for f in os.listdir(folder_path) if f.endswith('.xlsx') or f.endswith('.xls')]
    
    result = []
    
    for file in excel_files:
        file_path = os.path.join(folder_path, file)
        workbook = CalamineWorkbook.from_path(file_path)
        sheet = workbook.get_sheet_by_name(sheet_name)
        
        if sheet:
            # 将单元格引用转换为行和列的索引
            data = sheet.to_python()
            value1 = get_cell_reference(data,"T6")
            value2 = get_cell_reference(data,"T7") +get_cell_reference(data,"T11")
            value3 = get_cell_reference(data,"ad7")+get_cell_reference(data,"ad11")
            listValue=[value1,value2,value3]
            file_name = Path(file).stem  # 获取不含后缀的文件名
            listValue.insert(0,file_name)
            result.append(listValue)
    
    return result

# 示例调用
def example():
    folder_path = "D:\数字公司总部\报表部分\集团账套23全\子表"  # 指定文件夹路径
    getdata = get_excel_data(folder_path, "ZC2006 应收账款")
    theOutputSheet=excel.myBook("输出表").sheet("应收账款表")
    theOutputSheet.Range("a2").resize(len(getdata),len(getdata[0])).Value=getdata

def test_get_title():
    workbook = CalamineWorkbook.from_path("D:\数字公司总部\报表部分\财务中报--年报0218\中建三局安装工程有限公司-母公司.xlsx")
    theList = workbook.get_sheet_by_name("NBWL2004 内部往来-应收账款").to_python()
    print(getIndexByTitle(theList,("原值","期末账龄","2-3年")))

test_get_title()