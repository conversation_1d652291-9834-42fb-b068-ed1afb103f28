with a as(
select 
any_value(利润中心描述) as 利润中心描述,
any_value(利润中心) as 利润中心,
sum(case when 总账科目长文本 like '主营业务收入%'  then 0-带符号的本位币金额 else 0 end) as 收入,
sum(case when 总账科目长文本 like '主营业务成本%'  then 带符号的本位币金额 else 0 end) as 成本,
sum(case when 总账科目长文本 like '专项储备%'  then 带符号的本位币金额 else 0 end) as 专项储备余额,
sum(case when 总账科目长文本 like '%合同结算%' or 总账科目长文本 like '合同资产\工程款（已完工未结算）' or 总账科目长文本 like '%已结算未完工%' then 带符号的本位币金额 else 0 end) as 合同余额,
sum(case when 总账科目长文本 like '合同履约成本%' and 总账科目长文本 not like '合同履约成本%结转%' then 带符号的本位币金额 else 0 end) as 合同履约成本余额,
sum(case when 总账科目长文本 like '%预计负债\亏损合同%'  then 带符号的本位币金额 else 0 end) as 预计负债亏损合同,
sum(case when 总账科目长文本 like '原材料%'  then 带符号的本位币金额 else 0 end) as 原材料,
sum(case when 总账科目长文本 like '合同履约成本%'  then 带符号的本位币金额 else 0 end) as 成本余额,
sum(case when 总账科目长文本 like '应付账款%暂估%'  then 带符号的本位币金额 else 0 end) as 暂估应付余额,
sum(case when 总账科目长文本 like '研发支出%'  then 带符号的本位币金额 else 0 end) as 研发支出,
sum(case when 总账科目长文本 like '内部存款\非货币交易'  then 带符号的本位币金额 else 0 end) as 本利润非货币交易未平,
sum(case when 总账科目长文本 like '%可用存款%' or 总账科目长文本 like '银行存款%'  then 带符号的本位币金额 else 0 end) as 原始存量,
sum(case when 总账科目长文本 like '%内部往来\内部借贷%'  then 带符号的本位币金额 else 0 end) as 内部借款,
sum(case when 总账科目长文本 like '应付账款\应付供应链融资款'  then 带符号的本位币金额 else 0 end) as 保理借款,
sum(case when (总账科目长文本 like '%内部往来\其他' or 总账科目长文本 like '%内部往来\代垫费用')  and (客户描述 in ('总部客商名称')) then 带符号的本位币金额 else 0 end) as 内部往来挂总部,
sum(case when (总账科目长文本 like '%内部往来\其他' or 总账科目长文本 like '%内部往来\代垫费用') then 带符号的本位币金额 else 0 end) - 内部往来挂总部 as 内部往来挂经理部,
sum(case when 总账科目长文本 like '机关划转费用科目'then 带符号的本位币金额 else 0 end) as 现场维护费,
内部往来挂经理部+现场维护费 as 内部往来需调整,
sum(case when 总账科目长文本 like '%待转销%'then 带符号的本位币金额 else 0 end) as 销项税余额,
sum(case when ((总账科目长文本 like '应收账款%进度%' or 总账科目长文本 like '合同资产%质保金%') and 文本 not like '%自动清账%' and  ((借贷标识 = 'S' AND 反记帐 = '') OR (借贷标识 = 'H' AND 反记帐 = 'X'))) then 带符号的本位币金额 else 0 end) as 累计确权,
0-sum(case when ((总账科目长文本 like '应收账款%进度%' or 总账科目长文本 like '合同资产%质保金%') and 文本 not like '%自动清账%' and  ((借贷标识 = 'H' AND 反记帐 = '') OR (借贷标识 = 'S' AND 反记帐 = 'X'))) then 带符号的本位币金额 else 0 end) as 累计收款,
0-sum(case when ((总账科目长文本 like '应付账款%' and 总账科目长文本 not like '%税%' and 总账科目长文本 not like '%暂估%') and 文本 not like '%自动清账%' and  ((借贷标识 = 'H' AND 反记帐 = '') OR (借贷标识 = 'S' AND 反记帐 = 'X'))) then 带符号的本位币金额 else 0 end) as 累计分供结算,
sum(case when ((总账科目长文本 like '应付账款%' and 总账科目长文本 not like '%税%' and 总账科目长文本 not like '%暂估%') and 文本 not like '%自动清账%' and  ((借贷标识 = 'S' AND 反记帐 = '') OR (借贷标识 = 'H' AND 反记帐 = 'X'))) then 带符号的本位币金额 else 0 end) as 累计分供付款
FROM 明细帐
GROUP by  利润中心),
b as (
select sum(case when 成本类别 == '商务间接费用'  then 入账成本 else 0 end) as 商务间接费用,
sum(case when 成本类别 == '商务机械费用'  then 入账成本 else 0 end) as 商务机械费用,
sum(case when 成本类别 == '商务直接税金' then 入账成本 else 0 end) as 附加税,
any_value(利润中心) AS 利润中心
from 成本表 GROUP by 利润中心),
c as (
select sum(case when 类型 IS NULL  then 安全生产费 else 0 end) as 非分包安全费,
any_value(利润中心) AS 利润中心
from 专项储备 GROUP by 利润中心),
d as (select sum(结算额) as 总包结算额,
sum(付款额) as 总包付款额,
sum(暂估额) as 总包暂估额,
any_value(利润中心) AS 利润中心
from 内部对账
GROUP by 利润中心)
--
select a.*,b.商务间接费用,b.商务机械费用,b.附加税,c.非分包安全费,d.总包结算额,d.总包付款额,d.总包暂估额 from a left join b on a.利润中心 = b.利润中心 left join c on a.利润中心 = c.利润中心 left join d on a.利润中心 = d.利润中心
