
import src.utils.cscec as cscec
from playwright.sync_api import Page
import fitz
import time
import os
import src.base.settings as settings

def pdf_wirte(s,text):
    with fitz.open(s) as doc:
        for page in doc:
                try:
                    font_size = 16
                    x = 20
                    y = 20
                    page.insert_text(fitz.Point(x, y), text, fontname='helv', fontsize=font_size)
                except:
                    page
        doc.saveIncr()

def firstDownloadAttachment(page:Page,savePath): #第一类附件下载
    page.locator("//span[text()='附件']/parent::div/parent::div/parent::div/parent::div").click()
    table2=page.locator("//div[contains(text(),'文件大小')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table/tbody[2]//tr")
    if table2.nth(0).locator("//td[1]").text_content()=="1":
        for j in range(table2.count()):
            table2.nth(j).locator("//td[1]").click()
            time.sleep(0.5)
            filename=table2.nth(j).locator("//td[2]/div").text_content()
            name, ext = os.path.splitext(filename)
            with page.expect_download() as download_info:
                page.locator("//*[text()='附件窗口']/parent::div/parent::div/parent::div//span[contains(text(),'下载')]").dblclick()
                download = download_info.value
                path = download.path()
                download.save_as(path=savePath+"计数"+str(j)+ext)
    page.locator("//*[text()='附件窗口']/preceding-sibling::div[1]//td[3]").click() #点击关闭
def secondDownloadAttachment(page:Page,savePath):  #第二类附件下载
    page.locator("//span[text()='附件']/parent::div/parent::div/parent::div/parent::div").click()
    table2=page.locator("//div[contains(text(),'文件大小')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table/tbody[2]//tr")
    if table2.nth(0).locator("//td[1]").text_content()=="1":
        for j in range(table2.count()):
            table2.nth(j).locator("//td[1]").click()
            time.sleep(0.5)
            filename=table2.nth(j).locator("//td[2]/div").text_content()
            name, ext = os.path.splitext(filename)
            with page.expect_download() as download_info:
                page.locator("//*[text()='附件窗口']/parent::div/parent::div/parent::div//span[contains(text(),'下载')]").dblclick()
                download = download_info.value
                path = download.path()
                download.save_as(path=savePath+"计数"+str(j)+ext)
    page.locator("//*[text()='附件窗口']/preceding-sibling::div[1]//td[3]").click() #点击关闭  

def commonImageDownload(page:Page,savePath): #影像下载
    page.locator("//span[text()='影像']/parent::div/parent::div/parent::div/parent::div").click()
    cscec.getVisible(page,"//div[@title='下载' and @role='button']").click()
    cscec.getVisible(page,"//*[text()='影像下载']/parent::div/parent::div/parent::div//span[@class='x-editable']")
    xc=page.locator("//*[text()='影像下载']/parent::div/parent::div/parent::div//span[@class='x-editable']")
    for j in range(xc.count()):
        xc.nth(j).click()
        name, ext = os.path.splitext(xc.nth(j).locator("//preceding-sibling::div[1]//img").get_attribute("title"))
        with page.expect_download() as download_info:
            page.locator("//*[text()='影像下载']/parent::div/parent::div/parent::div//span[contains(text(),'下载')]").dblclick()
            download = download_info.value
            path = download.path()
            download.save_as(path=savePath+"计数"+str(j)+ext)
    page.locator("//*[text()='影像下载']/preceding-sibling::div[1]//td[1]").click() #点击关闭
    page.locator("//div[@class='ant-tabs-tab ant-tabs-tab-with-remove ant-tabs-tab-active']//span[@aria-label='close']//*[name()='svg']//*[name()='path' and contains(@d,'M563.8 512')]").click() #点击关闭

def invoiceImageDownload(page:Page,savePath): #收票单影像下载
    page.locator("//span[text()='影像']/parent::div/parent::div/parent::div/parent::div").click()
    #page.locator("//*[text()='票据影像']/parent::div/parent::div/parent::div//div[@title='下载' and @role='button']").click()
    try:
        invoiceState=page.locator("//label[contains(text(),'发票状态')]/parent::div/following-sibling::div[1]//input").get_attribute("value")#这一块都是等待作用
    except:
        invoiceState=None
    k=1200
    while invoiceState!="已验真" and k>0:
        time.sleep(0.05)
        try:
            invoiceState=page.locator("//label[contains(text(),'发票状态')]/parent::div/following-sibling::div[1]//input").get_attribute("value")#这一块都是等待作用
            k=-1
        except:
            k=k-1
    cscec.getVisible(page,"//div[@title='下载' and @role='button']").click()
    cscec.getVisible(page,"//*[text()='影像下载']/parent::div/parent::div/parent::div//span[@class='x-editable']")
    xc=page.locator("//*[text()='影像下载']/parent::div/parent::div/parent::div//span[@class='x-editable']")
    for j in range(xc.count()):
        xc.nth(j).click()
        name, ext = os.path.splitext(xc.nth(j).locator("//preceding-sibling::div[1]//img").get_attribute("title"))
        with page.expect_download() as download_info:
            page.locator("//*[text()='影像下载']/parent::div/parent::div/parent::div//span[contains(text(),'下载')]").dblclick()
            download = download_info.value
            path = download.path()
            download.save_as(path=savePath+"计数"+str(j)+ext)
    page.locator("//*[text()='影像下载']/preceding-sibling::div[1]//td[1]").click() #点击关闭
    page.locator("//*[text()='票据影像']/preceding-sibling::div[1]//td[1]").click() #点击关闭

def paymentImageDownload(page:Page,savePath): #付款单影像下载
    page.locator("//span[text()='影像']/parent::div/parent::div/parent::div/parent::div").click()
    #page.locator("//*[text()='票据影像']/parent::div/parent::div/parent::div//div[@title='下载' and @role='button']").click()
    cscec.getVisible(page,"//div[@title='下载' and @role='button']").click()
    try:
        page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='确定']").click(timeout=1000)
    except:
        cscec.getVisible(page,"//*[text()='影像下载']/parent::div/parent::div/parent::div//span[@class='x-editable']")
        xc=page.locator("//*[text()='影像下载']/parent::div/parent::div/parent::div//span[@class='x-editable']")
        xc=page.locator("//*[text()='影像下载']/parent::div/parent::div/parent::div//span[@class='x-editable']")
        for j in range(xc.count()):
            xc.nth(j).click()
            name, ext = os.path.splitext(xc.nth(j).locator("//preceding-sibling::div[1]//img").get_attribute("title"))
            with page.expect_download() as download_info:
                page.locator("//*[text()='影像下载']/parent::div/parent::div/parent::div//span[contains(text(),'下载')]").dblclick()
                download = download_info.value
                path = download.path()
                download.save_as(path=savePath+"计数"+str(j)+ext)
        page.locator("//*[text()='影像下载']/preceding-sibling::div[1]//td[1]").click() #点击关闭
    page.locator("//div[@class='ant-tabs-tab ant-tabs-tab-with-remove ant-tabs-tab-active']//span[@aria-label='close']//*[name()='svg']").click()

def taxBillAttachmentDownload(page:Page,savePath): #纳税单影像下载
    page.locator("//span[text()='2‰附件']/parent::div/parent::div/parent::div/parent::div").click()
    table2=page.locator("//div[contains(text(),'文件大小')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table/tbody[2]//tr")
    if table2.nth(0).locator("//td[2]").text_content()=="1":
        for j in range(table2.count()):
            table2.nth(j).locator("//td[1]").click()
            time.sleep(0.5)
            filename=table2.nth(j).locator("//td[3]/div").text_content()
            name, ext = os.path.splitext(filename)
            with page.expect_download() as download_info:
                page.locator("//*[text()='附件窗口']/parent::div/parent::div/parent::div//span[contains(text(),'下载')]").dblclick()
                download = download_info.value
                path = download.path()
                download.save_as(path=savePath+"计数"+str(j)+ext)
            table2.nth(j).locator("//td[1]").click()
    page.locator("//*[text()='附件窗口']/preceding-sibling::div[1]//td[3]").click() #点击关闭

def flowsheetPrinting(page:Page,savePath,processCode): #流程单打印
    try:
        page.locator("//span[text()='退出']/parent::div/parent::div/parent::div/parent::div/parent::div//span[text()='打印']").click(timeout=1000)
    except:
        try:
            print("系统提示错误")
            page.locator("//*[text()='系统提示']//parent::div/parent::div/parent::div//span[text()='确 定']").click(timeout=500) #这里的狗血确认有个空格
            page.locator("//span[text()='退出']/parent::div/parent::div/parent::div/parent::div/parent::div//span[text()='打印']").click(timeout=500)
        except:
            print("不下载附件，只打印流程单")
            page.locator("//span[text()='退出']/parent::div/parent::div/parent::div/parent::div/parent::div//span[text()='打印']").click()
    #不具备独一性，利用附件按钮反向查找
    if processCode[:3]=="JFK" or processCode[:3]=="CFK" or processCode[:3]=="TBZ":
        page.locator("//*[text()='模板选择窗口']/parent::div/parent::div/parent::div//span[text()='确定']").click()
    frame=page.frame_locator("//*[@id='FormPdfJsPage1']")
    tryCount=60
    while tryCount>0:
        try:
            with page.expect_download(timeout=1000) as download_info:
                frame.locator("//button[@id='download']").click()
                download = download_info.value
                path = download.path()
                download.save_as(path=savePath+"a流程单.pdf")
                tryCount=-1
        except:
            tryCount=tryCount-1
            time.sleep(0.3)  
    page.locator("//div[contains(@class,'ant-tabs-tab ant-tabs-tab-with-remove ant-tabs-tab-active')]//span[contains(@aria-label,'close')]//*[name()='svg']").click()
    page.locator("//div[@class='ant-tabs-tab ant-tabs-tab-with-remove ant-tabs-tab-active']//span[@aria-label='close']//*[name()='svg']").click()  

def download(page:Page,processCode,path,reasons=None):
    if "代付" in reasons:
        reasons="代付" 
    else:
        reasons=""
    if processCode[:3]=="JFK" or processCode[:3]=="ZGD" or processCode[:3]=="NBD" or processCode[:2]=="WS" or processCode[:3]=="CZD" or processCode[:3]=="CFK"   :#附件下载1
        firstDownloadAttachment(page,path+processCode+reasons)
    if processCode[:3]=="TBX" or processCode[:3]=="CBX" or processCode[:3]=="JSQ" or processCode[:3]=="JSD" or processCode[:3]=="TBZ":#附件下载2
        secondDownloadAttachment(page,path+processCode+reasons)
    if processCode[:3]=="TBX" or processCode[:3]=="CBX" or processCode[:3]=="CFK" :#影像下载
        commonImageDownload(page,path+processCode+reasons)
    if processCode[:2]=="SP" :  #收票单影像下载
        invoiceImageDownload(page,path+processCode+reasons)
    if processCode[:3]=="JFK" : #和TBX有区别
        paymentImageDownload(page,path+processCode+reasons)
    if processCode[:2]=="WS":
        taxBillAttachmentDownload(page,path+processCode)
    flowsheetPrinting(page,path+processCode,processCode)
          


